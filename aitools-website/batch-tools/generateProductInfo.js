const fs = require('fs/promises');
const path = require('path');
const axios = require('axios');
const { MongoClient } = require('mongodb');

// 配置项
const INPUT_FILE = 'urls.txt';  // 每行一个 mental model 名
// const OUTPUT_DIR = './output';           // 输出文件夹
const OUTPUT_DIR = "/Users/<USER>/workspace/funblocks-docs/tools/";
const API_ENDPOINT = 'http://localhost:50058/ai/generateProductInfo'; // 替换为实际 API 地址

// 请求 Web API（可自定义 body、headers 等）
async function fetchContent(url) {
    try {
        const response = await axios.post(API_ENDPOINT, { data: { url } });
        return response.data.data || '';  // 根据实际 API 响应结构修改
    } catch (err) {
        console.error(`❌ 请求失败: ${url}`);
        return '（请求失败，暂无内容）';
    }
}

// 主程序
async function main() {
    // 确保输出目录存在
    await fs.mkdir(OUTPUT_DIR, { recursive: true });

    // 读取并处理 mental models
    const rawText = await fs.readFile(INPUT_FILE, 'utf-8');
    const links = [...new Set(rawText
        .split('\n')
        .map(line => line.trim())
        .filter(Boolean)
        .map(line => line.split("（")[0])
    )];

    // console.log('models...........', models);
    links.map(m => console.log(m))

    const uri = 'mongodb://localhost:27017';
    const client = new MongoClient(uri);
    await client.connect();

    const db = client.db('aitools');
    const collection = db.collection('tools');

    let jsons = []
    for (let i = 0; i < links.length; i++) {
        const link = links[i];
        let response = await fetchContent(link);

        let toolInfo = response?.generated?.productInfo;
        // console.log('response.......', toolInfo)

        toolInfo.website = link;
        toolInfo.pricing = 'freemium';
        toolInfo.submittedBy = '686b6e3dd8b787c2d395adbb';
        toolInfo.submittedAt = new Date();
        toolInfo.launchOption = 'paid';
        toolInfo.paymentStatus = 'completed';
        toolInfo.paymentAmount = 1;
        toolInfo.selectedLaunchDate = new Date();
        toolInfo.launchDate = new Date();
        toolInfo.status = 'pending';

        // jsons.push(toolInfo)

        const result = await collection.insertOne(toolInfo);

        console.log(`✅ 写入: ${link}`, result);
    }

    await client.close();
    // const filename = 'tools.json';

    // const outputPath = path.join(OUTPUT_DIR, filename);


    // await fs.writeFile(outputPath, JSON.stringify(jsons), 'utf-8');
    console.log(`✅ 完成`);


}

main().catch(console.error);
