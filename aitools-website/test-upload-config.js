// 简单测试脚本来验证上传配置
const { FIELD_LIMITS, UPLOAD_CONFIG, validateFieldLength, validateFileUpload, getFullUploadPath, getFileUrl } = require('./src/constants/upload-config.ts');

console.log('=== 测试字段长度限制 ===');
console.log('FIELD_LIMITS:', FIELD_LIMITS);

console.log('\n=== 测试字段验证 ===');
// 测试工具名称验证
const nameTest1 = validateFieldLength('TOOL_NAME', 'Test Tool');
console.log('工具名称 "Test Tool":', nameTest1);

const nameTest2 = validateFieldLength('TOOL_NAME', '');
console.log('空工具名称:', nameTest2);

const nameTest3 = validateFieldLength('TOOL_NAME', 'A'.repeat(150));
console.log('超长工具名称:', nameTest3);

console.log('\n=== 测试上传配置 ===');
console.log('UPLOAD_CONFIG:', UPLOAD_CONFIG);

console.log('\n=== 测试路径生成 ===');
console.log('Logo上传路径:', getFullUploadPath('logos'));
console.log('Logo文件URL:', getFileUrl('logos', 'test.png'));

console.log('\n=== 测试文件验证 ===');
// 模拟文件对象
const mockFile = {
  name: 'test.png',
  type: 'image/png',
  size: 1024 * 1024 // 1MB
};

const fileTest = validateFileUpload(mockFile, 'logo');
console.log('PNG文件验证:', fileTest);

const mockLargeFile = {
  name: 'large.png',
  type: 'image/png',
  size: 10 * 1024 * 1024 // 10MB
};

const largeFileTest = validateFileUpload(mockLargeFile, 'logo');
console.log('大文件验证:', largeFileTest);

console.log('\n测试完成！');
