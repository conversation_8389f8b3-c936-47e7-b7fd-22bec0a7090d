import { Fragment } from 'react';
import { getServerSession } from 'next-auth/next';
import { redirect } from '@/i18n/routing';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import User from '@/models/User';
import SubmittedToolsListClient from '@/components/profile/SubmittedToolsListClient';

interface PageProps {
  params: Promise<{ locale: string }>;
}

export default async function SubmittedToolsPage({ params }: PageProps) {
  const { locale } = await params;

  // 获取服务器端session
  const session = await getServerSession(authOptions);

  // 如果未登录，重定向到首页
  if (!session?.user?.email) {
    redirect({ href: '/', locale });
  }

  // 获取用户信息
  await dbConnect();
  const user = await User.findOne({ email: session!.user!.email });
  if (!user) {
    redirect({ href: '/', locale });
  }

  // 获取用户提交的工具
  const tools = await Tool.find({
    submittedBy: user._id.toString()
  })
  .sort({ submittedAt: -1 })
  .lean();

  // 计算统计信息
  const stats = {
    total: tools.length,
    draft: tools.filter(t => t.status === 'draft').length,
    approved: tools.filter(t => t.status === 'approved').length,
    pending: tools.filter(t => t.status === 'pending').length,
    rejected: tools.filter(t => t.status === 'rejected').length,
    totalViews: tools.reduce((sum, t) => sum + (t.views || 0), 0),
    totalLikes: tools.reduce((sum, t) => sum + (t.likes || 0), 0)
  };

  // 序列化数据
  const serializedTools = tools.map(tool => ({
    ...tool,
    _id: tool._id?.toString() || '',
    submittedAt: tool.submittedAt?.toISOString() || new Date().toISOString(),
    launchDate: tool.launchDate ? tool.launchDate.toISOString() : null,
    selectedLaunchDate: tool.selectedLaunchDate ? tool.selectedLaunchDate.toISOString() : null,
    reviewedAt: tool.reviewedAt ? tool.reviewedAt.toISOString() : null,
    paidAt: tool.paidAt ? tool.paidAt.toISOString() : null,
  })) as any[];

  return (
    <Fragment>
      <SubmittedToolsListClient
        initialTools={serializedTools}
        initialStats={stats}
      />
    </Fragment>
  );
}
