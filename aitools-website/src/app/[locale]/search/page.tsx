import React from 'react';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import SearchPageClient from '@/components/search/SearchPageClient';
import { apiClient } from '@/lib/api';
import { type Locale } from '@/i18n/config';

interface SearchPageProps {
  params: Promise<{ locale: Locale }>;
  searchParams: Promise<{
    q?: string;
    page?: string;
    category?: string;
    sort?: string;
  }>;
}

// 生成动态metadata
export async function generateMetadata({ params, searchParams }: SearchPageProps): Promise<Metadata> {
  const { locale } = await params;
  const searchParamsData = await searchParams;
  const query = searchParamsData.q || '';
  const t = await getTranslations({ locale, namespace: 'search' });

  if (!query.trim()) {
    const title = t('page_title');
    const description = t('page_description');
    return {
      title,
      description,
    };
  }

  const titleWithQuery = t('page_title_with_query', { query });
  const descriptionWithQuery = t('page_description_with_query', { query });

  return {
    title: titleWithQuery,
    description: descriptionWithQuery,
    openGraph: {
      title: titleWithQuery,
      description: descriptionWithQuery,
    },
  };
}

export default async function SearchPage({ params, searchParams }: SearchPageProps) {
  const { locale } = await params;
  const searchParamsData = await searchParams;
  const query = searchParamsData.q || '';
  const page = parseInt(searchParamsData.page || '1');
  const category = searchParamsData.category || '';
  const sort = searchParamsData.sort || 'createdAt';

  // 如果没有搜索关键词，显示空状态
  if (!query.trim()) {
    return <SearchPageClient initialQuery="" initialResults={null} initialCategories={[]} locale={locale} />;
  }

  try {
    // 获取搜索结果
    const [toolsResponse, categoriesResponse] = await Promise.all([
      apiClient.getTools({
        search: query,
        page,
        limit: 12,
        category: category || undefined,
        sort,
        order: 'desc',
      }),
      apiClient.getCategories(),
    ]);

    if (!toolsResponse.success) {
      throw new Error(toolsResponse.error || '获取搜索结果失败');
    }

    if (!categoriesResponse.success) {
      throw new Error(categoriesResponse.error || '获取分类失败');
    }

    return (
      <SearchPageClient
        initialQuery={query}
        initialResults={toolsResponse.data || null}
        initialCategories={categoriesResponse.data?.categories || []}
        initialPage={page}
        initialCategory={category}
        initialSort={sort}
        locale={locale}
      />
    );
  } catch (error) {
    console.error('Search page error:', error);
    notFound();
  }
}
