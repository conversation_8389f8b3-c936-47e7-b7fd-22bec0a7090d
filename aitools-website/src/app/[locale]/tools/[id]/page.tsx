import React from 'react';
import { Link } from '@/i18n/routing';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getTranslations } from 'next-intl/server';
import ToolDetailServer from '@/components/tools/ToolDetailServer';
import { apiClient } from '@/lib/api';
import { getToolStructuredData, getBreadcrumbStructuredData } from '@/lib/seo/structuredData';
import { Locale } from '@/i18n/config';

// 生成动态metadata
export async function generateMetadata({ params }: { params: Promise<{ id: string; locale: Locale }> }): Promise<Metadata> {
  try {
    const { id, locale } = await params;
    const t = await getTranslations({ locale, namespace: 'tool_detail' });

    const response = await apiClient.getTool(id);

    if (!response.success || !response.data) {
      const siteT = await getTranslations({ locale, namespace: 'site' });
      return {
        title: `${t('not_found')} - ${siteT('title')}`,
        description: t('not_found_desc'),
      };
    }

    const tool = response.data;
    const siteT = await getTranslations({ locale, namespace: 'site' });
    const title = `${tool.name} - ${siteT('title')}`;
    const description = tool.description || `${tool.name} is an excellent AI tool to boost your productivity.`;
    const keywords = [tool.name, ...(tool.tags || []), 'AI tools', tool.category].join(', ');
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://aitools.example.com';
    const canonical = `/tools/${tool._id}`;
    const ogImage = tool.logo || '/og-tool-default.jpg';

    return {
      title,
      description,
      keywords,
      authors: [{ name: siteT('title') }],
      robots: {
        index: true,
        follow: true,
      },
      openGraph: {
        type: 'article',
        locale: locale === 'zh' ? 'zh_CN' : 'en_US',
        url: `${baseUrl}${canonical}`,
        siteName: siteT('title'),
        title,
        description,
        images: [
          {
            url: ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`,
            width: 1200,
            height: 630,
            alt: `${tool.name} - ${siteT('title')}`,
          },
        ],
        publishedTime: tool.launchDate ? new Date(tool.launchDate).toISOString() : undefined,
        modifiedTime: tool.updatedAt ? new Date(tool.updatedAt).toISOString() : undefined,
      },
      twitter: {
        card: 'summary_large_image',
        title,
        description,
        images: [ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`],
      },
      alternates: {
        canonical: `${baseUrl}${canonical}`,
      },
    };
  } catch (error: unknown) {
    const siteT = await getTranslations({ locale: 'zh', namespace: 'site' });
    const toolT = await getTranslations({ locale: 'zh', namespace: 'tool_detail' });
    return {
      title: `${toolT('page_title')} - ${siteT('title')}`,
      description: toolT('not_found_desc'),
    };
  }
}

// 服务器端渲染的工具详情页
export default async function ToolDetailPage({ params }: { params: Promise<{ id: string; locale: Locale }> }) {
  try {
    const { id, locale } = await params;
    const t = await getTranslations({ locale, namespace: 'tool_detail' });

    const response = await apiClient.getTool(id);

    if (!response.success || !response.data) {
      notFound();
    }

    const tool = response.data;

    // 检查工具是否已发布：approved状态且launchDate已过
    // const isPublished = tool.status === 'approved' && tool.launchDate && new Date(tool.launchDate) <= new Date();

    // if (!isPublished) {
    //   notFound();
    // }

    // 工具数据已经是正确的格式，无需额外序列化
    const serializedTool = tool;

    // 生成结构化数据
    const toolStructuredData = getToolStructuredData(serializedTool);
    const breadcrumbStructuredData = getBreadcrumbStructuredData([
      { name: t('breadcrumb_home'), url: '/' },
      { name: t('breadcrumb_tools'), url: '/tools' },
      { name: tool.name, url: `/tools/${tool._id}` }
    ]);

    return (
      <>
        {/* 结构化数据 */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(toolStructuredData)
          }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbStructuredData)
          }}
        />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* 面包屑导航 */}
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6" aria-label={t('breadcrumb_aria_label')}>
            <Link href="/" className="hover:text-blue-600">{t('breadcrumb_home')}</Link>
            <span>/</span>
            <Link href="/tools" className="hover:text-blue-600">{t('breadcrumb_tools')}</Link>
            <span>/</span>
            <span className="text-gray-900">{tool.name}</span>
          </nav>

          {/* 返回按钮 */}
          <div className="mb-6">
            <Link
              href="/tools"
              className="inline-flex items-center text-blue-600 hover:text-blue-700"
            >
              <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              {t('back_to_tools')}
            </Link>
          </div>

          {/* 工具详情服务端组件 */}
          <ToolDetailServer initialTool={serializedTool} toolId={id} locale={locale} />
        </div>
      </>
    );
  } catch (error) {
    console.error('Error loading tool:', error);
    notFound();
  }
}
