'use client';

import React, { useState } from 'react';
import { useRouter } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import { Tool } from '@/lib/api';
import { Locale } from '@/i18n/config';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import {
  CheckCircle,
  XCircle
} from 'lucide-react';

interface AdminToolDetailClientProps {
  tool: Tool;
  locale: Locale;
}

export default function AdminToolDetailClient({
  tool,
  locale
}: AdminToolDetailClientProps) {
  const router = useRouter();
  const t = useTranslations('admin');

  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const handleApprove = async () => {
    setIsProcessing(true);
    try {
      setError('');

      const response = await fetch(`/api/admin/tools/${tool._id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reviewedBy: 'admin', // In production, this should be the current logged-in admin
          reviewNotes: t('success.tool_approved'),
          launchDate: new Date().toISOString()
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccessMessage(t('success.tool_approved'));
        // Refresh the page to update the tool status
        router.refresh();
      } else {
        setError(data.error || t('errors.approve_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReject = async () => {
    if (!rejectReason.trim()) {
      setError(t('errors.reject_reason_required'));
      return;
    }

    setIsProcessing(true);
    try {
      setError('');

      const response = await fetch(`/api/admin/tools/${tool._id}/reject`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reviewedBy: 'admin', // In production, this should be the current logged-in admin
          rejectReason: rejectReason
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccessMessage(t('success.tool_rejected'));
        setShowRejectModal(false);
        setRejectReason('');
        // Refresh the page to update the tool status
        router.refresh();
      } else {
        setError(data.error || t('errors.reject_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <>
      {/* Success/Error Messages */}
      {successMessage && (
        <SuccessMessage
          message={successMessage}
          onClose={() => setSuccessMessage('')}
          className="mb-6"
        />
      )}

      {error && (
        <ErrorMessage
          message={error}
          onClose={() => setError('')}
          className="mb-6"
        />
      )}

      {/* Action Buttons - Only render if tool is pending */}
      {tool.status === 'pending' && (
        <div className="flex space-x-3 mb-6">
          <button
            onClick={handleApprove}
            disabled={isProcessing}
            className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            <CheckCircle className="w-4 h-4 mr-2" />
            {isProcessing ? t('actions.processing') : t('actions.approve')}
          </button>
          <button
            onClick={() => setShowRejectModal(true)}
            disabled={isProcessing}
            className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center"
          >
            <XCircle className="w-4 h-4 mr-2" />
            {t('actions.reject')}
          </button>
        </div>
      )}

      {/* Reject Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('reject_modal.title')}</h3>
            <p className="text-sm text-gray-600 mb-4">
              {t('reject_modal.description')}
            </p>
            <textarea
              value={rejectReason}
              onChange={(e) => setRejectReason(e.target.value)}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder={t('reject_modal.placeholder')}
            />
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectReason('');
                }}
                disabled={isProcessing}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                {t('actions.cancel')}
              </button>
              <button
                onClick={handleReject}
                disabled={!rejectReason.trim() || isProcessing}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {isProcessing ? t('actions.processing') : t('actions.confirm_reject')}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
