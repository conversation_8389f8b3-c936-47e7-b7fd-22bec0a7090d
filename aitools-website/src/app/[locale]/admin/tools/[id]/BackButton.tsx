'use client';

import { useRouter } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import { ArrowLeft } from 'lucide-react';

export default function BackButton() {
  const router = useRouter();
  const t = useTranslations('admin');

  return (
    <button
      onClick={() => router.back()}
      className="flex items-center text-gray-600 hover:text-gray-900 mb-4 transition-colors"
    >
      <ArrowLeft className="w-4 h-4 mr-2" />
      {t('actions.back_to_review')}
    </button>
  );
}
