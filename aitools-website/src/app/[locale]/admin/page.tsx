'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, usePathname } from '@/i18n/routing';
import { useTranslations, useLocale } from 'next-intl';
import { Locale } from '@/i18n/config';
import LoadingSpinner from '@/components/LoadingSpinner';
import ErrorMessage from '@/components/ErrorMessage';
import SuccessMessage from '@/components/SuccessMessage';
import { apiClient, Tool } from '@/lib/api';
import {
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Search,
  Filter,
  Calendar,
  User,
  ExternalLink,
  AlertTriangle
} from 'lucide-react';
import ToolLogo from '@/components/ToolLogo';

export default function AdminPage() {
  const router = useRouter();
  const pathname = usePathname();

  const t = useTranslations('admin');
  const tCategories = useTranslations('categories');
  const tProfile = useTranslations('profile');
  const tLaunch = useTranslations('launch')
  const locale = useLocale() as Locale;

  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('pending');
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectReason, setRejectReason] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchTools();
  }, [statusFilter]);

  const fetchTools = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await apiClient.getAdminTools({
        status: statusFilter === 'all' ? undefined : statusFilter,
        limit: 50
      });

      if (response.success && response.data) {
        setTools(response.data.tools);
      } else {
        setError(response.error || t('errors.fetch_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setLoading(false);
    }
  };

  const filteredTools = tools.filter(tool => {
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tool.description.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || tool.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const handleApprove = async (toolId: string) => {
    try {
      setActionLoading(true);
      setError('');

      const response = await apiClient.approveTool(toolId, {
        reviewedBy: 'admin', // In production, this should be the current logged-in admin
        reviewNotes: t('success.tool_approved'),
        launchDate: new Date().toISOString()
      });

      if (response.success) {
        setSuccessMessage(t('success.tool_approved'));
        await fetchTools(); // Refetch data
      } else {
        setError(response.error || t('errors.approve_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setActionLoading(false);
    }
  };

  const handleReject = async (toolId: string, reason: string) => {
    try {
      setActionLoading(true);
      setError('');

      const response = await apiClient.rejectTool(toolId, {
        reviewedBy: 'admin', // In production, this should be the current logged-in admin
        rejectReason: reason
      });

      if (response.success) {
        setSuccessMessage(t('success.tool_rejected'));
        await fetchTools(); // Refetch data
        setShowRejectModal(false);
        setRejectReason('');
        setSelectedTool(null);
      } else {
        setError(response.error || t('errors.reject_failed'));
      }
    } catch (err) {
      setError(t('errors.network_error'));
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <Clock className="w-3 h-3 mr-1" />
            {t('status_labels.pending')}
          </span>
        );
      case 'approved':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircle className="w-3 h-3 mr-1" />
            {t('status_labels.approved')}
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircle className="w-3 h-3 mr-1" />
            {t('status_labels.rejected')}
          </span>
        );
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <LoadingSpinner size="lg" className="py-20" />
      </div>
    );
  }

  return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <Shield className="mr-3 h-8 w-8 text-blue-600" />
                {t('main_page.title')}
              </h1>
              <p className="mt-2 text-lg text-gray-600">
                {t('main_page.subtitle')}
              </p>
            </div>
            
            {/* Quick Stats */}
            <div className="flex space-x-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">
                  {tools.filter(t => t.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-500">{t('main_page.stats.pending')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {tools.filter(t => t.status === 'approved').length}
                </div>
                <div className="text-sm text-gray-500">{t('main_page.stats.approved')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {tools.filter(t => t.status === 'rejected').length}
                </div>
                <div className="text-sm text-gray-500">{t('main_page.stats.rejected')}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder={t('main_page.search_placeholder')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="sm:w-48">
              <div className="relative">
                <Filter className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
                >
                  <option value="all">{t('main_page.filter.all_status')}</option>
                  <option value="pending">{t('main_page.filter.pending')}</option>
                  <option value="approved">{t('main_page.filter.approved')}</option>
                  <option value="rejected">{t('main_page.filter.rejected')}</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {/* Success/Error Messages */}
        {successMessage && (
          <SuccessMessage
            message={successMessage}
            onClose={() => setSuccessMessage('')}
            className="mb-6"
          />
        )}

        {error && (
          <ErrorMessage
            message={error}
            onClose={() => setError('')}
            className="mb-6"
          />
        )}

        {/* Tools List */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {filteredTools.length === 0 ? (
            <div className="text-center py-12">
              <AlertTriangle className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">{t('main_page.no_tools.title')}</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm || statusFilter !== 'all' ? t('main_page.no_tools.description_filtered') : t('main_page.no_tools.description_empty')}
              </p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {filteredTools.map((tool) => (
                <div key={tool._id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-start space-x-4">
                        {/* Logo */}
                        <div className="flex-shrink-0">
                         <ToolLogo tool={tool} size='sm' />
                        </div>

                        {/* Tool Info */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 truncate">
                              {tool.name}
                            </h3>
                            {getStatusBadge(tool.status)}
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              {tCategories(`category_names.${tool.category}`) || tool.category}
                            </span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              {t(`pricing_labels.${tool.pricing}`) || tool.pricing}
                            </span>
                            {/* 付费优先发布标识 */}
                            {tool.launchOption === 'paid' && tool.paymentStatus === 'completed' && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                                <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path fillRule="evenodd" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" clipRule="evenodd" />
                                </svg>
                                {tLaunch('priority_service_paid')}
                              </span>
                            )}
                          </div>

                          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                            {tool.description}
                          </p>

                          <div className="flex items-center space-x-6 text-sm text-gray-500">
                            <div className="flex items-center">
                              <User className="w-4 h-4 mr-1" />
                              {tool.submittedBy}
                            </div>
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {formatDate(tool.submittedAt)}
                            </div>
                            {tool.launchDate && (
                              <div className="flex items-center">
                                <Clock className="w-4 h-4 mr-1" />
                                {t('main_page.published_date')}: {new Date(tool.launchDate).toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US')}
                              </div>
                            )}
                          </div>

                          {/* Tags */}
                          {tool.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-3">
                              {tool.tags.map((tag, index) => (
                                <span
                                  key={index}
                                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700"
                                >
                                  {tag}
                                </span>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center space-x-2 ml-4">
                      <a
                        href={tool.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                        title={t('main_page.visit_website')}
                      >
                        <ExternalLink className="w-4 h-4" />
                      </a>
                      
                      <button
                        onClick={() => router.push(`/admin/tools/${tool._id}`)}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                        title={t('main_page.view_details')}
                      >
                        {t('main_page.view_details')}
                      </button>

                      {tool.status === 'pending' && (
                        <>
                          <button
                            onClick={() => handleApprove(tool._id)}
                            disabled={actionLoading}
                            className="px-3 py-1 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                          >
                            {actionLoading ? t('actions.processing') : t('actions.approve')}
                          </button>
                          <button
                            onClick={() => {
                              setSelectedTool(tool._id);
                              setShowRejectModal(true);
                            }}
                            disabled={actionLoading}
                            className="px-3 py-1 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                          >
                            {t('actions.reject')}
                          </button>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Reject Modal */}
        {showRejectModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">{t('main_page.reject_modal_simple.title')}</h3>
              <p className="text-sm text-gray-600 mb-4">
                {t('main_page.reject_modal_simple.description')}
              </p>
              <textarea
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder={t('main_page.reject_modal_simple.placeholder')}
              />
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => {
                    setShowRejectModal(false);
                    setRejectReason('');
                    setSelectedTool(null);
                  }}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
                >
                  {t('actions.cancel')}
                </button>
                <button
                  onClick={() => selectedTool && handleReject(selectedTool, rejectReason)}
                  disabled={!rejectReason.trim() || actionLoading}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                >
                  {actionLoading ? t('actions.processing') : t('actions.confirm_reject')}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
  );
}
