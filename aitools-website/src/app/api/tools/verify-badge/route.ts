import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { toolId, website, expectedText } = await request.json();

    if (!website || !expectedText) {
      return NextResponse.json(
        { success: false, error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // 验证网站URL格式
    let websiteUrl: URL;
    try {
      websiteUrl = new URL(website.startsWith('http') ? website : `https://${website}`);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Invalid website URL' },
        { status: 400 }
      );
    }

    // 获取网站内容并检查是否包含badge
    try {
      const response = await fetch(`http://api.scraperapi.com/?api_key=********************************&url=${websiteUrl.toString()}`, {
        // headers: {
        //   'User-Agent': 'AITools Badge Verification Bot/1.0',
        // },
        // 设置超时时间
        signal: AbortSignal.timeout(10000), // 10秒超时
      });

      if (!response.ok) {
        return NextResponse.json(
          { success: false, error: `Website returned ${response.status} status` },
          { status: 400 }
        );
      }

      const html = await response.text();

      console.log('html.................', expectedText, html)
      
      // 检查是否包含预期的文本
      const containsBadge = html.includes(expectedText) && html.includes('aitools.pub');
      
      if (!containsBadge) {
        return NextResponse.json(
          { success: false, error: 'Badge not found on the website. Please make sure you have added the badge code to your homepage.' },
          { status: 400 }
        );
      }

      // 验证成功，不更新数据库，直接返回成功状态
      return NextResponse.json({
        success: true,
        message: 'Badge verification successful'
      });

    } catch (fetchError) {
      console.error('Failed to fetch website:', fetchError);
      
      if (fetchError instanceof Error && fetchError.name === 'TimeoutError') {
        return NextResponse.json(
          { success: false, error: 'Website request timed out. Please try again later.' },
          { status: 408 }
        );
      }
      
      return NextResponse.json(
        { success: false, error: 'Failed to access the website. Please check if the URL is correct and the website is accessible.' },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Badge verification error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
