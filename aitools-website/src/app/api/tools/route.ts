import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// GET /api/tools - 获取工具列表
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort') || 'createdAt';
    const order = searchParams.get('order') || 'desc';
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // 构建查询条件
    const query: any = {};
    
    if (category && category !== 'all') {
      query.category = category;
    }
    
    if (status && status !== 'all') {
      if (status === 'published') {
        // 查询已发布的工具：approved状态且launchDate已过
        query.status = 'approved';
        query.selectedLaunchDate = { $lte: new Date() };
      } else {
        query.status = status;
      }
    } else {
      // 默认只显示已发布的工具（对于公开API）
      query.status = 'approved';
      query.selectedLaunchDate = { $lte: new Date() };
    }
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // 日期筛选
    if (dateFrom || dateTo) {
      query.selectedLaunchDate = query.launchDate || {};
      if (dateFrom) {
        query.selectedLaunchDate.$gte = new Date(dateFrom);
      }
      if (dateTo) {
        query.selectedLaunchDate.$lte = new Date(dateTo);
      }
    }

    // 计算跳过的文档数
    const skip = (page - 1) * limit;

    // 构建排序条件
    const sortOrder = order === 'desc' ? -1 : 1;
    const sortQuery: any = {};
    sortQuery[sort] = sortOrder;

    // console.log('query.................', query, sortQuery, skip, limit)
    // 执行查询
    const tools = await Tool.find(query)
      .sort(sortQuery)
      .skip(skip)
      .limit(limit)
      .select('-submittedBy -reviewNotes -reviewedBy')
      .lean();

    // 获取总数
    const total = await Tool.countDocuments(query);

    // 计算分页信息
    const totalPages = Math.ceil(total / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        tools,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: total,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage
        }
      }
    });

  } catch (error) {
    console.error('Error fetching tools:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, error: getApiMessage(locale, 'tools.fetch_failed') },
      { status: 500 }
    );
  }
}

// POST /api/tools - 创建新工具
export async function POST(request: NextRequest) {
  try {
    await dbConnect();

    const body = await request.json();
    const locale = getLocaleFromRequest(request);

    // 验证必需字段
    const requiredFields = ['name', 'description', 'website', 'category', 'pricing', 'submitterName', 'submitterEmail'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: getApiMessage(locale, `tools.${field}_required`) },
          { status: 400 }
        );
      }
    }

    // 检查工具网站是否已存在
    const existingTool = await Tool.findOne({ website: body.website });
    if (existingTool) {
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'tools.website_exists') },
        { status: 400 }
      );
    }

    // 创建新工具
    const toolData = {
      name: body.name,
      tagline: body.tagline,
      description: body.description,
      website: body.website,
      logo: body.logo,
      category: body.category,
      pricing: body.pricing,
      tags: body.tags || [],
      submittedBy: body.submitterName, // 临时使用名称，后续应该使用用户ID
      submittedAt: new Date(),
      selectedLaunchDate: body.publishDate ? new Date(body.publishDate) : undefined,
      status: 'draft',
      views: 0,
      likes: 0,
      isActive: true
    };

    const tool = new Tool(toolData);
    await tool.save();

    return NextResponse.json({
      success: true,
      data: tool,
      message: getApiMessage(locale, 'tools.submit_success')
    }, { status: 201 });

  } catch (error: any) {
    console.error('Error creating tool:', error);
    const locale = getLocaleFromRequest(request);

    if (error?.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map((err: any) => err.message);
      return NextResponse.json(
        { success: false, error: getApiMessage(locale, 'errors.validation_failed'), details: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: getApiMessage(locale, 'tools.create_failed') },
      { status: 500 }
    );
  }
}
