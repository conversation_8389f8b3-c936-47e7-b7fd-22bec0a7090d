import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import dbConnect from '@/lib/mongodb';
import Comment from '@/models/Comment';
import User from '@/models/User';
import Tool from '@/models/Tool';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// 获取工具评论
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await dbConnect();

    const { id: toolId } = await params;
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // 查找评论并关联用户信息
    const comments = await Comment.find({ 
      toolId, 
      isActive: true,
      parentId: null // 只获取顶级评论
    })
    .populate('userId', 'name email image')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit);

    // 获取每个评论的回复
    const commentsWithReplies = await Promise.all(
      comments.map(async (comment) => {
        const replies = await Comment.find({
          parentId: comment._id,
          isActive: true
        })
        .populate('userId', 'name email image')
        .sort({ createdAt: 1 });

        return {
          ...comment.toObject(),
          replies
        };
      })
    );

    // 获取总评论数
    const total = await Comment.countDocuments({ 
      toolId, 
      isActive: true,
      parentId: { $exists: false }
    });

    return NextResponse.json({
      success: true,
      data: {
        comments: commentsWithReplies,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get comments error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}

// 添加评论
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    await dbConnect();

    // 获取用户信息
    const user = await User.findOne({ email: session.user.email });
    if (!user) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.not_found') },
        { status: 404 }
      );
    }

    const { id: toolId } = await params;
    const { content, parentId } = await request.json();

    // 验证输入
    if (!content || content.trim().length === 0) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    if (content.length > 1000) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'errors.invalid_request') },
        { status: 400 }
      );
    }

    // 检查工具是否存在
    const tool = await Tool.findById(toolId);
    if (!tool) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'tools.not_found') },
        { status: 404 }
      );
    }

    // 如果是回复，检查父评论是否存在
    if (parentId) {
      const parentComment = await Comment.findById(parentId);

      if (!parentComment || parentComment.toolId?.toString() !== toolId) {
        return NextResponse.json(
          { success: false, message: getApiMessage(locale, 'errors.not_found') },
          { status: 404 }
        );
      }
    }

    // 创建评论
    const comment = new Comment({
      toolId,
      userId: user._id,
      content: content.trim(),
      parentId: parentId || undefined
    });

    await comment.save();

    // 关联用户信息
    await comment.populate('userId', 'name email image');

    // 更新用户评论列表
    if (!user.comments.includes(comment._id.toString())) {
      user.comments.push(comment._id.toString());
      await user.save();
    }

    return NextResponse.json({
      success: true,
      data: comment
    }, { status: 201 });

  } catch (error) {
    console.error('Create comment error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}