import { NextRequest, NextResponse } from 'next/server';
import { createPaymentIntent } from '@/lib/stripe';

// POST /api/test/create-payment-intent - 创建测试支付意图
export async function POST(request: NextRequest) {
  try {
    const { amount, currency = 'cny' } = await request.json();

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { success: false, message: '无效的金额' },
        { status: 400 }
      );
    }

    // 创建支付意图
    const paymentIntent = await createPaymentIntent(
      amount,
      currency,
      {
        test: 'true',
        description: 'Stripe集成测试支付'
      }
    );

    return NextResponse.json({
      success: true,
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id
    });

  } catch (error) {
    console.error('Error creating test payment intent:', error);
    return NextResponse.json(
      { success: false, message: '创建支付失败' },
      { status: 500 }
    );
  }
}
