import { NextRequest, NextResponse } from 'next/server';
import { readFile, stat } from 'fs/promises';
import { join } from 'path';
import { getUploadDir } from '@/constants/upload-config';

// GET /api/uploads/[...path] - 提供上传文件的静态访问
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    
    if (!path || path.length === 0) {
      return NextResponse.json(
        { success: false, message: 'File path is required' },
        { status: 400 }
      );
    }

    // 重构路径：第一部分是子目录（如 logos），其余是文件名
    const [subDir, ...fileNameParts] = path;
    const fileName = fileNameParts.join('/');

    if (!fileName) {
      return NextResponse.json(
        { success: false, message: 'File name is required' },
        { status: 400 }
      );
    }

    // 安全检查：防止路径遍历攻击
    if (fileName.includes('..') || fileName.includes('\\') || subDir.includes('..')) {
      return NextResponse.json(
        { success: false, message: 'Invalid file path' },
        { status: 400 }
      );
    }

    // 构建完整文件路径
    const uploadDir = getUploadDir(subDir?.toUpperCase() as any);
    const filePath = join(uploadDir, fileName);

    try {
      // 检查文件是否存在
      const fileStats = await stat(filePath);
      
      if (!fileStats.isFile()) {
        return NextResponse.json(
          { success: false, message: 'File not found' },
          { status: 404 }
        );
      }

      // 读取文件
      const fileBuffer = await readFile(filePath);
      
      // 根据文件扩展名确定 MIME 类型
      const ext = fileName.split('.').pop()?.toLowerCase();
      let contentType = 'application/octet-stream';
      
      switch (ext) {
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'gif':
          contentType = 'image/gif';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        case 'svg':
          contentType = 'image/svg+xml';
          break;
      }

      // 设置缓存头
      const headers = new Headers();
      headers.set('Content-Type', contentType);
      headers.set('Cache-Control', 'public, max-age=31536000, immutable'); // 1年缓存
      headers.set('Content-Length', fileStats.size.toString());

      return new NextResponse(fileBuffer, {
        status: 200,
        headers
      });

    } catch (fileError) {
      return NextResponse.json(
        { success: false, message: 'File not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('File serving error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
