import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb';
import Tool from '@/models/Tool';
import { CATEGORY_SLUGS } from '@/constants/categories-i18n';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';

// GET /api/categories - 获取分类列表和统计
export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    // 获取所有分类的工具数量统计（已发布的工具）
    const categoryStats = await Tool.aggregate([
      {
        $match: {
          status: 'approved',
          selectedLaunchDate: { $lte: new Date() }
        }
      },
      {
        $group: {
          _id: '$category',
          count: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    // 构建完整的分类列表（使用分类slug）
    const categories = CATEGORY_SLUGS.map(slug => {
      const stats = categoryStats.find(stat => stat._id === slug);
      return {
        id: slug,
        name: slug, // API 返回 slug，前端会根据语言环境显示对应的名称
        count: stats?.count || 0,
        totalViews: stats?.totalViews || 0,
        totalLikes: stats?.totalLikes || 0
      };
    });

    // 按工具数量排序
    categories.sort((a, b) => b.count - a.count);

    // 获取总统计（已发布的工具）
    const totalStats = await Tool.aggregate([
      {
        $match: {
          status: 'approved',
          selectedLaunchDate: { $lte: new Date() }
        }
      },
      {
        $group: {
          _id: null,
          totalTools: { $sum: 1 },
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' }
        }
      }
    ]);

    const overview = totalStats[0] || {
      totalTools: 0,
      totalViews: 0,
      totalLikes: 0
    };

    return NextResponse.json({
      success: true,
      data: {
        categories,
        overview
      }
    });

  } catch (error) {
    console.error('Error fetching categories:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, error: getApiMessage(locale, 'errors.internal_error') },
      { status: 500 }
    );
  }
}
