import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';
import { UPLOAD_CONFIG, getFileUrl, validateFileUpload, getUploadDir } from '@/constants/upload-config';

export async function POST(request: NextRequest) {
  try {
    // 检查用户认证
    const session = await getServerSession(authOptions);
    const locale = getLocaleFromRequest(request);

    if (!session?.user?.email) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'user.unauthorized') },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('logo') as File;

    if (!file) {
      return NextResponse.json(
        { success: false, message: getApiMessage(locale, 'upload.no_file') },
        { status: 400 }
      );
    }

    // 使用配置文件验证文件
    const validation = validateFileUpload(file, 'LOGO');
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, message: validation.error },
        { status: 400 }
      );
    }

    // 生成唯一文件名
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split('.').pop();
    const fileName = `logo_${timestamp}_${randomString}.${fileExtension}`;

    // 使用配置的上传路径
    const uploadDir = getUploadDir('LOGOS');
    try {
      await mkdir(uploadDir, { recursive: true });
    } catch (error) {
      // 目录可能已存在，忽略错误
    }

    // 保存文件
    const filePath = join(uploadDir, fileName);
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);

    await writeFile(filePath, buffer);

    // 使用配置的URL生成方法
    const fileUrl = getFileUrl('LOGOS', fileName);


    return NextResponse.json({
      success: true,
      data: {
        url: fileUrl,
        filename: fileName,
        size: file.size,
        type: file.type
      },
      message: getApiMessage(locale, 'upload.upload_success')
    });

  } catch (error) {
    console.error('Upload error:', error);
    const locale = getLocaleFromRequest(request);
    return NextResponse.json(
      { success: false, message: getApiMessage(locale, 'upload.upload_failed') },
      { status: 500 }
    );
  }
}
