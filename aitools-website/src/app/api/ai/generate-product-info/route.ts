import { NextRequest, NextResponse } from 'next/server';
import { getAiServiceUrls } from '@/lib/env';

export async function POST(request: NextRequest) {
  try {
    const { website } = await request.json();

    if (!website) {
      return NextResponse.json(
        { success: false, error: 'Website URL is required' },
        { status: 400 }
      );
    }

    // 验证URL格式
    try {
      new URL(website);
    } catch (error) {
      return NextResponse.json(
        { success: false, error: 'Invalid website URL format' },
        { status: 400 }
      );
    }

    const aiServiceUrls = getAiServiceUrls();
    
    // 调用AI服务
    const response = await fetch(aiServiceUrls.generateProductInfo, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ data: {url: website }}),
    });

    if (!response.ok) {
      console.error('AI service error:', response.status, response.statusText);
      return NextResponse.json(
        { success: false, error: 'AI service is currently unavailable' },
        { status: 503 }
      );
    }

    const aiResponse = await response.json();
    
    // 验证AI服务返回的数据格式
    if (!aiResponse.data?.generated?.productInfo) {
      console.error('Invalid AI response format:', aiResponse);
      return NextResponse.json(
        { success: false, error: 'Invalid response from AI service' },
        { status: 502 }
      );
    }

    const productInfo = aiResponse.data.generated.productInfo;
    
    // 验证必要字段
    const requiredFields = ['name', 'tagline', 'description', 'category'];
    const missingFields = requiredFields.filter(field => !productInfo[field]);
    
    if (missingFields.length > 0) {
      console.error('Missing required fields in AI response:', missingFields);
      return NextResponse.json(
        { success: false, error: 'Incomplete data from AI service' },
        { status: 502 }
      );
    }

    // 返回处理后的数据
    return NextResponse.json({
      success: true,
      data: {
        name: productInfo.name || '',
        tagline: productInfo.tagline || '',
        description: productInfo.description || '',
        category: productInfo.category || '',
        tags: Array.isArray(productInfo.tags) ? productInfo.tags : [],
      }
    });

  } catch (error) {
    console.error('Generate product info error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
