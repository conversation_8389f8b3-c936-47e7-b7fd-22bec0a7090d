@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom utilities */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}



.markdown-body {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif;
  color: #374151; /* gray-700 */
  line-height: 1.75;
  font-size: 1rem;
}

.markdown-body h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
  font-weight: 700;
  margin: 1.5rem 0 1rem;
  color: #111827; /* gray-900 */
}

.markdown-body h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 1.25rem 0 1rem;
  color: #111827;
}

.markdown-body h3 {
  font-size: 1.25rem;
  font-weight: 500;
  margin: 1rem 0 0.75rem;
  color: #111827;
}

.markdown-body h4,
.markdown-body h5,
.markdown-body h6 {
  font-size: 1rem;
  font-weight: 500;
  margin: 0.75rem 0 0.5rem;
  color: #1f2937;
}

.markdown-body p {
  margin: 1rem 0;
  color: #374151;
}

.markdown-body ul,
.markdown-body ol {
  padding-left: 1.5rem;
  margin: 1rem 0;
}

.markdown-body ul {
  list-style-type: disc;
}

.markdown-body ol {
  list-style-type: decimal;
}

.markdown-body li {
  margin: 0.5rem 0;
}

.markdown-body li > ul,
.markdown-body li > ol {
  margin-top: 0.5rem;
}

.markdown-body blockquote {
  border-left: 4px solid #d1d5db; /* gray-300 */
  background-color: #f9fafb; /* gray-50 */
  padding: 0.75rem 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #4b5563;
}

.markdown-body hr {
  border: none;
  border-top: 1px solid #e5e7eb; /* gray-200 */
  margin: 2rem 0;
}

.markdown-body a {
  color: #2563eb; /* blue-600 */
  text-decoration: underline;
  transition: color 0.2s;
}

.markdown-body a:hover {
  color: #1d4ed8; /* blue-700 */
}

.markdown-body strong {
  font-weight: 600;
  color: #111827;
}

.markdown-body em {
  font-style: italic;
  color: #374151;
}

.markdown-body code {
  background-color: #f3f4f6; /* gray-100 */
  color: #1f2937; /* gray-800 */
  font-size: 0.875rem;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.markdown-body pre {
  background-color: #f3f4f6;
  color: #1f2937;
  font-size: 0.875rem;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1.5rem 0;
}

.markdown-body pre code {
  background: none;
  padding: 0;
  font-size: inherit;
  color: inherit;
}

.markdown-body table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  font-size: 0.95rem;
}

.markdown-body thead {
  background-color: #f9fafb;
}

.markdown-body th,
.markdown-body td {
  border: 1px solid #d1d5db;
  padding: 0.75rem 1rem;
  text-align: left;
}

.markdown-body th {
  font-weight: 600;
  color: #111827;
}

.markdown-body td {
  color: #374151;
}

.markdown-body img {
  max-width: 100%;
  border-radius: 0.5rem;
  margin: 1rem 0;
}
