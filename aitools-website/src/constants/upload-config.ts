/**
 * 上传配置和字段长度限制
 */

// 字段长度限制配置
export const FIELD_LIMITS = {
  // 工具基本信息字段
  TOOL_NAME: {
    min: 2,
    max: 40,
    label: 'Tool Name'
  },
  TAGLINE: {
    min: 4,
    max: 80,
    label: 'Tagline'
  },
  DESCRIPTION: {
    min: 10,
    max: 2000,
    label: 'Description'
  },
  LONG_DESCRIPTION: {
    min: 0,
    max: 3000,
    label: 'Long Description'
  },
  PRICING_DETAILS: {
    min: 0,
    max: 500,
    label: 'Pricing Details'
  },
  WEBSITE_URL: {
    min: 10,
    max: 100,
    label: 'Website URL'
  },
  
  // 用户相关字段
  USER_NAME: {
    min: 1,
    max: 40,
    label: 'User Name'
  },
  USER_BIO: {
    min: 0,
    max: 1000,
    label: 'User Bio'
  },
  
  // 评论字段
  COMMENT: {
    min: 1,
    max: 1000,
    label: 'Comment'
  }
} as const;

// 上传目录配置
export const UPLOAD_CONFIG = {
  // 基础上传目录（相对于 public 目录）
  BASE_DIR: process.env.UPLOAD_BASE_DIR || 'uploads',
  
  // 各类文件的子目录
  DIRECTORIES: {
    LOGOS: 'logos',
    AVATARS: 'avatars',
    SCREENSHOTS: 'screenshots',
    TEMP: 'temp'
  },
  
  // 文件大小限制（字节）
  FILE_SIZE_LIMITS: {
    LOGO: 5 * 1024 * 1024, // 5MB
    AVATAR: 5 * 1024 * 1024, // 5MB
    SCREENSHOT: 10 * 1024 * 1024, // 10MB
  },
  
  // 允许的文件类型
  ALLOWED_TYPES: {
    IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  },
  
  // 文件命名规则
  NAMING: {
    LOGO_PREFIX: 'logo_',
    AVATAR_PREFIX: 'avatar_',
    SCREENSHOT_PREFIX: 'screenshot_',
    TIMESTAMP_FORMAT: 'timestamp_random' // timestamp + random string
  }
} as const;

// 获取完整的上传目录路径
export function getUploadDir(subDir: keyof typeof UPLOAD_CONFIG.DIRECTORIES): string {
  const baseDir = UPLOAD_CONFIG.BASE_DIR;
  const subDirectory = UPLOAD_CONFIG.DIRECTORIES[subDir];
  return `${baseDir}/${subDirectory}`;
}

// 获取文件的URL路径
export function getFileUrl(subDir: keyof typeof UPLOAD_CONFIG.DIRECTORIES, filename: string): string {
  const subDirectory = UPLOAD_CONFIG.DIRECTORIES[subDir];
  return `/api/uploads/${subDirectory}/${filename}`;
}

// 生成唯一文件名
export function generateFileName(
  prefix: string,
  originalName: string,
  namingType: string = UPLOAD_CONFIG.NAMING.TIMESTAMP_FORMAT
): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const fileExtension = originalName.split('.').pop();
  
  switch (namingType) {
    case 'timestamp_random':
      return `${prefix}${timestamp}_${randomString}.${fileExtension}`;
    default:
      return `${prefix}${timestamp}_${randomString}.${fileExtension}`;
  }
}

// 验证字段长度
export function validateFieldLength(
  fieldName: keyof typeof FIELD_LIMITS,
  value: string
): { isValid: boolean; error?: string } {
  const limits = FIELD_LIMITS[fieldName];
  if (!limits) {
    return { isValid: true };
  }
  
  const length = value.trim().length;
  
  if (length < limits.min) {
    return {
      isValid: false,
      error: `${limits.label} must be at least ${limits.min} characters long`
    };
  }
  
  if (length > limits.max) {
    return {
      isValid: false,
      error: `${limits.label} cannot exceed ${limits.max} characters`
    };
  }
  
  return { isValid: true };
}

// 验证文件大小
export function validateFileSize(
  fileType: keyof typeof UPLOAD_CONFIG.FILE_SIZE_LIMITS,
  fileSize: number
): { isValid: boolean; error?: string } {
  const limit = UPLOAD_CONFIG.FILE_SIZE_LIMITS[fileType];
  
  if (fileSize > limit) {
    const limitMB = Math.round(limit / (1024 * 1024));
    return {
      isValid: false,
      error: `File size cannot exceed ${limitMB}MB`
    };
  }
  
  return { isValid: true };
}

// 验证文件类型
export function validateFileType(
  fileType: string,
  allowedTypes: readonly string[]
): { isValid: boolean; error?: string } {
  if (!allowedTypes.includes(fileType)) {
    return {
      isValid: false,
      error: `File type ${fileType} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }
  
  return { isValid: true };
}


// 验证文件上传的类型和大小
export function validateFileUpload(
  file: { type: string; size: number },
  fileCategory: keyof typeof UPLOAD_CONFIG.FILE_SIZE_LIMITS
): { isValid: boolean; error?: string } {
  // 验证大小
  const sizeValidation = validateFileSize(fileCategory, file.size);
  if (!sizeValidation.isValid) {
    return sizeValidation;
  }

  // 根据类别选择允许的类型，目前只有 IMAGES 可选，可根据需要扩展
  const allowedTypes = UPLOAD_CONFIG.ALLOWED_TYPES.IMAGES;
  const typeValidation = validateFileType(file.type, allowedTypes);
  if (!typeValidation.isValid) {
    return typeValidation;
  }

  return { isValid: true };
}