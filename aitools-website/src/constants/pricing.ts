/**
 * 统一的价格配置文件
 * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中
 */

// 基础价格配置
export const PRICING_CONFIG = {
  // Priority launch service pricing
  PRIORITY_LAUNCH: {
    // Display price (CNY)
    displayPrice: 19.9,
    // Original price (CNY) - for showing pre-discount price
    originalPrice: 49.9,
    // Stripe price (in cents)
    stripeAmount: 1990,
    // Original Stripe amount (in cents)
    originalStripeAmount: 4990,
    // Currency
    currency: 'USD',
    // Stripe currency code (lowercase)
    stripeCurrency: 'usd', // Note: currently using USD for testing
    // Product name
    productName: 'AI Tool Priority Launch Service',
    // Product description
    description: 'Get your AI tool prioritized for review and featured placement',
    // Limited-time promotion info
    promotion: {
      // Whether the promotion is enabled
      enabled: true,
      // Promotion description
      description: 'Limited-time offer - First 100 paid users',
      // Discount percentage
      discountPercent: 50,
      // Remaining slots (can be dynamically fetched from database)
      remainingSlots: 85
    },
    // Feature list
    features: [
      'Choose any publish date',
      'Priority review processing',
      'Featured homepage placement',
      'Dedicated customer support'
    ]
  },

  // Free launch configuration
  FREE_LAUNCH: {
    displayPrice: 0,
    stripeAmount: 0,
    currency: 'USD',
    stripeCurrency: 'usd',
    productName: 'Free Launch Service',
    description: 'Choose any publish date after one month',
    features: [
      'Free submission for review',
      'Publish date: from one month later',
      'Standard review process',
      'Standard display placement'
    ]
  }
} as const;

// 发布选项配置
export const LAUNCH_OPTIONS = [
  {
    id: 'free' as const,
    title: '免费发布',
    description: PRICING_CONFIG.FREE_LAUNCH.description,
    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,
    features: PRICING_CONFIG.FREE_LAUNCH.features
  },
  {
    id: 'paid' as const,
    title: '优先发布',
    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,
    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,
    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,
    recommended: true
  }
] as const;

// 工具定价类型配置
export const TOOL_PRICING_TYPES = {
  FREE: {
    value: 'free',
    label: 'Free',
    color: 'bg-green-100 text-green-800'
  },
  FREEMIUM: {
    value: 'freemium',
    label: ' Freemium',
    color: 'bg-blue-100 text-blue-800'
  },
  PAID: {
    value: 'paid',
    label: 'Paid',
    color: 'bg-orange-100 text-orange-800'
  }
} as const;

// 工具定价选项（用于筛选）
export const TOOL_PRICING_OPTIONS = [
  { value: '', label: 'All Prices' },
  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },
  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },
  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }
] as const;

// 工具定价选项（用于表单）
export const TOOL_PRICING_FORM_OPTIONS = [
  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },
  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },
  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }
] as const;

// 类型定义
export type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];
export type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];

// 辅助函数
export const getPricingConfig = (optionId: LaunchOptionId) => {
  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;
};

export const getToolPricingColor = (pricing: string) => {
  switch (pricing) {
    case TOOL_PRICING_TYPES.FREE.value:
      return TOOL_PRICING_TYPES.FREE.color;
    case TOOL_PRICING_TYPES.FREEMIUM.value:
      return TOOL_PRICING_TYPES.FREEMIUM.color;
    case TOOL_PRICING_TYPES.PAID.value:
      return TOOL_PRICING_TYPES.PAID.color;
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getToolPricingText = (pricing: string) => {
  switch (pricing) {
    case TOOL_PRICING_TYPES.FREE.value:
      return TOOL_PRICING_TYPES.FREE.label;
    case TOOL_PRICING_TYPES.FREEMIUM.value:
      return TOOL_PRICING_TYPES.FREEMIUM.label;
    case TOOL_PRICING_TYPES.PAID.value:
      return TOOL_PRICING_TYPES.PAID.label;
    default:
      return pricing;
  }
};

// 格式化价格显示
export const formatPrice = (price: number, locale?: string) => {
  if (price === 0) {
    return locale === 'zh' ? '免费' : 'Free';
  }
  return `¥${price}`;
};

// 格式化原价显示（带删除线）
export const formatOriginalPrice = (price: number, locale?: string) => {
  if (price === 0) {
    return locale === 'zh' ? '免费' : 'Free';
  }
  return `¥${price}`;
};

// 获取促销信息
export const getPromotionInfo = () => {
  return PRICING_CONFIG.PRIORITY_LAUNCH.promotion;
};

// 检查是否有促销活动
export const hasActivePromotion = () => {
  const promotion = PRICING_CONFIG.PRIORITY_LAUNCH.promotion;
  return promotion.enabled && promotion.remainingSlots > 0;
};

// 格式化Stripe金额显示
export const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: currency.toUpperCase(),
    minimumFractionDigits: 2,
  }).format(amount / 100);
};
