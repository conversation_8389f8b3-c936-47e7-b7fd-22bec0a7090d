/**
 * 动态环境配置工具
 * 根据运行时环境自动判断URL配置，而不是在配置文件中写死
 */

/**
 * 获取当前运行环境的基础URL
 * 支持开发环境、生产环境和部署平台的自动检测
 */
export function getBaseUrl(): string {
  // 1. 优先使用明确设置的环境变量
  if (process.env.NEXT_PUBLIC_APP_URL) {
    return process.env.NEXT_PUBLIC_APP_URL;
  }

  // 2. 在服务器端运行时
  if (typeof window === 'undefined') {
    // 构建时特殊处理：如果是构建阶段，使用固定的URL
    if (process.env.NODE_ENV === 'production' && !process.env.VERCEL_URL && !process.env.NETLIFY) {
      // 生产构建时，假设服务器将在3011端口运行
      return 'http://localhost:3011';
    }

    // Vercel部署环境
    if (process.env.VERCEL_URL) {
      return `https://${process.env.VERCEL_URL}`;
    }

    // Netlify部署环境
    if (process.env.NETLIFY && process.env.URL) {
      return process.env.URL;
    }

    // Railway部署环境
    if (process.env.RAILWAY_STATIC_URL) {
      return process.env.RAILWAY_STATIC_URL;
    }

    // 其他云平台的通用环境变量
    if (process.env.APP_URL) {
      return process.env.APP_URL;
    }

    // 开发环境默认值
    const port = process.env.PORT || '3011';
    return `http://localhost:${port}`;
  }

  // 3. 在客户端运行时
  if (typeof window !== 'undefined') {
    const { protocol, hostname, port } = window.location;
    return `${protocol}//${hostname}${port ? `:${port}` : ''}`;
  }

  // 4. 兜底默认值
  return 'http://localhost:3011';
}

/**
 * 获取API基础URL
 */
export function getApiBaseUrl(): string {
  // 1. 优先使用明确设置的环境变量
  if (process.env.NEXT_PUBLIC_API_BASE_URL) {
    return process.env.NEXT_PUBLIC_API_BASE_URL;
  }

  // 2. 基于基础URL构建API URL
  const baseUrl = getBaseUrl();
  return `${baseUrl}/api`;
}

/**
 * 获取NextAuth URL
 * NextAuth需要这个URL来处理回调和重定向
 */
export function getNextAuthUrl(): string {
  // 1. 优先使用明确设置的环境变量
  if (process.env.NEXTAUTH_URL) {
    return process.env.NEXTAUTH_URL;
  }

  // 2. 基于基础URL构建
  return getBaseUrl();
}

/**
 * 获取当前运行环境
 */
export function getEnvironment(): 'development' | 'production' | 'test' {
  return (process.env.NODE_ENV as any) || 'development';
}

/**
 * 检查是否在开发环境
 */
export function isDevelopment(): boolean {
  return getEnvironment() === 'development';
}

/**
 * 检查是否在生产环境
 */
export function isProduction(): boolean {
  return getEnvironment() === 'production';
}

/**
 * 获取当前端口号
 */
export function getCurrentPort(): string {
  // 服务器端
  if (typeof window === 'undefined') {
    return process.env.PORT || '3011';
  }
  
  // 客户端
  return window.location.port || (window.location.protocol === 'https:' ? '443' : '80');
}

/**
 * 动态环境配置对象
 * 可以在应用中直接使用这些配置
 */
export const dynamicEnv = {
  baseUrl: getBaseUrl(),
  apiBaseUrl: getApiBaseUrl(),
  nextAuthUrl: getNextAuthUrl(),
  environment: getEnvironment(),
  isDevelopment: isDevelopment(),
  isProduction: isProduction(),
  port: getCurrentPort(),
};

/**
 * 获取AI服务URL配置
 */
export function getAiServiceUrls() {
  return {
    generateProductInfo: process.env.AI_GENERATE_PRODUCT_INFO_URL || 'http://localhost:50058/ai/generateProductInfo',
  };
}

/**
 * 在开发环境中打印配置信息（用于调试）
 */
if (isDevelopment() && typeof window === 'undefined') {
  console.log('🔧 Dynamic Environment Configuration:');
  console.log('  Base URL:', dynamicEnv.baseUrl);
  console.log('  API Base URL:', dynamicEnv.apiBaseUrl);
  console.log('  NextAuth URL:', dynamicEnv.nextAuthUrl);
  console.log('  Environment:', dynamicEnv.environment);
  console.log('  Port:', dynamicEnv.port);
  console.log('  AI Service URLs:', getAiServiceUrls());
}
