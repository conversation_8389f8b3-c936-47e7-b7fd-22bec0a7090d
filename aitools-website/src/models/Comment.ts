import mongoose, { Document, Schema } from 'mongoose';

export interface IComment extends Document {
  toolId: string;
  userId: string;
  content: string;
  parentId?: string; // 支持回复评论
  likes: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const CommentSchema: Schema = new Schema({
  toolId: {
    type: Schema.Types.ObjectId,
    ref: 'Tool',
    required: [true, 'Tool ID is required']
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  content: {
    type: String,
    required: [true, 'Comment content is required'],
    trim: true,
    maxlength: [1000, 'Comment cannot exceed 1000 characters'],
    minlength: [1, 'Comment cannot be empty']
  },
  parentId: {
    type: Schema.Types.ObjectId,
    ref: 'Comment',
    default: null
  },
  likes: {
    type: Number,
    default: 0,
    min: 0
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
CommentSchema.index({ toolId: 1, createdAt: -1 });
CommentSchema.index({ userId: 1 });
CommentSchema.index({ parentId: 1 });
CommentSchema.index({ isActive: 1 });

// 虚拟字段 - 获取回复数量
CommentSchema.virtual('replyCount', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'parentId',
  count: true,
  match: { isActive: true }
});

// 静态方法 - 获取工具的评论（包含回复）
CommentSchema.statics.getToolComments = function(toolId: string) {
  return this.find({ 
    toolId, 
    isActive: true 
  })
  .populate('userId', 'name avatar')
  .populate({
    path: 'parentId',
    select: 'content userId',
    populate: {
      path: 'userId',
      select: 'name'
    }
  })
  .sort({ createdAt: -1 });
};

// 实例方法 - 获取回复
CommentSchema.methods.getReplies = function() {
  return mongoose.model('Comment').find({
    parentId: this._id,
    isActive: true
  })
  .populate('userId', 'name avatar')
  .sort({ createdAt: 1 });
};

export default mongoose.models.Comment || mongoose.model<IComment>('Comment', CommentSchema);
