import mongoose, { Document, Schema } from 'mongoose';

export interface IOrder extends Document {
  userId: string; // 用户ID
  toolId: string; // 工具ID
  type: 'launch_date_priority'; // 订单类型
  amount: number; // 金额（分为单位）
  currency: string; // 货币类型
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  
  // 支付相关
  paymentMethod?: string; // 支付方式
  paymentIntentId?: string; // Stripe payment intent ID (已弃用，使用stripePaymentIntentId)
  paymentSessionId?: string; // Stripe checkout session ID
  stripePaymentIntentId?: string; // Stripe payment intent ID
  stripeCustomerId?: string; // Stripe customer ID
  stripePaymentDetails?: {
    paymentIntentId: string;
    amount: number;
    currency: string;
    status: string;
    created: Date;
    failureReason?: string;
  };
  
  // 订单详情
  description: string; // 订单描述
  selectedLaunchDate: Date; // 选择的发布日期
  
  // 时间戳
  createdAt: Date;
  updatedAt: Date;
  paidAt?: Date;
  cancelledAt?: Date;
  refundedAt?: Date;
}

const OrderSchema: Schema = new Schema({
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User ID is required']
  },
  toolId: {
    type: Schema.Types.ObjectId,
    ref: 'Tool',
    required: [true, 'Tool ID is required']
  },
  type: {
    type: String,
    required: true,
    enum: ['launch_date_priority'],
    default: 'launch_date_priority'
  },
  amount: {
    type: Number,
    required: [true, 'Amount is required'],
    min: [0, 'Amount must be positive']
  },
  currency: {
    type: String,
    required: true,
    default: 'CNY',
    enum: ['CNY', 'USD']
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'completed', 'failed', 'cancelled', 'refunded'],
    default: 'pending'
  },
  
  // 支付相关
  paymentMethod: {
    type: String,
    trim: true
  },
  paymentIntentId: {
    type: String,
    trim: true
  },
  paymentSessionId: {
    type: String,
    trim: true
  },
  stripePaymentIntentId: {
    type: String,
    trim: true
  },
  stripeCustomerId: {
    type: String,
    trim: true
  },
  stripePaymentDetails: {
    paymentIntentId: String,
    amount: Number,
    currency: String,
    status: String,
    created: Date,
    failureReason: String
  },
  
  // 订单详情
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  selectedLaunchDate: {
    type: Date,
    required: [true, 'Selected launch date is required']
  },
  
  // 时间戳
  paidAt: {
    type: Date
  },
  cancelledAt: {
    type: Date
  },
  refundedAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
OrderSchema.index({ userId: 1, createdAt: -1 });
OrderSchema.index({ toolId: 1 });
OrderSchema.index({ status: 1 });
OrderSchema.index({ paymentIntentId: 1 });
OrderSchema.index({ paymentSessionId: 1 });
OrderSchema.index({ stripePaymentIntentId: 1 });
OrderSchema.index({ stripeCustomerId: 1 });

// 虚拟字段
OrderSchema.virtual('user', {
  ref: 'User',
  localField: 'userId',
  foreignField: '_id',
  justOne: true
});

OrderSchema.virtual('tool', {
  ref: 'Tool',
  localField: 'toolId',
  foreignField: '_id',
  justOne: true
});

// 实例方法
OrderSchema.methods.markAsPaid = function() {
  this.status = 'completed';
  this.paidAt = new Date();
  return this.save();
};

OrderSchema.methods.markAsFailed = function() {
  this.status = 'failed';
  return this.save();
};

OrderSchema.methods.cancel = function() {
  this.status = 'cancelled';
  this.cancelledAt = new Date();
  return this.save();
};

OrderSchema.methods.refund = function() {
  this.status = 'refunded';
  this.refundedAt = new Date();
  return this.save();
};

export default mongoose.models.Order || mongoose.model<IOrder>('Order', OrderSchema);
