'use client';

import { useEffect } from 'react';
import { usePathname } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import { FaHeart, FaRegHeart } from 'react-icons/fa';
import { useLike } from '@/contexts/LikeContext';
import { Locale } from '@/i18n/config';

interface LikeButtonProps {
  toolId: string;
  initialLikes?: number;
  initialLiked?: boolean;
  onLoginRequired?: () => void;
  onUnlike?: (toolId: string) => void;
  isInLikedPage?: boolean;
  showCount?: boolean; // 是否显示点赞数量
  size?: 'sm' | 'md' | 'lg'; // 按钮大小
}

export default function LikeButton({
  toolId,
  initialLikes = 0,
  initialLiked = false,
  onLoginRequired,
  onUnlike,
  isInLikedPage = false,
  showCount = true,
  size = 'md'
}: LikeButtonProps) {
  const { data: session } = useSession();
  const { getToolState, initializeToolState, toggleLike } = useLike();

  const pathname = usePathname();
  const t = useTranslations('common');

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  // 获取当前工具的状态
  const toolState = getToolState(toolId);

  // 初始化工具状态
  useEffect(() => {
    initializeToolState(toolId, initialLikes, initialLiked);
  }, [toolId, initialLikes, initialLiked]); // 移除initializeToolState依赖，避免无限循环

  // 处理点赞点击
  const handleLike = async () => {
    if (!session) {
      onLoginRequired?.();
      return;
    }

    if (toolState.loading) return;

    // 记录操作前的状态
    const wasLiked = toolState.liked;

    // 执行点赞操作
    const success = await toggleLike(toolId, isInLikedPage);

    // 如果是在收藏页面且从已点赞变为未点赞，调用onUnlike回调
    if (success && isInLikedPage && wasLiked && onUnlike) {
      onUnlike(toolId);
    }
  };

  // 根据size确定样式
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          button: 'p-1.5',
          icon: 'h-4 w-4',
          text: 'text-sm'
        };
      case 'lg':
        return {
          button: 'p-3',
          icon: 'h-6 w-6',
          text: 'text-lg'
        };
      default: // md
        return {
          button: 'p-2',
          icon: 'h-5 w-5',
          text: 'text-base'
        };
    }
  };

  const sizeClasses = getSizeClasses();

  return (
    <button
      onClick={handleLike}
      disabled={toolState.loading}
      className={`
        ${sizeClasses.button}
        inline-flex items-center space-x-1
        ${toolState.liked
          ? 'text-red-500 hover:text-red-600'
          : 'text-gray-400 hover:text-red-500'
        }
        transition-colors duration-200
        disabled:opacity-50 disabled:cursor-not-allowed
      `}
      title={toolState.liked ? t('unlike') : t('like')}
    >
      {toolState.loading ? (
        <div className={`${sizeClasses.icon} animate-spin rounded-full border-2 border-gray-300 border-t-red-500`} />
      ) : toolState.liked ? (
        <FaHeart className={sizeClasses.icon} />
      ) : (
        <FaRegHeart className={sizeClasses.icon} />
      )}
      {showCount && (
        <span className={`${sizeClasses.text} font-medium`}>
          {toolState.likes}
        </span>
      )}
    </button>
  );
}
