'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { FaUser, FaR<PERSON>ly, <PERSON>aH<PERSON>t, FaRegHeart } from 'react-icons/fa';

interface Comment {
  _id: string;
  content: string;
  userId: {
    _id: string;
    name: string;
    email: string;
    image?: string;
  };
  createdAt: string;
  likes: number;
  replies?: Comment[];
}

interface CommentSectionClientProps {
  toolId: string;
  initialComments: Comment[];
  onLoginRequired?: () => void;
}

export default function CommentSectionClient({ 
  toolId, 
  initialComments, 
  onLoginRequired 
}: CommentSectionClientProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>(initialComments);
  const [newComment, setNewComment] = useState('');
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const t = useTranslations('comments');

  // 重新获取评论列表
  const fetchComments = async () => {
    try {
      const response = await fetch(`/api/tools/${toolId}/comments`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setComments(data.data.comments);
        }
      }
    } catch (error) {
      console.error('Failed to fetch comments:', error);
    }
  };

  // 提交评论
  const handleSubmitComment = async () => {
    if (!session) {
      onLoginRequired?.();
      return;
    }

    if (!newComment.trim()) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/tools/${toolId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newComment.trim()
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setNewComment('');
          fetchComments(); // 重新获取评论列表
        }
      } else {
        const errorData = await response.json();
        console.error('Comment submission failed:', errorData.message);
      }
    } catch (error) {
      console.error('Comment submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 提交回复
  const handleSubmitReply = async () => {
    if (!session) {
      onLoginRequired?.();
      return;
    }

    if (!replyContent.trim() || !replyTo) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/tools/${toolId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: replyContent.trim(),
          parentId: replyTo
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setReplyContent('');
          setReplyTo(null);
          fetchComments(); // 重新获取评论列表
        }
      } else {
        const errorData = await response.json();
        console.error('Reply submission failed:', errorData.message);
      }
    } catch (error) {
      console.error('Reply submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return t('just_now');
    } else if (diffInHours < 24) {
      return t('hours_ago', { hours: diffInHours });
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      if (diffInDays < 7) {
        return t('days_ago', { days: diffInDays });
      } else {
        return date.toLocaleDateString();
      }
    }
  };

  // 渲染单个评论
  const renderComment = (comment: Comment, isReply = false) => (
    <div key={comment._id} className={`${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''}`}>
      <div className="flex items-start space-x-3">
        <div className="flex-shrink-0">
          {comment.userId.image ? (
            <img
              src={comment.userId.image}
              alt={comment.userId.name}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <FaUser className="w-4 h-4 text-gray-600" />
            </div>
          )}
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-gray-900">
              {comment.userId.name}
            </span>
            <span className="text-xs text-gray-500">
              {formatDate(comment.createdAt)}
            </span>
          </div>
          <p className="mt-1 text-sm text-gray-700">
            {comment.content}
          </p>
          <div className="mt-2 flex items-center space-x-4">
            <button
              className="text-xs text-gray-500 hover:text-blue-600 flex items-center space-x-1"
              onClick={() => setReplyTo(replyTo === comment._id ? null : comment._id)}
            >
              <FaReply className="w-3 h-3" />
              <span>{t('reply')}</span>
            </button>
          </div>
          
          {/* 回复输入框 */}
          {replyTo === comment._id && (
            <div className="mt-3 space-y-2">
              <textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder={session ? t('write_reply') : t('login_to_reply')}
                className="w-full p-2 text-sm border border-gray-300 rounded resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={2}
                maxLength={500}
                disabled={!session}
              />
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">
                  {replyContent.length}/500
                </span>
                <div className="space-x-2">
                  <button
                    onClick={() => {
                      setReplyTo(null);
                      setReplyContent('');
                    }}
                    className="px-3 py-1 text-xs text-gray-600 hover:text-gray-800"
                  >
                    {t('cancel')}
                  </button>
                  <button
                    onClick={handleSubmitReply}
                    disabled={isSubmitting || !replyContent.trim() || !session}
                    className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSubmitting ? t('submitting') : t('submit_reply')}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* 渲染回复 */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4 space-y-4">
          {comment.replies.map(reply => renderComment(reply, true))}
        </div>
      )}
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        {t('comments')} ({comments.length})
      </h3>
      
      {/* 评论输入框 */}
      <div className="space-y-3 mb-6">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder={session ? t('write_comment') : t('login_to_comment')}
          className="w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={4}
          maxLength={1000}
          disabled={!session}
        />
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">
            {newComment.length}/1000
          </span>
          <button
            onClick={handleSubmitComment}
            disabled={isSubmitting || !newComment.trim() || !session}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? t('submitting') : t('submit_comment')}
          </button>
        </div>
      </div>

      {/* 评论列表 */}
      <div className="space-y-6">
        {comments.length === 0 ? (
          <p className="text-gray-500 text-center py-8">
            {t('no_comments')}
          </p>
        ) : (
          comments.map(comment => renderComment(comment))
        )}
      </div>
    </div>
  );
}
