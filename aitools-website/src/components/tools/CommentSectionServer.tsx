import dbConnect from '@/lib/mongodb';
import Comment from '@/models/Comment';
import CommentSectionClient from './CommentSectionClient';

interface CommentSectionServerProps {
  toolId: string;
  onLoginRequired?: () => void;
}

interface CommentData {
  _id: string;
  content: string;
  userId: {
    _id: string;
    name: string;
    email: string;
    image?: string;
  };
  createdAt: string;
  likes: number;
  replies?: CommentData[];
}

async function getComments(toolId: string): Promise<CommentData[]> {
  try {
    await dbConnect();

    // 查找评论并关联用户信息
    const comments = await Comment.find({ 
      toolId, 
      isActive: true,
      parentId: null // 只获取顶级评论
    })
    .populate('userId', 'name email image')
    .sort({ createdAt: -1 })
    .limit(10); // 限制初始加载的评论数量

    // 获取每个评论的回复
    const commentsWithReplies = await Promise.all(
      comments.map(async (comment) => {
        const replies = await Comment.find({
          parentId: comment._id,
          isActive: true
        })
        .populate('userId', 'name email image')
        .sort({ createdAt: 1 });

        return {
          _id: comment._id.toString(),
          content: comment.content,
          userId: {
            _id: comment.userId._id.toString(),
            name: comment.userId.name,
            email: comment.userId.email,
            image: comment.userId.image
          },
          createdAt: comment.createdAt.toISOString(),
          likes: comment.likes || 0,
          replies: replies.map(reply => ({
            _id: reply._id.toString(),
            content: reply.content,
            userId: {
              _id: reply.userId._id.toString(),
              name: reply.userId.name,
              email: reply.userId.email,
              image: reply.userId.image
            },
            createdAt: reply.createdAt.toISOString(),
            likes: reply.likes || 0
          }))
        };
      })
    );

    return commentsWithReplies;
  } catch (error) {
    console.error('Error fetching comments:', error);
    return [];
  }
}

export default async function CommentSectionServer({
  toolId,
  onLoginRequired
}: CommentSectionServerProps) {
  const comments = await getComments(toolId);

  return (
    <CommentSectionClient
      toolId={toolId}
      initialComments={comments}
      onLoginRequired={onLoginRequired}
    />
  );
}
