'use client';

import { useState, useEffect } from 'react';
import { usePathname } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import { useSession } from 'next-auth/react';
import { <PERSON>a<PERSON><PERSON>, <PERSON>aR<PERSON>ly, <PERSON>a<PERSON><PERSON><PERSON>, FaRegHeart } from 'react-icons/fa';
import { Locale } from '@/i18n/config';

interface Comment {
  _id: string;
  content: string;
  userId: {
    _id: string;
    name: string;
    email: string;
    image?: string;
  };
  createdAt: string;
  likes: number;
  replies?: Comment[];
}

interface CommentSectionProps {
  toolId: string;
  onLoginRequired?: () => void;
}

export default function CommentSection({ toolId, onLoginRequired }: CommentSectionProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyTo, setReplyTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const pathname = usePathname();
  const t = useTranslations('comments');

  // Extract current locale from pathname
  const currentLocale = (pathname?.startsWith('/en') ? 'en' : 'zh') as Locale;

  // 获取评论列表
  const fetchComments = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/tools/${toolId}/comments`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setComments(data.data.comments);
        }
      }
    } catch (error) {
      console.error('Failed to fetch comments:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchComments();
  }, [toolId]);

  // 提交评论
  const handleSubmitComment = async () => {
    if (!session) {
      onLoginRequired?.();
      return;
    }

    if (!newComment.trim()) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/tools/${toolId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: newComment.trim()
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setNewComment('');
          fetchComments(); // 重新获取评论列表
        }
      } else {
        const errorData = await response.json();
        console.error('Comment submission failed:', errorData.message);
      }
    } catch (error) {
      console.error('Comment submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // 提交回复
  const handleSubmitReply = async (parentId: string) => {
    if (!session) {
      onLoginRequired?.();
      return;
    }

    if (!replyContent.trim()) return;

    setIsSubmitting(true);
    try {
      const response = await fetch(`/api/tools/${toolId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: replyContent.trim(),
          parentId
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setReplyContent('');
          setReplyTo(null);
          fetchComments(); // 重新获取评论列表
        }
      } else {
        const errorData = await response.json();
        console.error('Reply submission failed:', errorData.message);
      }
    } catch (error) {
      console.error('Reply submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return t('just_now');
    } else if (diffInHours < 24) {
      return t('hours_ago', { hours: diffInHours });
    } else if (diffInHours < 24 * 7) {
      return t('days_ago', { days: Math.floor(diffInHours / 24) });
    } else {
      return date.toLocaleDateString(currentLocale === 'zh' ? 'zh-CN' : 'en-US');
    }
  };

  const CommentItem = ({ comment, isReply = false }: { comment: Comment; isReply?: boolean }) => (
    <div className={`${isReply ? 'ml-8 border-l-2 border-gray-100 pl-4' : ''}`}>
      <div className="flex gap-3">
        <div className="flex-shrink-0">
          {comment.userId.image ? (
            <img
              src={comment.userId.image}
              alt={comment.userId.name}
              className="w-8 h-8 rounded-full"
            />
          ) : (
            <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
              <FaUser className="w-4 h-4 text-gray-600" />
            </div>
          )}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-gray-900">{comment.userId.name}</span>
            <span className="text-sm text-gray-500">{formatDate(comment.createdAt)}</span>
          </div>
          
          <p className="text-gray-700 mb-2">{comment.content}</p>
          
          <div className="flex items-center gap-4">
            {!isReply && (
              <button
                onClick={() => setReplyTo(replyTo === comment._id ? null : comment._id)}
                className="text-sm text-gray-500 hover:text-gray-700 flex items-center gap-1"
              >
                <FaReply className="w-3 h-3" />
                {t('reply')}
              </button>
            )}
          </div>
          
          {/* 回复输入框 */}
          {replyTo === comment._id && (
            <div className="mt-3">
              <textarea
                value={replyContent}
                onChange={(e) => setReplyContent(e.target.value)}
                placeholder={t('write_reply')}
                className="w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                maxLength={1000}
              />
              <div className="flex justify-end gap-2 mt-2">
                <button
                  onClick={() => {
                    setReplyTo(null);
                    setReplyContent('');
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800"
                >
                  {t('cancel')}
                </button>
                <button
                  onClick={() => handleSubmitReply(comment._id)}
                  disabled={isSubmitting || !replyContent.trim()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? t('submitting') : t('send')}
                </button>
              </div>
            </div>
          )}
          
          {/* 回复列表 */}
          {comment.replies && comment.replies.length > 0 && (
            <div className="mt-4 space-y-4">
              {comment.replies.map((reply) => (
                <CommentItem key={reply._id} comment={reply} isReply={true} />
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">
        {t('title', { count: comments.length })}
      </h3>
      
      {/* 评论输入框 */}
      <div className="space-y-3">
        <textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder={session ? t('write_comment') : t('login_to_comment')}
          className="w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          rows={4}
          maxLength={1000}
          disabled={!session}
        />
        <div className="flex justify-between items-center">
          <span className="text-sm text-gray-500">
            {newComment.length}/1000
          </span>
          <button
            onClick={handleSubmitComment}
            disabled={isSubmitting || !newComment.trim() || !session}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? t('submitting') : t('submit_comment')}
          </button>
        </div>
      </div>
      
      {/* 评论列表 */}
      {isLoading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">{t('loading')}</p>
        </div>
      ) : comments.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">{t('no_comments')}</p>
        </div>
      ) : (
        <div className="space-y-6">
          {comments.map((comment) => (
            <CommentItem key={comment._id} comment={comment} />
          ))}
        </div>
      )}
    </div>
  );
}
