'use client';

import React, { useState } from 'react';
import LoginModal from '@/components/auth/LoginModal';

interface ToolDetailInteractionProps {
  children: React.ReactNode;
}

export default function ToolDetailInteraction({ children }: ToolDetailInteractionProps) {
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);

  const handleLoginRequired = () => {
    setIsLoginModalOpen(true);
  };

  // 克隆children并传递回调函数
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, {
        onLoginRequired: handleLoginRequired,
      } as any);
    }
    return child;
  });

  return (
    <>
      {childrenWithProps}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </>
  );
}
