import React from 'react';

interface CharacterCounterProps {
  current: number;
  max: number;
  min?: number;
  className?: string;
}

export default function CharacterCounter({ 
  current, 
  max, 
  min = 0, 
  className = '' 
}: CharacterCounterProps) {
  const isOverLimit = current > max;
  const isUnderMin = current < min;
  const percentage = (current / max) * 100;
  
  // 确定颜色
  let textColor = 'text-gray-500';
  if (isOverLimit) {
    textColor = 'text-red-500';
  } else if (isUnderMin) {
    textColor = 'text-orange-500';
  } else if (percentage > 80) {
    textColor = 'text-yellow-600';
  } else {
    textColor = 'text-green-600';
  }

  return (
    <div className={`text-sm ${textColor} ${className}`}>
      <span className="font-medium">{current}</span>
      <span className="text-gray-400">/{max}</span>
      {min > 0 && current < min && (
        <span className="ml-2 text-orange-500">
          (At least {min} characters)
        </span>
      )}
      {isOverLimit && (
        <span className="ml-2 text-red-500">
          (Exceeded by {current - max} characters)
        </span>
      )}
    </div>
  );
}
