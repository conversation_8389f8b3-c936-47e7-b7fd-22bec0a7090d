import { remark } from 'remark';
import html from 'remark-html';
import remarkGfm from 'remark-gfm';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export default async function ServerMarkdownRenderer({
  content,
  className = '',
}: MarkdownRendererProps) {
  const processedContent = await remark()
    .use(remarkGfm)
    .use(html, { sanitize: false }) // 注意这里不要开启 sanitize，保留 GFM 输出
    .process(content);

  const contentHtml = processedContent.toString();

  return (
    <div
      className={`prose prose-slate max-w-none ${className}`}
      style={{
        listStylePosition: 'inside',
        paddingLeft: '1.25rem',
      }}
      dangerouslySetInnerHTML={{ __html: contentHtml }}
    />
  );
}
