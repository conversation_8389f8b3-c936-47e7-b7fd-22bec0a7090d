'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { usePathname } from 'next/navigation';
import { FaGlobe } from 'react-icons/fa';
import { locales, localeNames, type Locale } from '@/i18n/config';

interface LanguageSwitcherClientProps {
  currentLocale: Locale;
}

export default function LanguageSwitcherClient({ currentLocale }: LanguageSwitcherClientProps) {
  const [isOpen, setIsOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  const switchLanguage = (newLocale: Locale) => {
    // 解析当前路径，移除语言前缀
    const segments = pathname?.split('/')?.filter(Boolean) || [];
    
    // 如果第一段是语言代码，则移除它
    if (segments.length > 0 && locales.includes(segments[0] as Locale)) {
      segments.shift();
    }
    
    // 构建新路径
    const newPath = `/${newLocale}${segments.length > 0 ? '/' + segments.join('/') : ''}`;
    
    // 导航到新路径
    router.replace(newPath);
    setIsOpen(false);
  };

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
      >
        <FaGlobe className="w-4 h-4" />
        <span className="text-sm">{localeNames[currentLocale]}</span>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50">
          {locales.map((locale) => (
            <button
              key={locale}
              onClick={() => switchLanguage(locale)}
              className={`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${
                locale === currentLocale ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
              }`}
            >
              {localeNames[locale]}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}