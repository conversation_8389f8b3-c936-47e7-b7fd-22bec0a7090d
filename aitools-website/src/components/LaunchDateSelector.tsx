'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Calendar, Clock, CreditCard, CheckCircle, Tag } from 'lucide-react';
import { LAUNCH_OPTIONS, formatPrice, formatOriginalPrice, PRICING_CONFIG, hasActivePromotion } from '@/constants/pricing';


// 使用统一的发布选项配置
const launchOptions = LAUNCH_OPTIONS;

interface LaunchDateSelectorProps {
  toolId?: string;
  currentOption?: 'free' | 'paid';
  currentDate?: string;
  isEditing?: boolean;
  onSubmit: (option: 'free' | 'paid', date: string) => Promise<void>;
  isSubmitting: boolean;
  error?: string;
  hasPaidOrder?: boolean;
}

export default function LaunchDateSelector({
  currentOption = 'paid',
  currentDate,
  isEditing = false,
  onSubmit,
  isSubmitting,
  error,
  hasPaidOrder = false
}: LaunchDateSelectorProps) {
  // 如果已付费，强制设置为 paid 选项
  const [selectedOption, setSelectedOption] = useState<'free' | 'paid'>(
    hasPaidOrder ? 'paid' : currentOption
  );
  const [selectedDate, setSelectedDate] = useState<string>('');

  const t = useTranslations('launch');
  const locale = useLocale();

  // 计算折扣百分比
  const getDiscountPercentage = () => {
    if (!hasActivePromotion()) return 0;
    
    const originalPrice = PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice;
    const currentPrice = PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice;
    const discount = Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
    return discount;
  };

  // 获取最早可选择的免费日期（一个月后）
  const getMinFreeDate = () => {
    const date = new Date();
    date.setMonth(date.getMonth() + 1);
    return date.toISOString().split('T')[0];
  };

  // 获取最早可选择的付费日期（明天）
  const getMinPaidDate = () => {
    const date = new Date();
    date.setDate(date.getDate() + 1);
    return date.toISOString().split('T')[0];
  };

  useEffect(() => {
    if (currentDate) {
      setSelectedDate(currentDate);
    } else {
      // 根据选择的选项设置默认日期
      if (selectedOption === 'free') {
        setSelectedDate(getMinFreeDate());
      } else {
        setSelectedDate(getMinPaidDate());
      }
    }
  }, [selectedOption, currentDate]);

  const handleOptionChange = (option: 'free' | 'paid') => {
    // 如果已经付费，不允许切换选项
    if (hasPaidOrder) {
      return;
    }

    setSelectedOption(option);
    // 当切换选项时，重新设置日期
    if (option === 'free') {
      setSelectedDate(getMinFreeDate());
    } else {
      setSelectedDate(getMinPaidDate());
    }
  };

  const handleSubmit = async () => {
    if (!selectedDate) {
      return;
    }
    await onSubmit(selectedOption, selectedDate);
  };

  return (
    <div className="space-y-8">
      {/* 选项选择 - 如果已付费则不显示 */}
      {!hasPaidOrder && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {isEditing ? t('select_plan') : t('select_option')}
          </h3>
          <div className="grid md:grid-cols-2 gap-6">
            {launchOptions.map((option) => (
              <div
                key={option.id}
                className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all flex flex-col ${
                  selectedOption === option.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                } ${'recommended' in option && option.recommended ? 'ring-2 ring-blue-200' : ''}`}
                onClick={() => handleOptionChange(option.id)}
              >
              {'recommended' in option && option.recommended && (
                <div className="absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  {t('recommended')}
                </div>
              )}
              
              <div className="flex items-center justify-between mb-4 flex-1">
                <div className="flex items-center">
                  {option.id === 'free' ? (
                    <Calendar className="h-6 w-6 text-gray-600 mr-3" />
                  ) : (
                    <CreditCard className="h-6 w-6 text-blue-600 mr-3" />
                  )}
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900">{t(`plans.${option.id}.title`)}</h4>
                    <p className="text-sm text-gray-600">{t(`plans.${option.id}.description`)}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  {option.id === 'paid' && hasActivePromotion() ? (
                    <div className="text-right">
                      {/* 早鸟价标签 */}
                      <div className="inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-semibold mb-3 shadow-lg">
                        <Tag className="h-3 w-3 mr-1" />
                        {t('promotion.early_bird')}
                      </div>
                      {/* 价格展示 */}
                      <div className="space-y-1">
                        <div className="text-sm text-gray-500">
                          {t('promotion.original_price')} <span className="line-through font-medium text-gray-400">{formatOriginalPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice, locale)}</span>
                        </div>
                        <div className="text-3xl font-bold text-gray-900">
                          {formatPrice(option.price, locale)}
                        </div>
                        {/* 折扣信息 */}
                        <div className="inline-flex items-center px-2 py-1 rounded-full bg-red-100 text-red-600 text-xs font-bold">
                          Save {getDiscountPercentage()}%
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-2xl font-bold text-gray-900">
                      {formatPrice(option.price, locale)}
                    </div>
                  )}
                </div>
              </div>
              
              <ul className="space-y-2">
                {option.features.map((_, index) => (
                  <li key={index} className="flex items-center text-sm text-gray-600">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                    {t(`plans.${option.id}.features.${index}`)}
                  </li>
                ))}
              </ul>
              
              <div className="mt-4">
                <input
                  type="radio"
                  name="launchOption"
                  value={option.id}
                  checked={selectedOption === option.id}
                  onChange={() => handleOptionChange(option.id)}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border-2 ${
                  selectedOption === option.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300'
                }`}>
                  {selectedOption === option.id && (
                    <div className="w-2 h-2 bg-white rounded-full mx-auto mt-0.5"></div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      )}

      {/* 已付费用户的提示信息 */}
      {hasPaidOrder && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
            <div>
              <h4 className="text-sm font-medium text-green-800">{t('priority_service_activated_title')}</h4>
              <p className="text-sm text-green-600 mt-1">{t('priority_service_activated_description')}</p>
            </div>
          </div>
        </div>
      )}

      {/* 日期选择 */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Clock className="h-5 w-5 mr-2" />
          {t('select_date')}
        </h3>
        
        <div className="max-w-md">
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            min={hasPaidOrder || selectedOption === 'paid' ? getMinPaidDate() : getMinFreeDate()}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          
          <p className="text-sm text-gray-500 mt-2">
            {hasPaidOrder
              ? t('paid_date_info')
              : selectedOption === 'free'
              ? t('free_date_info')
              : t('paid_date_info')
            }
          </p>
        </div>
      </div>

      {/* 提交按钮 */}
      <div className="text-center">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting || !selectedDate}
          className="bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {selectedOption === 'paid' ? t('processing') : t('saving')}
            </>
          ) : (
            <>
              {hasPaidOrder ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t('save_changes')}
                </>
              ) : selectedOption === 'paid' ? (
                <>
                  <CreditCard className="h-4 w-4 mr-2" />
                  {hasActivePromotion() ? (
                    <span className="flex items-center">
                      {isEditing ? (locale === 'zh' ? '升级并支付 ' : 'Upgrade and Pay ') : (locale === 'zh' ? '立即支付 ' : 'Pay Now ')}
                      <span className="line-through text-blue-200 mx-1 text-sm">
                        {formatOriginalPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice, locale)}
                      </span>
                      <span className="font-bold">
                        {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice, locale)}
                      </span>
                      <span className="ml-2 px-2 py-0.5 bg-amber-400 text-amber-900 text-xs rounded-full font-medium">
                        {t('promotion.early_bird')}
                      </span>
                    </span>
                  ) : (
                    <span>
                      {isEditing ? t('upgrade_and_pay', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice, locale) }) : t('pay_amount', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice, locale) })}
                    </span>
                  )}
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {isEditing ? t('save_changes') : t('confirm_date')}
                </>
              )}
            </>
          )}
        </button>
        
        {error && (
          <p className="text-red-600 text-sm mt-4">{error}</p>
        )}
        
        <p className="text-gray-500 text-sm mt-4">
          {hasPaidOrder
            ? t('changes_effective')
            : selectedOption === 'paid'
            ? t('payment_redirect')
            : isEditing
              ? t('changes_effective')
              : t('review_queue')
          }
        </p>
      </div>
    </div>
  );
}