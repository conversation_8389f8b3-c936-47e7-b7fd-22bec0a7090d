'use client';

import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { Award, Copy, Check, ExternalLink, Loader2, AlertCircle, CheckCircle, Globe } from 'lucide-react';
import FeaturedBadge, { BadgeTheme, generateBadgeEmbedCode } from './FeaturedBadge';

interface BadgeVerificationProps {
  toolId?: string;
  toolWebsite?: string;
  onVerificationSuccess: () => void;
  className?: string;
}

export default function BadgeVerification({
  toolId,
  toolWebsite,
  onVerificationSuccess,
  className = ''
}: BadgeVerificationProps) {
  const [selectedTheme, setSelectedTheme] = useState<BadgeTheme>('light');
  const [copied, setCopied] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [verificationError, setVerificationError] = useState('');

  const t = useTranslations('badge_verification');

  const themes: { value: BadgeTheme; label: string; description: string }[] = [
    { value: 'light', label: t('themes.light.label'), description: t('themes.light.description') },
    { value: 'dark', label: t('themes.dark.label'), description: t('themes.dark.description') },
    { value: 'neutral', label: t('themes.neutral.label'), description: t('themes.neutral.description') }
  ];

  const copyEmbedCode = async () => {
    try {
      const code = generateBadgeEmbedCode(selectedTheme, 'lg'); // 固定使用large尺寸
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const verifyBadge = async () => {
    if (!toolWebsite) {
      setVerificationError(t('errors.no_website'));
      return;
    }

    setIsVerifying(true);
    setVerificationError('');
    setVerificationStatus('idle');

    try {
      const response = await fetch('/api/tools/verify-badge', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          toolId,
          website: toolWebsite,
          // website: 'http://localhost:4000/aiflow',
          expectedText: 'Featured on AITools'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setVerificationStatus('success');
        setTimeout(() => {
          onVerificationSuccess();
        }, 1500);
      } else {
        setVerificationStatus('error');
        setVerificationError(data.error || t('errors.verification_failed'));
      }
    } catch (error) {
      setVerificationStatus('error');
      setVerificationError(t('errors.network_error'));
    } finally {
      setIsVerifying(false);
    }
  };

  return (
    <div className={`bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200 ${className}`}>
      <div className="flex items-center mb-6">
        <Award className="h-6 w-6 text-blue-600 mr-3" />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{t('title')}</h3>
          <p className="text-sm text-gray-600">{t('subtitle')}</p>
        </div>
      </div>

      <div className="space-y-8">
        {/* 步骤1: 选择主题 */}
        <div>
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3">
              1
            </div>
            <h4 className="text-md font-medium text-gray-900">选择Badge主题</h4>
          </div>

          {/* 主题选择 - Tab样式 */}
          <div className="mb-4 ml-11">
            <div className="flex border-b border-gray-200">
              {themes.map((theme) => (
                <button
                  key={theme.value}
                  onClick={() => setSelectedTheme(theme.value)}
                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                    selectedTheme === theme.value
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {theme.label}
                </button>
              ))}
            </div>
          </div>

          {/* Badge预览 */}
          <div className="bg-white rounded-lg p-4 border border-gray-200 mb-4 ml-11">
            <div className="text-sm text-gray-600 mb-2">预览：</div>
            <FeaturedBadge theme={selectedTheme} size="lg" />
          </div>

          {/* 复制按钮 */}
          <div className="ml-11">
            <button
              onClick={copyEmbedCode}
              className="w-full bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2"
            >
              {copied ? (
                <>
                  <Check className="h-4 w-4" />
                  已复制!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4" />
                  复制嵌入代码
                </>
              )}
            </button>
          </div>
        </div>

        {/* 步骤2: 添加到网站 */}
        <div>
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3">
              2
            </div>
            <h4 className="text-md font-medium text-gray-900">添加到您的网站</h4>
          </div>
          <p className="text-sm text-gray-600 ml-11">将复制的代码添加到您网站的首页，通常放在页脚或侧边栏位置。</p>
        </div>

        {/* 步骤3: 验证 */}
        <div>
          <div className="flex items-center mb-4">
            <div className="w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3">
              3
            </div>
            <h4 className="text-md font-medium text-gray-900">验证Badge安装</h4>
          </div>

          {toolWebsite && (
            <div className="bg-white rounded-lg p-4 border border-gray-200 mb-4 ml-11">
              <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                <Globe className="h-4 w-4" />
                您的网站：
              </div>
              <a
                href={toolWebsite}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 flex items-center gap-1"
              >
                {toolWebsite}
                <ExternalLink className="h-3 w-3" />
              </a>
            </div>
          )}

          {/* 验证状态 */}
          {verificationStatus === 'success' && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4 ml-11">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">Badge验证成功！</span>
              </div>
              <p className="text-sm text-green-600 mt-1">您现在可以选择任意发布日期，无需等待一个月。</p>
            </div>
          )}

          {verificationStatus === 'error' && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4 ml-11">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="h-5 w-5" />
                <span className="font-medium">验证失败</span>
              </div>
              <p className="text-sm text-red-600 mt-1">{verificationError}</p>
            </div>
          )}

          <div className="ml-11">
            <button
              onClick={verifyBadge}
              disabled={isVerifying || verificationStatus === 'success'}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors"
            >
              {isVerifying ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  验证中...
                </>
              ) : verificationStatus === 'success' ? (
                <>
                  <CheckCircle className="h-4 w-4" />
                  已验证！
                </>
              ) : (
                '验证Badge'
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
