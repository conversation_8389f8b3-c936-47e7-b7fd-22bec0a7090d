'use client';

import { useState } from 'react';
import { Award, Copy, Check } from 'lucide-react';

export type BadgeTheme = 'light' | 'dark' | 'neutral';

interface FeaturedBadgeProps {
  theme?: BadgeTheme;
  size?: 'sm' | 'md' | 'lg';
  showCopyButton?: boolean;
  toolName?: string;
  className?: string;
}

const themeStyles = {
  light: {
    container: 'bg-white border-2 border-purple-500 text-purple-600',
    icon: 'text-purple-500',
    text: 'text-purple-600',
    accent: 'bg-purple-500'
  },
  dark: {
    container: 'bg-gray-900 border-2 border-yellow-400 text-yellow-400',
    icon: 'text-yellow-400',
    text: 'text-yellow-400',
    accent: 'bg-yellow-400'
  },
  neutral: {
    container: 'bg-gray-50 border-2 border-gray-400 text-gray-700',
    icon: 'text-gray-600',
    text: 'text-gray-700',
    accent: 'bg-gray-400'
  }
};

const sizeStyles = {
  sm: {
    container: 'px-3 py-2 text-xs',
    icon: 'h-3 w-3',
    spacing: 'gap-1.5'
  },
  md: {
    container: 'px-4 py-2.5 text-sm',
    icon: 'h-4 w-4',
    spacing: 'gap-2'
  },
  lg: {
    container: 'px-6 py-3 text-base',
    icon: 'h-5 w-5',
    spacing: 'gap-2.5'
  }
};

export default function FeaturedBadge({ 
  theme = 'light', 
  size = 'md', 
  showCopyButton = false,
  toolName = 'Your Tool',
  className = ''
}: FeaturedBadgeProps) {
  const [copied, setCopied] = useState(false);
  
  const themeStyle = themeStyles[theme];
  const sizeStyle = sizeStyles[size];

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(generateBadgeEmbedCode(theme, size));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  return (
    <div className={`inline-flex items-center ${className}`}>
      <a 
        href="https://aitools.pub" 
        target="_blank" 
        rel="noopener noreferrer"
        className={`
          inline-flex items-center rounded-lg font-semibold transition-all duration-200 hover:scale-105 hover:shadow-lg
          ${themeStyle.container} ${sizeStyle.container} ${sizeStyle.spacing}
        `}
      >
        <Award className={`${themeStyle.icon} ${sizeStyle.icon}`} />
        <span className={themeStyle.text}>Featured on AITools</span>
      </a>
      
      {showCopyButton && (
        <button
          onClick={copyToClipboard}
          className="ml-2 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
          title="Copy embed code"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4" />
          )}
        </button>
      )}
    </div>
  );
}

export function generateBadgeEmbedCode(theme: BadgeTheme = 'light', size: 'sm' | 'md' | 'lg' = 'md'): string {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://aitools.pub';
  
  return `<a href="${baseUrl}" target="_blank" rel="noopener noreferrer" style="display: inline-flex; align-items: center; padding: ${size === 'sm' ? '8px 12px' : size === 'md' ? '10px 16px' : '12px 24px'}; border-radius: 8px; text-decoration: none; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-weight: 600; font-size: ${size === 'sm' ? '12px' : size === 'md' ? '14px' : '16px'}; gap: ${size === 'sm' ? '6px' : '8px'}; ${
    theme === 'light' 
      ? 'background-color: #ffffff; border: 2px solid #9333ea; color: #9333ea;'
      : theme === 'dark'
      ? 'background-color: #111827; border: 2px solid #fbbf24; color: #fbbf24;'
      : 'background-color: #f9fafb; border: 2px solid #6b7280; color: #374151;'
  } transition: all 0.2s ease;">
  <svg width="${size === 'sm' ? '12' : size === 'md' ? '16' : '20'}" height="${size === 'sm' ? '12' : size === 'md' ? '16' : '20'}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"/>
    <circle cx="12" cy="8" r="6"/>
  </svg>
  Featured on AITools
</a>`;
}



{/* <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-award-icon lucide-award">

</svg> */}