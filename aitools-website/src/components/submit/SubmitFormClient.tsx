'use client';

import React, { useState, Fragment, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from '@/i18n/routing';
import { useTranslations } from 'next-intl';
import LoadingSpinner from '@/components/LoadingSpinner';

import SuccessMessage from '@/components/SuccessMessage';
import LoginModal from '@/components/auth/LoginModal';
import { TOOL_PRICING_FORM_OPTIONS } from '@/constants/pricing';
import { Tool } from '@/lib/api';
import {
  Upload,
  Link as LinkIcon,
  Info,
  ArrowLeft,
  Sparkles
} from 'lucide-react';
import { Link } from '@/i18n/routing';
import { MAX_TAGS_COUNT } from '@/constants/tags';
import TagSelector from '@/components/TagSelector';
import OptimizedImage, { ImageSizes, ResponsiveSizes } from '../ui/OptimizedImage';
import { Locale } from '@/i18n/config';
import { FIELD_LIMITS, validateFieldLength } from '@/constants/upload-config';
import CharacterCounter from '@/components/ui/CharacterCounter';
import MarkdownEditor from '@/components/MarkdownEditor';

interface CategoryOption {
  value: string;
  label: string;
}

interface SubmitFormClientProps {
  categoryOptions: CategoryOption[];
  // 编辑模式相关props
  isEditMode?: boolean;
  toolId?: string;
  initialTool?: Tool;
}

export default function SubmitFormClient({
  categoryOptions,
  isEditMode = false,
  toolId,
  initialTool,
}: SubmitFormClientProps) {
  // const t = getTranslations({ locale, namespace: 'submit' });
  const t = useTranslations('submit');
  const { data: session, status } = useSession();
  const router = useRouter();

  const [tool, setTool] = useState<Tool | null>(initialTool || null);
  const [loading, setLoading] = useState(isEditMode && !initialTool);
  const [formData, setFormData] = useState({
    name: '',
    tagline: '',
    description: '',
    website: '',
    logoFile: null as File | null,
    category: '',
    tags: [] as string[],
    pricing: ''
  });

  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [logoUrl, setLogoUrl] = useState<string>('');
  const [uploadingLogo, setUploadingLogo] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [submitMessage, setSubmitMessage] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isLoginModalOpen, setIsLoginModalOpen] = useState(false);
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);

  // 获取工具信息（编辑模式）
  useEffect(() => {
    if (!isEditMode || !toolId || initialTool) return;

    const fetchToolInfo = async () => {
      try {
        const response = await fetch(`/api/tools/${toolId}`);
        const data = await response.json();

        if (data.success) {
          const toolData = data.data;
          setTool(toolData);

          // 填充表单数据
          setFormData({
            name: toolData.name || '',
            tagline: toolData.tagline || '',
            description: toolData.description || '',
            website: toolData.website || '',
            logoFile: null,
            category: toolData.category || '',
            tags: toolData.tags || [],
            pricing: toolData.pricing || ''
          });

          setLogoUrl(toolData.logo || '');
          setLogoPreview(toolData.logo || '');
        } else {
          setSubmitStatus('error');
          setSubmitMessage(data.message || '获取工具信息失败');
        }
      } catch (error) {
        console.error('获取工具信息失败:', error);
        setSubmitStatus('error');
        setSubmitMessage('网络错误，请重试');
      } finally {
        setLoading(false);
      }
    };

    if (session) {
      fetchToolInfo();
    } else if (status !== 'loading') {
      setLoading(false);
    }
  }, [toolId, session, status, isEditMode, initialTool]);

  // 初始化表单数据（编辑模式）
  useEffect(() => {
    if (isEditMode && initialTool) {
      setTool(initialTool);
      setFormData({
        name: initialTool.name || '',
        tagline: initialTool.tagline || '',
        description: initialTool.description || '',
        website: initialTool.website || '',
        logoFile: null,
        category: initialTool.category || '',
        tags: initialTool.tags || [],
        pricing: initialTool.pricing || ''
      });
      setLogoUrl(initialTool.logo || '');
      setLogoPreview(initialTool.logo || '');
      setLoading(false);
    }
  }, [isEditMode, initialTool]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        logoFile: file
      }));

      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        setLogoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleTagsChange = (selectedTags: string[]) => {
    setFormData(prev => ({
      ...prev,
      tags: selectedTags
    }));
  };

  // AI生成表单信息
  const handleGenerateAI = async () => {
    if (!formData.website.trim()) {
      setErrors(prev => ({ ...prev, website: t('form.website_url_invalid') }));
      return;
    }

    // 验证URL格式
    try {
      new URL(formData.website);
    } catch (error) {
      setErrors(prev => ({ ...prev, website: t('form.website_url_invalid') }));
      return;
    }

    setIsGeneratingAI(true);
    setErrors(prev => ({ ...prev, website: '' }));

    try {
      const response = await fetch('/api/ai/generate-product-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ website: formData.website }),
      });

      const data = await response.json();

      if (data.success) {
        // 自动填写表单
        setFormData(prev => ({
          ...prev,
          name: data.data.name || prev.name,
          tagline: data.data.tagline || prev.tagline,
          description: data.data.description || prev.description,
          category: data.data.category || prev.category,
          tags: data.data.tags && data.data.tags.length > 0 ? data.data.tags : prev.tags,
        }));

        // 清除相关错误
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.name;
          delete newErrors.tagline;
          delete newErrors.description;
          delete newErrors.category;
          delete newErrors.tags;
          return newErrors;
        });
      } else {
        setErrors(prev => ({ ...prev, website: data.error || 'AI generation failed, please try again' }));
      }
        } catch (error) {
      console.error('AI generation error:', error);
      setErrors(prev => ({ ...prev, website: 'Network error, please try again' }));
        } finally {
      setIsGeneratingAI(false);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = t('form.tool_name') + ' is required';
    if (!formData.description.trim()) newErrors.description = t('form.description') + ' is required';
    if (!formData.website.trim()) newErrors.website = t('form.website_url') + ' is required';
    if (!formData.category) newErrors.category = t('form.category') + ' is required';
    if (!formData.pricing) newErrors.pricing = t('form.pricing_model') + ' is required';

    // URL validation
    if (formData.website && !formData.website.match(/^https?:\/\/.+/)) {
      newErrors.website = t('form.website_url_placeholder');
    }

    // 验证 logo（编辑模式下如果已有logo则不强制要求新上传）
    if (!isEditMode && !formData.logoFile) {
      newErrors.logo = t('form.logo_required');
    }

    if (formData.tags.length === 0) {
      newErrors.tags = t('form.tags_placeholder');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!session?.user?.email) {
      setIsLoginModalOpen(true);
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // 处理 logo 上传
      let finalLogoUrl = logoUrl;
      if (formData.logoFile) {
        const logoFormData = new FormData();
        logoFormData.append('logo', formData.logoFile);

        const uploadResponse = await fetch('/api/upload/logo', {
          method: 'POST',
          body: logoFormData,
        });

        if (uploadResponse.ok) {
          const uploadResult = await uploadResponse.json();
          finalLogoUrl = uploadResult.data.url;
        } else {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.message || 'Logo upload failed');
        }
      }

      if (isEditMode && toolId) {
        // 编辑模式：更新工具
        const updateData = {
          name: formData.name,
          tagline: formData.tagline,
          description: formData.description,
          website: formData.website,
          logo: finalLogoUrl || undefined,
          category: formData.category,
          tags: formData.tags,
          pricing: formData.pricing
        };

        const response = await fetch(`/api/tools/${toolId}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(updateData),
        });

        const data = await response.json();

        if (data.success) {
          setSubmitStatus('success');
          setSubmitMessage('工具信息更新成功！');
          // 跳转回提交的工具列表
          setTimeout(() => {
            router.push('/profile/submitted');
          }, 2000);
        } else {
          setSubmitStatus('error');
          setSubmitMessage(data.error || 'Update failed, please retry');
        }
      } else {
        // 新建模式：提交工具
        const submitData = {
          name: formData.name,
          tagline: formData.tagline,
          description: formData.description,
          website: formData.website,
          logo: finalLogoUrl,
          category: formData.category,
          tags: formData.tags,
          pricing: formData.pricing,
        };

        const response = await fetch('/api/tools/submit', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(submitData),
        });

        if (response.ok) {
          const result = await response.json();
          setSubmitStatus('success');
          setSubmitMessage(t('form.success_message'));

          // 重定向到新的工具信息提交成功页面
          setTimeout(() => {
            router.push(`/submit/tool-info-success?toolId=${result.data.toolId}`);
          }, 500);
        } else {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Submission failed');
        }
      }
    } catch (error) {
      console.error('Submit error:', error);
      setSubmitStatus('error');
      setSubmitMessage((error as Error).message + '. ' +t('form.error_message'));
    } finally {
      setIsSubmitting(false);
    }
  };

  // 加载状态
  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // 未登录状态
  if (!session) {
    return (
      <Fragment>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {t('auth.login_required')}
            </h1>
            <p className="text-gray-600 mb-6">
              {t('auth.login_to_submit')}
            </p>
            <button
              onClick={() => setIsLoginModalOpen(true)}
              className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
            >
              {t('auth.login')}
            </button>
          </div>
        </div>
        <LoginModal
          isOpen={isLoginModalOpen}
          onClose={() => setIsLoginModalOpen(false)}
        />
      </Fragment>
    );
  }

  // 编辑模式下工具不存在
  if (isEditMode && !tool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Tool not found</h1>
          <p className="text-gray-600 mb-6">The tool you want to edit does not exist or has been deleted.</p>
          <Link
            href="/profile/submitted"
            className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
          >
            Back to Tools
          </Link>
        </div>
      </div>
    );
  }

  if (submitStatus === 'success') {
    return (
      <div className="max-w-2xl mx-auto">
        <SuccessMessage message={submitMessage || t('form.success_message')} />
      </div>
    );
  }

  return (
    <Fragment>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        {isEditMode && (
          <div className="mb-8">
            <Link
              href="/profile/submitted"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 mb-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Tools
            </Link>
            <h1 className="text-3xl font-bold text-gray-900">Edit Tool</h1>
            <p className="text-gray-600 mt-2">
              Update your tool information to help more users understand your product.
            </p>
          </div>
        )}

        {!isEditMode && (
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-blue-100 rounded-full">
                <Upload className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {t('title')}
            </h1>
            <p className="text-lg text-gray-600">
              {t('subtitle')}
            </p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="max-w-4xl mx-auto space-y-8">
          {/* 基本信息 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6 flex items-center">
              <Info className="h-5 w-5 mr-2 text-blue-600" />
              {t('form.basic_info')}
            </h2>

            {/* 官方网站 */}
            <div className="mb-6">
              <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.website_url')} <span className="text-red-500">*</span>
              </label>
              <div className="flex gap-2">
                <div className="relative flex-1">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <LinkIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  {
                    !isEditMode &&
                    <input
                      type="url"
                      id="website"
                      name="website"
                      value={formData.website}
                      onChange={handleInputChange}
                      placeholder={isEditMode ? "https://example.com" : t('form.website_url_placeholder')}
                      maxLength={FIELD_LIMITS.WEBSITE_URL.max}
                      className={`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.website ? 'border-red-300' : 'border-gray-300'
                        }`}
                      required
                    />
                  }
                  {
                    isEditMode && <div className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600">
                      {formData.website}
                    </div>
                  }
                </div>
                {/* AI生成按钮 - 仅在非编辑模式显示 */}
                {!isEditMode && (
                  <button
                    type="button"
                    onClick={handleGenerateAI}
                    disabled={isGeneratingAI || !formData.website.trim()}
                    className="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-sm font-medium rounded-md hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2 whitespace-nowrap"
                  >
                    {isGeneratingAI ? (
                      <>
                        <LoadingSpinner size="sm" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-4 w-4" />
                        AI 表单信息生成
                      </>
                    )}
                  </button>
                )}
              </div>
              <div className="mt-1 flex justify-between items-center">
                {!isEditMode && (
                  <CharacterCounter
                    current={formData.website.length}
                    max={FIELD_LIMITS.WEBSITE_URL.max}
                    min={FIELD_LIMITS.WEBSITE_URL.min}
                  />
                )}
                {errors.website && <span className="text-red-500 text-sm">{errors.website}</span>}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 工具名称 */}
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.tool_name')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder={t('form.tool_name_placeholder')}
                  maxLength={FIELD_LIMITS.TOOL_NAME.max}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                />
                <div className="mt-1 flex justify-between items-center">
                  <CharacterCounter
                    current={formData.name.length}
                    max={FIELD_LIMITS.TOOL_NAME.max}
                    min={FIELD_LIMITS.TOOL_NAME.min}
                  />
                  {errors.name && <span className="text-red-500 text-sm">{errors.name}</span>}
                </div>
              </div>

              {/* 工具标语 */}
              <div>
                <label htmlFor="tagline" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.tagline')} <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="tagline"
                  name="tagline"
                  value={formData.tagline}
                  onChange={handleInputChange}
                  placeholder={t('form.tagline_placeholder')}
                  maxLength={FIELD_LIMITS.TAGLINE.max}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
                <div className="mt-1 flex justify-between items-center">
                  <CharacterCounter
                    current={formData.tagline.length}
                    max={FIELD_LIMITS.TAGLINE.max}
                    min={FIELD_LIMITS.TAGLINE.min}
                  />
                  {errors.tagline && <span className="text-red-500 text-sm">{errors.tagline}</span>}
                </div>
              </div>
            </div>

            {/* 详细描述 */}
            <div className="mt-6">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.description')} <span className="text-red-500">*</span>
              </label>
              <MarkdownEditor
                value={formData.description}
                onChange={(value) => setFormData(prev => ({ ...prev, description: value }))}
                placeholder={t('form.description_placeholder')}
                maxLength={FIELD_LIMITS.DESCRIPTION.max}
                error={errors.description}
                height={200}
              />
            </div>

            {/* Logo 上传 */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.logo_upload')} {!isEditMode && <span className="text-red-500">*</span>}
              </label>
              <div className="flex items-start space-x-6">
                <div className="flex-1">
                  {
                    // isEditMode ? (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleLogoChange(e);
                          }
                        }}
                        className="hidden"
                        id="logo-upload"
                        required={!isEditMode}
                      />
                      <label htmlFor="logo-upload" className="cursor-pointer">
                        <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {uploadingLogo ? t('form.uploading') : t('form.click_to_upload')}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">
                          {t('form.logo_upload_hint')}
                        </p>
                      </label>
                    </div>
                    // ) : (
                    //   <div className="relative">
                    //     <input
                    //       type="file"
                    //       id="logo"
                    //       name="logo"
                    //       accept="image/*"
                    //       onChange={handleLogoChange}
                    //       className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    //       required={!isEditMode}
                    //     />
                    //     <p className="mt-1 text-sm text-gray-500">
                    //       {t('form.logo_upload_hint')}
                    //     </p>
                    //   </div>
                    // )
                  }
                  {errors.logo && <p className="text-red-600 text-sm mt-1">{errors.logo}</p>}
                </div>
                {/* {logoPreview && (
                  <div className="flex-shrink-0">
                    <div className={`border border-gray-300 rounded-md overflow-hidden ${'w-24 h-24'}`}>
                      <img
                        src={logoPreview}
                        alt={t('form.logo_preview')}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                )} */}
                {
                  logoPreview && <OptimizedImage
                    alt={"app logo"}
                    src={logoPreview}
                    width={ImageSizes.toolLogo.width}
                    height={ImageSizes.toolLogo.height}
                    className="rounded-lg object-cover"
                    sizes={ResponsiveSizes.toolLogo}
                    placeholder="blur"
                  />
                }
              </div>
            </div>
          </div>

          {/* 分类和定价 */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              {t('form.category_and_pricing')}
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 工具分类 */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.category')} <span className="text-red-500">*</span>
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required
                >
                  <option value="">{t('form.category_placeholder')}</option>
                  {categoryOptions.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* 价格模式 */}
              <div>
                <label htmlFor="pricing" className="block text-sm font-medium text-gray-700 mb-2">
                  {t('form.pricing_model')} <span className="text-red-500">*</span>
                </label>
                <select
                  id="pricing"
                  name="pricing"
                  value={formData.pricing}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${errors.pricing ? 'border-red-300' : 'border-gray-300'
                    }`}
                  required
                >
                  <option value="">{t('form.pricing_placeholder')}</option>
                  {TOOL_PRICING_FORM_OPTIONS.map((option) => (
                    <option key={option.value} value={option.value}>
                      {t(`form.${option.value}`)}
                    </option>
                  ))}
                </select>
                {errors.pricing && <p className="text-red-600 text-sm mt-1">{errors.pricing}</p>}
              </div>
            </div>

            {/* 选择标签 */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('form.tags')} <span className="text-red-500">*</span>
              </label>
              <TagSelector
                selectedTags={formData.tags}
                onTagsChange={handleTagsChange}
                maxTags={MAX_TAGS_COUNT}
                placeholder={t('form.tags_placeholder')}
              />
              {errors.tags && <p className="text-red-600 text-sm mt-1">{errors.tags}</p>}
            </div>
          </div>

          {/* 提交指南 - 仅在新建模式显示 */}
          {!isEditMode && (
            <div className="bg-blue-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-900 mb-4">
                {t('form.guidelines_title')}
              </h3>
              <ul className="space-y-2 text-blue-800">
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                  {t('form.guideline_1')}
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                  {t('form.guideline_2')}
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                  {t('form.guideline_3')}
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                  {t('form.guideline_4')}
                </li>
                <li className="flex items-start">
                  <span className="flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"></span>
                  {t('form.guideline_5')}
                </li>
              </ul>
            </div>
          )}

          {/* 提交按钮 */}
          <div className={isEditMode ? "flex justify-end" : "flex justify-center"}>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ${isEditMode ? 'px-8 py-3 text-base' : 'px-8 py-3 text-base'
                }`}
            >
              {isSubmitting ? (
                <Fragment>
                  <LoadingSpinner size="sm" className="mr-2" />
                  {t('form.submitting')}
                </Fragment>
              ) : (
                <Fragment>
                  <Upload className="h-5 w-5 mr-2" />
                  {t('form.submit_button')}
                </Fragment>
              )}
            </button>
          </div>
        </form>

        {/* Status Messages */}
        {submitStatus === 'error' && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800">{submitMessage}</p>
          </div>
        )}
      </div>

      {/* 登录模态框 */}
      <LoginModal
        isOpen={isLoginModalOpen}
        onClose={() => setIsLoginModalOpen(false)}
      />
    </Fragment>
  );
}
