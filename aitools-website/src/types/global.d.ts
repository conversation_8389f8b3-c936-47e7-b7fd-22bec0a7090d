import mongoose from 'mongoose';
import { DefaultSession, DefaultUser } from 'next-auth';

declare global {
  var mongoose: {
    conn: typeof mongoose | null;
    promise: Promise<typeof mongoose> | null;
  };
}

// Extend NextAuth types to include role property
declare module 'next-auth' {
  interface Session extends DefaultSession {
    user?: {
      id?: string;
      role?: string;
    } & DefaultSession['user'];
  }

  interface User extends DefaultUser {
    role?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    userId?: string;
    role?: string;
  }
}

export {};

// Tool related types
export interface ToolFilters {
  category?: string;
  tags?: string[];
  pricing?: 'free' | 'freemium' | 'paid';
  search?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface SortParams {
  sortBy: 'newest' | 'popular' | 'name' | 'views' | 'likes';
  order: 'asc' | 'desc';
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}
