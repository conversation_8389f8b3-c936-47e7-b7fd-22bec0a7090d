# 上传配置和字段长度限制实现

## 概述

本次实现为AI工具网站添加了两个主要功能：
1. **字段长度限制配置和显示** - 为工具提交表单的各个字段设置最大长度限制，并在前端显示字符计数
2. **可配置的上传目录管理** - 支持通过环境变量配置上传目录，并提供统一的文件访问API

## 实现的功能

### 1. 字段长度限制

#### 配置文件
- **文件**: `src/constants/upload-config.ts`
- **功能**: 集中管理所有表单字段的长度限制
- **字段配置**:
  - 工具名称: 1-100 字符
  - 标语: 0-200 字符  
  - 描述: 10-500 字符
  - 长描述: 0-2000 字符
  - 网站URL: 0-500 字符
  - 定价详情: 0-500 字符

#### 后端验证
- **Tool模型** (`src/models/Tool.ts`): 使用配置文件中的限制替换硬编码值
- **API验证** (`src/app/api/tools/submit/route.ts`, `src/app/api/tools/[id]/route.ts`): 添加字段长度验证

#### 前端显示
- **表单组件** (`src/components/submit/SubmitFormClient.tsx`): 添加实时字符计数器
- **字符计数器** (`src/components/ui/CharacterCounter.tsx`): 新建组件显示当前字符数/最大限制
- **视觉反馈**: 根据字符数量显示不同颜色（绿色/黄色/红色）

### 2. 可配置上传目录

#### 环境变量配置
- **变量**: `UPLOAD_BASE_DIR` 
- **默认值**: `public/images`
- **示例**: 设置为 `/var/uploads` 可将文件保存到自定义目录

#### 上传API更新
- **Logo上传** (`src/app/api/upload/logo/route.ts`): 使用配置的上传路径
- **文件验证**: 统一的文件类型和大小验证

#### 静态文件服务
- **API端点**: `/api/uploads/[...path]`
- **功能**: 提供上传文件的HTTP访问
- **安全性**: 防止路径遍历攻击
- **缓存**: 设置1年缓存头优化性能

## 文件结构

```
src/
├── constants/
│   └── upload-config.ts          # 配置文件
├── models/
│   └── Tool.ts                   # 更新的模型
├── app/api/
│   ├── tools/
│   │   ├── submit/route.ts       # 提交API验证
│   │   └── [id]/route.ts         # 编辑API验证
│   ├── upload/
│   │   └── logo/route.ts         # Logo上传API
│   └── uploads/
│       └── [...path]/route.ts    # 静态文件服务
├── components/
│   ├── submit/
│   │   └── SubmitFormClient.tsx  # 更新的表单
│   └── ui/
│       └── CharacterCounter.tsx  # 字符计数器
└── .env.example                  # 环境变量示例
```

## 使用方法

### 配置上传目录

在 `.env.local` 文件中设置：
```bash
# 使用默认目录 (public/images)
# UPLOAD_BASE_DIR=

# 使用自定义目录
UPLOAD_BASE_DIR=/var/uploads
```

### 访问上传的文件

上传的文件可以通过以下方式访问：
- **API方式**: `/api/uploads/logos/filename.png`
- **直接访问** (如果在public目录): `/images/logos/filename.png`

### 字段长度验证

前端会自动显示字符计数，后端API会验证字段长度：
- 超出限制的提交会被拒绝
- 前端实时显示字符数和限制
- 颜色编码提供视觉反馈

## 测试

运行测试脚本验证配置：
```bash
node test-upload-config.js
```

## 注意事项

1. **向后兼容**: 现有的硬编码路径仍然有效
2. **安全性**: 文件访问API包含路径遍历保护
3. **性能**: 静态文件设置了长期缓存
4. **扩展性**: 配置文件易于添加新的字段限制

## 下一步建议

1. 添加更多文件类型支持
2. 实现图片压缩和优化
3. 添加文件删除API
4. 实现批量上传功能
