{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_256bb82d._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__abf7a29f._.js", "server/edge/chunks/edge-wrapper_80fb5899.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "483dcebf864cb4cccf8a524382f17965", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "d793ebf89f477976f84ad450b18e8671e634605992b9a9ee28657ca8cfca52db", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "184b52f15225314c1012e5ca63124c01bed0d836d3de24c5ed910769c7f0d4d8"}}}, "sortedMiddleware": ["/"], "functions": {}}