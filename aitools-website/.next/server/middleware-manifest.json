{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "p7vi5DasklxnybZ1I5Lm5", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pbGO3VwpkSvUolI25fpFPUUkncDK/RUErZt9oTegUAc=", "__NEXT_PREVIEW_MODE_ID": "fb464e465f013325fee07a597ae6ef52", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "24be611ada83bfc02174dbe93ea3d2439ff6c1692c86a65d3a5702252cce2dbd", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "96767744cddd11b47c904cbf5d69d55d258ff30fe8a80b170302486d9dda3af8"}}}, "functions": {}, "sortedMiddleware": ["/"]}