{"/_not-found/page": "app/_not-found/page.js", "/api/admin/stats/route": "app/api/admin/stats/route.js", "/api/admin/tools/[id]/approve/route": "app/api/admin/tools/[id]/approve/route.js", "/api/admin/tools/[id]/reject/route": "app/api/admin/tools/[id]/reject/route.js", "/api/admin/tools/route": "app/api/admin/tools/route.js", "/api/ai/generate-product-info/route": "app/api/ai/generate-product-info/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/auth/send-code/route": "app/api/auth/send-code/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/orders/[id]/pay/route": "app/api/orders/[id]/pay/route.js", "/api/orders/[id]/route": "app/api/orders/[id]/route.js", "/api/stripe/create-payment-intent/route": "app/api/stripe/create-payment-intent/route.js", "/api/stripe/webhook/route": "app/api/stripe/webhook/route.js", "/api/test/create-payment-intent/route": "app/api/test/create-payment-intent/route.js", "/api/tools/[id]/comments/route": "app/api/tools/[id]/comments/route.js", "/api/tools/[id]/launch-date/route": "app/api/tools/[id]/launch-date/route.js", "/api/tools/[id]/like/route": "app/api/tools/[id]/like/route.js", "/api/tools/[id]/route": "app/api/tools/[id]/route.js", "/api/tools/publish/route": "app/api/tools/publish/route.js", "/api/tools/route": "app/api/tools/route.js", "/api/tools/submit/route": "app/api/tools/submit/route.js", "/api/upload/avatar/route": "app/api/upload/avatar/route.js", "/api/upload/logo/route": "app/api/upload/logo/route.js", "/api/uploads/[...path]/route": "app/api/uploads/[...path]/route.js", "/api/user/liked-tools/route": "app/api/user/liked-tools/route.js", "/api/user/tools/route": "app/api/user/tools/route.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js", "/[locale]/admin/dashboard/page": "app/[locale]/admin/dashboard/page.js", "/[locale]/admin/page": "app/[locale]/admin/page.js", "/[locale]/admin/tools/[id]/page": "app/[locale]/admin/tools/[id]/page.js", "/[locale]/categories/[slug]/page": "app/[locale]/categories/[slug]/page.js", "/[locale]/dashboard/page": "app/[locale]/dashboard/page.js", "/[locale]/demo-features/page": "app/[locale]/demo-features/page.js", "/[locale]/payment/checkout/page": "app/[locale]/payment/checkout/page.js", "/[locale]/profile/liked/page": "app/[locale]/profile/liked/page.js", "/[locale]/profile/page": "app/[locale]/profile/page.js", "/[locale]/profile/submitted/page": "app/[locale]/profile/submitted/page.js", "/[locale]/search/page": "app/[locale]/search/page.js", "/[locale]/settings/page": "app/[locale]/settings/page.js", "/[locale]/submit/edit/[toolId]/page": "app/[locale]/submit/edit/[toolId]/page.js", "/[locale]/submit/launch-date-success/page": "app/[locale]/submit/launch-date-success/page.js", "/[locale]/submit/launch-date/[toolId]/page": "app/[locale]/submit/launch-date/[toolId]/page.js", "/[locale]/submit/page": "app/[locale]/submit/page.js", "/[locale]/submit/success/page": "app/[locale]/submit/success/page.js", "/[locale]/submit/tool-info-success/page": "app/[locale]/submit/tool-info-success/page.js", "/[locale]/test-auth/page": "app/[locale]/test-auth/page.js", "/[locale]/test-pricing/page": "app/[locale]/test-pricing/page.js", "/[locale]/test-promotion/page": "app/[locale]/test-promotion/page.js", "/[locale]/test-stripe/page": "app/[locale]/test-stripe/page.js", "/[locale]/tools/[id]/page": "app/[locale]/tools/[id]/page.js", "/[locale]/about/page": "app/[locale]/about/page.js", "/[locale]/categories/page": "app/[locale]/categories/page.js", "/[locale]/contact/page": "app/[locale]/contact/page.js", "/[locale]/page": "app/[locale]/page.js", "/[locale]/privacy/page": "app/[locale]/privacy/page.js", "/[locale]/terms/page": "app/[locale]/terms/page.js", "/[locale]/tools/page": "app/[locale]/tools/page.js"}