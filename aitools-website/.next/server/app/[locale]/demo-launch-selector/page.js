(()=>{var e={};e.id=9099,e.ids=[9099],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16562:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),o=t(88170),l=t.n(o),i=t(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(s,n);let d={children:["",{children:["[locale]",{children:["demo-launch-selector",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,54005)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/demo-launch-selector/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/demo-launch-selector/page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/demo-launch-selector/page",pathname:"/[locale]/demo-launch-selector",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28951:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var r=t(60687),a=t(43210),o=t(33489),l=t(77618);function i(){let[e,s]=(0,a.useState)("free"),[t,i]=(0,a.useState)(""),[n,d]=(0,a.useState)(!1),[c,m]=(0,a.useState)(""),[x,u]=(0,a.useState)(!1),[p,h]=(0,a.useState)(!1);(0,l.c3)("launch");let b=async(e,t)=>{d(!0),m(""),await new Promise(e=>setTimeout(e,2e3)),"paid"===e?alert(`模拟支付流程：选择了付费选项，发布日期：${t}`):alert(`模拟免费提交：发布日期：${t}`),s(e),i(t),d(!1)};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Launch Date Selector 演示"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"展示带有Badge验证功能的发布日期选择器"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"演示控制"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"付费状态"}),(0,r.jsx)("button",{onClick:()=>u(!x),className:`w-full px-4 py-2 rounded-lg font-medium transition-colors ${x?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:x?"已付费":"未付费"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Badge验证状态"}),(0,r.jsx)("button",{onClick:()=>h(!p),className:`w-full px-4 py-2 rounded-lg font-medium transition-colors ${p?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:p?"Badge已验证":"Badge未验证"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"重置状态"}),(0,r.jsx)("button",{onClick:()=>{u(!1),h(!1),m("")},className:"w-full px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors",children:"重置"})]})]})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:(0,r.jsx)(o.A,{toolId:"demo-tool-id",toolWebsite:"https://example.com",currentOption:e,currentDate:t,isEditing:!1,onSubmit:b,isSubmitting:n,error:c,hasPaidOrder:x,badgeVerified:p})}),(0,r.jsxs)("div",{className:"bg-blue-50 rounded-lg border border-blue-200 p-6 mt-8",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:"当前状态"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-blue-800",children:"选择的选项："}),(0,r.jsx)("span",{className:"text-blue-600 ml-2",children:e||"未选择"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-blue-800",children:"选择的日期："}),(0,r.jsx)("span",{className:"text-blue-600 ml-2",children:t||"未选择"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-blue-800",children:"付费状态："}),(0,r.jsx)("span",{className:"text-blue-600 ml-2",children:x?"已付费":"未付费"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-blue-800",children:"Badge状态："}),(0,r.jsx)("span",{className:"text-blue-600 ml-2",children:p?"已验证":"未验证"})]})]})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg border border-gray-200 p-6 mt-8",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"功能说明"}),(0,r.jsxs)("div",{className:"space-y-3 text-sm text-gray-700",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"免费发布 + Badge未验证："})," 只能选择一个月后的日期"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"免费发布 + Badge已验证："})," 可以选择明天开始的任意日期，显示badge验证成功提示"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"付费发布："})," 可以选择明天开始的任意日期"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"已付费："})," 显示优先服务激活提示，可以选择任意日期"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("strong",{children:"Badge验证流程："})," 在免费选项下会显示badge获取选项，用户可以通过验证badge来解锁立即发布权限"]})]})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},54005:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/demo-launch-selector/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/demo-launch-selector/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68898:(e,s,t)=>{Promise.resolve().then(t.bind(t,54005))},79551:e=>{"use strict";e.exports=require("url")},87050:(e,s,t)=>{Promise.resolve().then(t.bind(t,28951))}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,9658,6435,6699,1430,2585,3489],()=>t(16562));module.exports=r})();