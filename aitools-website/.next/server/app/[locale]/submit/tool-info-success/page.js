"use strict";(()=>{var e={};e.id=6094,e.ids=[6094],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5706:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),o=r.n(i),l=r(30893),n={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let d={children:["",{children:["[locale]",{children:["submit",{children:["tool-info-success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,82865)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/tool-info-success/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/tool-info-success/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/submit/tool-info-success/page",pathname:"/[locale]/submit/tool-info-success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},82865:(e,t,r)=>{r.r(t),r.d(t,{default:()=>f});var s=r(37413),a=r(78878),i=r(35426),o=r(12909),l=r(75745),n=r(30762),d=r(17063),c=r(56037),u=r.n(c),p=r(91142),x=r(40918);let m=(0,r(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var h=r(64348);async function b(e,t){try{if(await (0,l.A)(),!u().Types.ObjectId.isValid(e))return null;let r=await d.A.findOne({email:t});if(!r)return null;let s=await n.A.findOne({_id:e,submittedBy:r._id}).lean();if(!s)return null;return{...s,_id:s._id.toString(),submittedBy:s.submittedBy.toString(),createdAt:s.createdAt?.toISOString(),updatedAt:s.updatedAt?.toISOString()}}catch(e){return console.error("Error fetching tool data:",e),null}}async function f({params:e,searchParams:t}){let r=await (0,i.getServerSession)(o.N),{locale:l}=await e,{toolId:n}=await t,d=await (0,h.A)("submit.success");r?.user?.email||(0,a.V2)({href:"/",locale:l}),n||(0,a.V2)({href:"/",locale:l});let c=await b(n,r.user.email);return c||(0,a.V2)({href:"/",locale:l}),(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("div",{className:"bg-green-100 rounded-full p-3",children:(0,s.jsx)(p.A,{className:"h-12 w-12 text-green-600"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:d("tool_info_submitted_title")}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:d("tool_info_submitted_desc")})]}),(0,s.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-6 mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:d("tool_info_title")}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-gray-700",children:d("tool_name_label")}),(0,s.jsx)("span",{className:"ml-2 text-gray-900",children:c.name})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"font-medium text-gray-700",children:d("current_status_label")}),(0,s.jsx)("span",{className:"ml-2 text-gray-900",children:d("status_draft")})]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)(x.A,{className:"h-6 w-6 text-blue-600 mr-2"}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900",children:d("next_step_title")})]}),(0,s.jsx)("p",{className:"text-blue-800 mb-6",children:d("next_step_desc")}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)(a.N_,{href:`/submit/launch-date/${c._id}`,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-medium text-center hover:bg-blue-700 transition-colors flex items-center",children:[d("set_launch_date_button"),(0,s.jsx)(m,{className:"h-5 w-5 ml-2"})]})})]})]})}},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,1430,3136,2585,1158],()=>r(5706));module.exports=s})();