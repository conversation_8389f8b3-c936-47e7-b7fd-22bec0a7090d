(()=>{var e={};e.id=245,e.ids=[245],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5148:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var i=r(36344),a=r(65752),n=r(13581),s=r(75745),o=r(17063);let l={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,n.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,s.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,a]=r.split(":");if(i!==e.token||a!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,s.A)();try{let i=await o.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),a=r.n(i);let n=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),s=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({email:1}),s.index({role:1}),s.index({emailVerificationToken:1}),s.index({"accounts.provider":1,"accounts.providerAccountId":1}),s.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},s.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=a().models.User||a().model("User",s)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23466:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var i=r(60687),a=r(43210),n=r(12340),s=r(77618),o=r(32498);function l({toolId:e,locale:t,currentOption:r="free",currentDate:l,minFreeDate:d,minPaidDate:c,hasPaidOrder:u=!1,orderId:m,isEditMode:p=!1}){let h=(0,n.rd)(),g=(0,s.c3)("launch"),[x,y]=(0,a.useState)(!1),[f,v]=(0,a.useState)(""),I=async(r,i)=>{y(!0),v("");try{let a=await fetch(`/api/tools/${e}/launch-date`,{method:"POST",headers:{"Content-Type":"application/json","X-Locale":t},body:JSON.stringify({launchOption:r,selectedDate:i,hasPaidOrder:u})}),n=await a.json();n.success?"paid"===r&&n.data.paymentUrl?window.location.href=n.data.paymentUrl:h.push(`/submit/launch-date-success?toolId=${e}`):v(n.message||g("submit_failed"))}catch{v(g("network_error"))}finally{y(!1)}};return(0,i.jsx)(o.A,{toolId:e,currentOption:u?"paid":r,currentDate:l,isEditing:p,onSubmit:I,isSubmitting:x,error:f,hasPaidOrder:u})}},26373:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var i=r(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),s=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},o=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:n="",children:s,iconNode:c,...u},m)=>(0,i.createElement)("svg",{ref:m,...d,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:o("lucide",n),...!s&&!l(u)&&{"aria-hidden":"true"},...u},[...c.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(s)?s:[s]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...n},l)=>(0,i.createElement)(c,{ref:l,iconNode:t,className:o(`lucide-${a(s(e))}`,`lucide-${e}`,r),...n}));return r.displayName=s(e),r}},28354:e=>{"use strict";e.exports=require("util")},28448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var i=r(65239),a=r(48088),n=r(88170),s=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["[locale]",{children:["submit",{children:["launch-date",{children:["[toolId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,96781)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/submit/launch-date/[toolId]/page",pathname:"/[locale]/submit/launch-date/[toolId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29703:(e,t,r)=>{"use strict";r.d(t,{P5:()=>o,Tr:()=>n,Y5:()=>i,kx:()=>l,w8:()=>s});let i={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},a={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function n(e){let t=a.BASE_DIR,r=a.DIRECTORIES[e];return`${t}/${r}`}function s(e,t){let r=a.DIRECTORIES[e];return`/api/uploads/${r}/${t}`}function o(e,t){let r=i[e];if(!r)return{isValid:!0};let a=t.trim().length;return a<r.min?{isValid:!1,error:`${r.label} must be at least ${r.min} characters long`}:a>r.max?{isValid:!1,error:`${r.label} cannot exceed ${r.max} characters`}:{isValid:!0}}function l(e,t){var r;let i=function(e,t){let r=a.FILE_SIZE_LIMITS[e];if(t>r){let e=Math.round(r/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!i.isValid)return i;let n=a.ALLOWED_TYPES.IMAGES,s=(r=e.type,n.includes(r)?{isValid:!0}:{isValid:!1,error:`File type ${r} is not allowed. Allowed types: ${n.join(", ")}`});return s.isValid?{isValid:!0}:s}},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(56037),a=r.n(i),n=r(60366),s=r(29703);let o=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,minlength:[s.Y5.TOOL_NAME.min,`Tool name must be at least ${s.Y5.TOOL_NAME.min} characters`],maxlength:[s.Y5.TOOL_NAME.max,`Tool name cannot exceed ${s.Y5.TOOL_NAME.max} characters`]},tagline:{type:String,trim:!0,maxlength:[s.Y5.TAGLINE.max,`Tagline cannot exceed ${s.Y5.TAGLINE.max} characters`]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,minlength:[s.Y5.DESCRIPTION.min,`Description must be at least ${s.Y5.DESCRIPTION.min} characters`],maxlength:[s.Y5.DESCRIPTION.max,`Description cannot exceed ${s.Y5.DESCRIPTION.max} characters`]},longDescription:{type:String,trim:!0,maxlength:[s.Y5.LONG_DESCRIPTION.max,`Long description cannot exceed ${s.Y5.LONG_DESCRIPTION.max} characters`]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,maxlength:[s.Y5.WEBSITE_URL.max,`Website URL cannot exceed ${s.Y5.WEBSITE_URL.max} characters`],validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:n.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[s.Y5.PRICING_DETAILS.max,`Pricing details cannot exceed ${s.Y5.PRICING_DETAILS.max} characters`]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({status:1,isActive:1}),o.index({category:1,status:1}),o.index({tags:1,status:1}),o.index({submittedBy:1}),o.index({launchDate:-1}),o.index({views:-1}),o.index({likes:-1}),o.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=a().models.Tool||a().model("Tool",o)},31098:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var i=r(56037),a=r.n(i);let n=new i.Schema({userId:{type:i.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:i.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({userId:1,createdAt:-1}),n.index({toolId:1}),n.index({status:1}),n.index({paymentIntentId:1}),n.index({paymentSessionId:1}),n.index({stripePaymentIntentId:1}),n.index({stripeCustomerId:1}),n.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),n.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),n.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},n.methods.markAsFailed=function(){return this.status="failed",this.save()},n.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},n.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let s=a().models.Order||a().model("Order",n)},33873:e=>{"use strict";e.exports=require("path")},51465:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},57592:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx","default")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>s,PZ:()=>o,RI:()=>d,ut:()=>l});var i=r(64348);let a=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function n(e){let t=await (0,i.A)({locale:e||"en",namespace:"categories"});return a.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function s(e){return(await n(e)).map(e=>({value:e.slug,label:e.name}))}async function o(e,t){return(await n(t)).find(t=>t.slug===e)}let l=a.map(e=>e.slug),d=a.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65414:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,23466))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var i=r(56037),a=r.n(i);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let o=async function(){if(s.conn)return s.conn;s.promise||(s.promise=a().connect(n,{bufferCommands:!1}));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91142:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},94735:e=>{"use strict";e.exports=require("events")},96781:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var i=r(37413),a=r(35426),n=r(78878),s=r(12909),o=r(75745),l=r(30762),d=r(17063),c=r(31098),u=r(51465),m=r(91142),p=r(5148),h=r(64348),g=r(56037),x=r.n(g),y=r(57592);async function f(e,t,r=!1){try{if(await (0,o.A)(),!x().Types.ObjectId.isValid(e))return null;let i=await l.A.findById(e).lean();if(!i)return null;let a=await d.A.findOne({email:t}).lean();if(!a||i.submittedBy.toString()!==a._id.toString())return null;if(r){if(!i.selectedLaunchDate||"published"===i.status)return null}else if("draft"!==i.status)return null;let n=!1,s=null;if("paid"===i.launchOption){let e=await c.A.findOne({toolId:i._id,status:"completed"}).lean();e&&(n=!0,s=e._id.toString())}return{_id:i._id.toString(),name:i.name,description:i.description,status:i.status,launchOption:i.launchOption,selectedLaunchDate:i.selectedLaunchDate,hasPaidOrder:n,orderId:s}}catch(e){return console.error("Failed to fetch tool:",e),null}}async function v({params:e,searchParams:t}){let{locale:r,toolId:o}=await e,{mode:l="create"}=await t,d=await (0,h.A)({locale:r,namespace:"launch"}),c=await (0,h.A)({locale:r,namespace:"common"}),g=await (0,a.getServerSession)(s.N);g?.user?.email||(0,n.V2)({href:"/",locale:r});let x="edit"===l,v=await f(o,g.user.email,x);v||(0,n.V2)({href:"/",locale:r});let I=function(){let e=new Date;return e.setMonth(e.getMonth()+1),e.toISOString().split("T")[0]}(),S=function(){let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]}(),b=v.selectedLaunchDate?new Date(v.selectedLaunchDate).toISOString().split("T")[0]:v.hasPaidOrder?S:I;return(0,i.jsxs)("div",{className:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8",style:{minWidth:1e3},children:[(0,i.jsxs)("div",{className:"mb-8",children:[x&&(0,i.jsxs)("a",{href:"/profile/submitted",className:"flex items-center text-gray-600 hover:text-gray-900 mb-4",children:[(0,i.jsx)(u.A,{className:"h-4 w-4 mr-1"}),c("back")]}),x?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:d("edit_launch_date")}),(0,i.jsx)("p",{className:"text-gray-600",children:d("edit_launch_date_description")})]}):(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)(m.A,{className:"h-12 w-12 text-green-500 mx-auto mb-4"}),(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:d("tool_submitted_success")}),(0,i.jsx)("p",{className:"text-lg text-gray-600",children:d("select_launch_date_prompt")})]})]}),(0,i.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6 mb-8",children:[(0,i.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:v.name}),(0,i.jsx)("p",{className:"text-gray-600",children:v.description})]}),x&&(0,i.jsx)("div",{className:"mb-8",children:(0,i.jsxs)("div",{className:"flex items-center space-x-4 text-sm",children:[(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)("div",{className:`w-3 h-3 rounded-full mr-2 ${"pending"===v.status?"bg-yellow-400":"approved"===v.status?"bg-green-400":"bg-gray-400"}`}),(0,i.jsx)("span",{className:"text-gray-600",children:d(`status.${v.status}`)})]}),v.hasPaidOrder&&(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(p.A,{className:"h-4 w-4 text-green-500 mr-1"}),(0,i.jsx)("span",{className:"text-green-600 font-medium",children:d("paid_plan")})]})]})}),(0,i.jsx)(y.default,{toolId:o,locale:r,currentOption:v.hasPaidOrder?"paid":v.launchOption,currentDate:b,minFreeDate:I,minPaidDate:S,hasPaidOrder:v.hasPaidOrder,orderId:v.orderId,isEditMode:x})]})}},96958:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,57592))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,9658,6435,6699,1430,3136,2585,2498],()=>r(28448));module.exports=i})();