(()=>{var e={};e.id=758,e.ids=[758],e.modules={2716:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),o=t(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(s,i);let d={children:["",{children:["[locale]",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73010)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/[locale]/dashboard/page",pathname:"/[locale]/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var r=t(60687),a=t(93613),l=t(11860);function n({message:e,onClose:s,className:t=""}){return(0,r.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${t}`,children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:e})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},11860:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13861:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21281:(e,s,t)=>{Promise.resolve().then(t.bind(t,43660))},25334:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33823:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});var r=t(60687);function a({size:e="md",className:s=""}){return(0,r.jsx)("div",{className:`flex justify-center items-center ${s}`,children:(0,r.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},33873:e=>{"use strict";e.exports=require("path")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},43660:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f});var r=t(60687),a=t(43210),l=t(12340),n=t(33823),o=t(11011);t(56976);var i=t(5336),d=t(48730),c=t(35071),h=t(96474),x=t(53411),m=t(13861),p=t(40228),u=t(25334),g=t(63143);let y=e=>{switch(e){case"published":return"bg-green-100 text-green-800";case"approved":return"bg-blue-100 text-blue-800";case"pending":return"bg-yellow-100 text-yellow-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},v=e=>{switch(e){case"published":return"已发布";case"approved":return"已通过";case"pending":return"审核中";case"rejected":return"已拒绝";case"draft":return"草稿";default:return e}},b=e=>{switch(e){case"published":case"approved":return(0,r.jsx)(i.A,{className:"h-4 w-4"});case"pending":case"draft":return(0,r.jsx)(d.A,{className:"h-4 w-4"});case"rejected":return(0,r.jsx)(c.A,{className:"h-4 w-4"});default:return null}};function f(){let[e,s]=(0,a.useState)("all"),[t,d]=(0,a.useState)([]),[c,f]=(0,a.useState)(!0),[j,N]=(0,a.useState)(""),w=t.filter(s=>"all"===e||s.status===e),A={total:t.length,approved:t.filter(e=>"approved"===e.status).length,pending:t.filter(e=>"pending"===e.status).length,rejected:t.filter(e=>"rejected"===e.status).length,totalViews:t.reduce((e,s)=>e+s.views,0),totalLikes:t.reduce((e,s)=>e+s.likes,0)};return c?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(n.A,{size:"lg",className:"py-20"})}):(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"开发者仪表板"}),(0,r.jsx)("p",{className:"text-lg text-gray-600",children:"管理您提交的 AI 工具"})]}),(0,r.jsxs)(l.N_,{href:"/submit",className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(h.A,{className:"mr-2 h-5 w-5"}),"提交新工具"]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总提交数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.total})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(i.A,{className:"h-8 w-8 text-green-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"已通过"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.approved})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-purple-600"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总浏览量"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.totalViews})]})]})}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 text-red-600",children:"❤️"})}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"总点赞数"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:A.totalLikes})]})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>s("all"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"all"===e?"bg-blue-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["全部 (",A.total,")"]}),(0,r.jsxs)("button",{onClick:()=>s("approved"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"approved"===e?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["已通过 (",A.approved,")"]}),(0,r.jsxs)("button",{onClick:()=>s("pending"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"pending"===e?"bg-yellow-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["审核中 (",A.pending,")"]}),(0,r.jsxs)("button",{onClick:()=>s("rejected"),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${"rejected"===e?"bg-red-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:["已拒绝 (",A.rejected,")"]})]})}),j&&(0,r.jsx)(o.default,{message:j,onClose:()=>N(""),className:"mb-6"}),(0,r.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200",children:w.length>0?(0,r.jsx)("div",{className:"divide-y divide-gray-200",children:w.map(e=>(0,r.jsx)("div",{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,r.jsxs)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${y(e.status)}`,children:[b(e.status),(0,r.jsx)("span",{className:"ml-1",children:v(e.status)})]})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-3 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["提交于 ",new Date(e.submittedAt).toLocaleDateString("zh-CN")]})]}),e.launchDate&&(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(i.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:["发布于 ",new Date(e.launchDate).toLocaleDateString("zh-CN")]})]}),"approved"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[e.views," 浏览"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)("span",{children:"❤️"}),(0,r.jsxs)("span",{children:[e.likes," 点赞"]})]})]})]}),"rejected"===e.status&&e.reviewNotes&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-red-800",children:[(0,r.jsx)("strong",{children:"拒绝原因："})," ",e.reviewNotes]})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:["approved"===e.status&&(0,r.jsx)(l.N_,{href:`/tools/${e._id}`,className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"查看详情",children:(0,r.jsx)(m.A,{className:"h-5 w-5"})}),(0,r.jsx)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"p-2 text-gray-400 hover:text-green-600 transition-colors",title:"访问网站",children:(0,r.jsx)(u.A,{className:"h-5 w-5"})}),("rejected"===e.status||"pending"===e.status)&&(0,r.jsx)("button",{className:"p-2 text-gray-400 hover:text-blue-600 transition-colors",title:"编辑",children:(0,r.jsx)(g.A,{className:"h-5 w-5"})})]})]})},e._id))}):(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 mb-4",children:(0,r.jsx)(x.A,{className:"h-12 w-12 mx-auto"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"all"===e?"还没有提交任何工具":`没有${v(e)}的工具`}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:"all"===e?"开始提交您的第一个 AI 工具吧！":"尝试选择其他状态查看工具"}),"all"===e&&(0,r.jsxs)(l.N_,{href:"/submit",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4"}),"提交工具"]})]})})]})}},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53411:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},56976:(e,s,t)=>{"use strict";function r(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3011";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3011";return`http://localhost:${e}`}}function a(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=r();return`${e}/api`}function l(){return"production"}function n(){return"development"===l()}t.d(s,{u:()=>c});let o={baseUrl:r(),apiBaseUrl:a(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:r(),environment:l(),isDevelopment:n(),isProduction:"production"===l(),port:process.env.PORT||"3011"};n()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",o.baseUrl),console.log("  API Base URL:",o.apiBaseUrl),console.log("  NextAuth URL:",o.nextAuthUrl),console.log("  Environment:",o.environment),console.log("  Port:",o.port),console.log("  AI Service URLs:",{generateProductInfo:process.env.AI_GENERATE_PRODUCT_INFO_URL||"http://localhost:50058/ai/generateProductInfo"}));let i=a();class d{constructor(e=i){this.baseURL=e}async request(e,s={}){try{let t=`${this.baseURL}${e}`,r={headers:{"Content-Type":"application/json",...s.headers},...s};console.log("API request:",{url:t,config:r});let a=await fetch(t,r),l=await a.json();if(!a.ok)throw Error(l.error||`HTTP error! status: ${a.status}`);return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/tools${t?`?${t}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,s){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(s)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/user/liked-tools${t?`?${t}`:""}`)}async getAdminTools(e){let s=new URLSearchParams;e&&Object.entries(e).forEach(([e,t])=>{void 0!==t&&s.append(e,t.toString())});let t=s.toString();return this.request(`/admin/tools${t?`?${t}`:""}`)}async approveTool(e,s){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(s)})}async rejectTool(e,s){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(s)})}async getAdminStats(e){let s=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${s}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,s){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(s)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}}let c=new d},62688:(e,s,t)=>{"use strict";t.d(s,{A:()=>h});var r=t(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,t)=>t?t.toUpperCase():s.toLowerCase()),n=e=>{let s=l(e);return s.charAt(0).toUpperCase()+s.slice(1)},o=(...e)=>e.filter((e,s,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===s).join(" ").trim(),i=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:s=24,strokeWidth:t=2,absoluteStrokeWidth:a,className:l="",children:n,iconNode:c,...h},x)=>(0,r.createElement)("svg",{ref:x,...d,width:s,height:s,stroke:e,strokeWidth:a?24*Number(t)/Number(s):t,className:o("lucide",l),...!n&&!i(h)&&{"aria-hidden":"true"},...h},[...c.map(([e,s])=>(0,r.createElement)(e,s)),...Array.isArray(n)?n:[n]])),h=(e,s)=>{let t=(0,r.forwardRef)(({className:t,...l},i)=>(0,r.createElement)(c,{ref:i,iconNode:s,className:o(`lucide-${a(n(e))}`,`lucide-${e}`,t),...l}));return t.displayName=n(e),t}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},73010:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx","default")},79551:e=>{"use strict";e.exports=require("url")},84833:(e,s,t)=>{Promise.resolve().then(t.bind(t,73010))},93613:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,4999,9658,6435,6699,1430,2585],()=>t(2716));module.exports=r})();