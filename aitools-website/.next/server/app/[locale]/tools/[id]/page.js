(()=>{var e={};e.id=6249,e.ids=[6249],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15985:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var a=r(60687),n=r(43210),s=r(82136),i=r(77618),o=r(23877);function l({toolId:e,initialComments:t,onLoginRequired:r}){let{data:l}=(0,s.useSession)(),[c,d]=(0,n.useState)(t),[u,m]=(0,n.useState)(""),[p,h]=(0,n.useState)(null),[g,f]=(0,n.useState)(""),[x,b]=(0,n.useState)(!1),y=(0,i.c3)("comments"),v=async()=>{try{let t=await fetch(`/api/tools/${e}/comments`);if(t.ok){let e=await t.json();e.success&&d(e.data.comments)}}catch(e){console.error("Failed to fetch comments:",e)}},N=async()=>{if(!l)return void r?.();if(u.trim()){b(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:u.trim()})});if(t.ok)(await t.json()).success&&(m(""),v());else{let e=await t.json();console.error("Comment submission failed:",e.message)}}catch(e){console.error("Comment submission error:",e)}finally{b(!1)}}},w=async()=>{if(!l)return void r?.();if(g.trim()&&p){b(!0);try{let t=await fetch(`/api/tools/${e}/comments`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:g.trim(),parentId:p})});if(t.ok)(await t.json()).success&&(f(""),h(null),v());else{let e=await t.json();console.error("Reply submission failed:",e.message)}}catch(e){console.error("Reply submission error:",e)}finally{b(!1)}}},j=e=>{let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/36e5);if(r<1)return y("just_now");{if(r<24)return y("hours_ago",{hours:r});let e=Math.floor(r/24);return e<7?y("days_ago",{days:e}):t.toLocaleDateString()}},A=(e,t=!1)=>(0,a.jsxs)("div",{className:`${t?"ml-8 border-l-2 border-gray-100 pl-4":""}`,children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:e.userId.image?(0,a.jsx)("img",{src:e.userId.image,alt:e.userId.name,className:"w-8 h-8 rounded-full"}):(0,a.jsx)("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:(0,a.jsx)(o.x$1,{className:"w-4 h-4 text-gray-600"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.userId.name}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:j(e.createdAt)})]}),(0,a.jsx)("p",{className:"mt-1 text-sm text-gray-700",children:e.content}),(0,a.jsx)("div",{className:"mt-2 flex items-center space-x-4",children:(0,a.jsxs)("button",{className:"text-xs text-gray-500 hover:text-blue-600 flex items-center space-x-1",onClick:()=>h(p===e._id?null:e._id),children:[(0,a.jsx)(o.w1Z,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:y("reply")})]})}),p===e._id&&(0,a.jsxs)("div",{className:"mt-3 space-y-2",children:[(0,a.jsx)("textarea",{value:g,onChange:e=>f(e.target.value),placeholder:l?y("write_reply"):y("login_to_reply"),className:"w-full p-2 text-sm border border-gray-300 rounded resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:2,maxLength:500,disabled:!l}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[g.length,"/500"]}),(0,a.jsxs)("div",{className:"space-x-2",children:[(0,a.jsx)("button",{onClick:()=>{h(null),f("")},className:"px-3 py-1 text-xs text-gray-600 hover:text-gray-800",children:y("cancel")}),(0,a.jsx)("button",{onClick:w,disabled:x||!g.trim()||!l,className:"px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:x?y("submitting"):y("submit_reply")})]})]})]})]})]}),e.replies&&e.replies.length>0&&(0,a.jsx)("div",{className:"mt-4 space-y-4",children:e.replies.map(e=>A(e,!0))})]},e._id);return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:[y("comments")," (",c.length,")"]}),(0,a.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,a.jsx)("textarea",{value:u,onChange:e=>m(e.target.value),placeholder:l?y("write_comment"):y("login_to_comment"),className:"w-full p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent",rows:4,maxLength:1e3,disabled:!l}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[u.length,"/1000"]}),(0,a.jsx)("button",{onClick:N,disabled:x||!u.trim()||!l,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:x?y("submitting"):y("submit_comment")})]})]}),(0,a.jsx)("div",{className:"space-y-6",children:0===c.length?(0,a.jsx)("p",{className:"text-gray-500 text-center py-8",children:y("no_comments")}):c.map(e=>A(e))})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24443:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(56037),n=r.n(a);let s=new a.Schema({toolId:{type:a.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},userId:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},content:{type:String,required:[!0,"Comment content is required"],trim:!0,maxlength:[1e3,"Comment cannot exceed 1000 characters"],minlength:[1,"Comment cannot be empty"]},parentId:{type:a.Schema.Types.ObjectId,ref:"Comment",default:null},likes:{type:Number,default:0,min:0},isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({toolId:1,createdAt:-1}),s.index({userId:1}),s.index({parentId:1}),s.index({isActive:1}),s.virtual("replyCount",{ref:"Comment",localField:"_id",foreignField:"parentId",count:!0,match:{isActive:!0}}),s.statics.getToolComments=function(e){return this.find({toolId:e,isActive:!0}).populate("userId","name avatar").populate({path:"parentId",select:"content userId",populate:{path:"userId",select:"name"}}).sort({createdAt:-1})},s.methods.getReplies=function(){return n().model("Comment").find({parentId:this._id,isActive:!0}).populate("userId","name avatar").sort({createdAt:1})};let i=n().models.Comment||n().model("Comment",s)},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32134:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,15985)),Promise.resolve().then(r.bind(r,7485)),Promise.resolve().then(r.bind(r,63701))},33873:e=>{"use strict";e.exports=require("path")},56037:e=>{"use strict";e.exports=require("mongoose")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63701:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(60687),n=r(43210),s=r.n(n),i=r(48577);function o({children:e}){let[t,r]=(0,n.useState)(!1),o=()=>{r(!0)},l=s().Children.map(e,e=>s().isValidElement(e)?s().cloneElement(e,{onLoginRequired:o}):e);return(0,a.jsxs)(a.Fragment,{children:[l,(0,a.jsx)(i.A,{isOpen:t,onClose:()=>r(!1)})]})}},65030:(e,t,r)=>{"use strict";let a;r.r(t),r.d(t,{default:()=>e5,generateMetadata:()=>e4});var n=r(37413);r(61120);var s=r(78878),i=r(39916),o=r(64348),l=r(21659),c=r(75745),d=r(24443),u=r(80555);async function m(e){try{await (0,c.A)();let t=await d.A.find({toolId:e,isActive:!0,parentId:null}).populate("userId","name email image").sort({createdAt:-1}).limit(10);return await Promise.all(t.map(async e=>{let t=await d.A.find({parentId:e._id,isActive:!0}).populate("userId","name email image").sort({createdAt:1});return{_id:e._id.toString(),content:e.content,userId:{_id:e.userId._id.toString(),name:e.userId.name,email:e.userId.email,image:e.userId.image},createdAt:e.createdAt.toISOString(),likes:e.likes||0,replies:t.map(e=>({_id:e._id.toString(),content:e.content,userId:{_id:e.userId._id.toString(),name:e.userId.name,email:e.userId.email,image:e.userId.image},createdAt:e.createdAt.toISOString(),likes:e.likes||0}))}}))}catch(e){return console.error("Error fetching comments:",e),[]}}async function p({toolId:e,onLoginRequired:t}){let r=await m(e);return(0,n.jsx)(u.default,{toolId:e,initialComments:r,onLoginRequired:t})}var h=r(93331),g=r(52343),f=r(79171),x=r(33488),b=r(94014),y=r(1215),v=r(85838),N=r(92572),w=r(89203);let j={}.hasOwnProperty;function A(e,t){let r=t||{};function a(t,...r){let n=a.invalid,s=a.handlers;if(t&&j.call(t,e)){let r=String(t[e]);n=j.call(s,r)?s[r]:a.unknown}if(n)return n.call(this,t,...r)}return a.handlers=r.handlers||{},a.invalid=r.invalid,a.unknown=r.unknown,a}let C={}.hasOwnProperty;var k=r(60730),_=r(19143),S=r(18732);let I=[function(e,t,r,a){if("code"===t.type&&(0,_.m)(t,a)&&("list"===e.type||e.type===t.type&&(0,_.m)(e,a)))return!1;if("spread"in r&&"boolean"==typeof r.spread){if("paragraph"===e.type&&(e.type===t.type||"definition"===t.type||"heading"===t.type&&(0,S.f)(t,a)))return;return+!!r.spread}}],O=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"],q=[{character:"	",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",before:"[\\r\\n]",inConstruct:"phrasing"},{character:"	",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"\r",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:"\n",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde","codeFencedMetaGraveAccent","codeFencedMetaTilde","destinationLiteral","headingAtx"]},{character:" ",after:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",before:"[\\r\\n]",inConstruct:"phrasing"},{character:" ",inConstruct:["codeFencedLangGraveAccent","codeFencedLangTilde"]},{character:"!",after:"\\[",inConstruct:"phrasing",notInConstruct:O},{character:'"',inConstruct:"titleQuote"},{atBreak:!0,character:"#"},{character:"#",inConstruct:"headingAtx",after:"(?:[\r\n]|$)"},{character:"&",after:"[#A-Za-z]",inConstruct:"phrasing"},{character:"'",inConstruct:"titleApostrophe"},{character:"(",inConstruct:"destinationRaw"},{before:"\\]",character:"(",inConstruct:"phrasing",notInConstruct:O},{atBreak:!0,before:"\\d+",character:")"},{character:")",inConstruct:"destinationRaw"},{atBreak:!0,character:"*",after:"(?:[ 	\r\n*])"},{character:"*",inConstruct:"phrasing",notInConstruct:O},{atBreak:!0,character:"+",after:"(?:[ 	\r\n])"},{atBreak:!0,character:"-",after:"(?:[ 	\r\n-])"},{atBreak:!0,before:"\\d+",character:".",after:"(?:[ 	\r\n]|$)"},{atBreak:!0,character:"<",after:"[!/?A-Za-z]"},{character:"<",after:"[!/?A-Za-z]",inConstruct:"phrasing",notInConstruct:O},{character:"<",inConstruct:"destinationLiteral"},{atBreak:!0,character:"="},{atBreak:!0,character:">"},{character:">",inConstruct:"destinationLiteral"},{atBreak:!0,character:"["},{character:"[",inConstruct:"phrasing",notInConstruct:O},{character:"[",inConstruct:["label","reference"]},{character:"\\",after:"[\\r\\n]",inConstruct:"phrasing"},{character:"]",inConstruct:["label","reference"]},{atBreak:!0,character:"_"},{character:"_",inConstruct:"phrasing",notInConstruct:O},{atBreak:!0,character:"`"},{character:"`",inConstruct:["codeFencedLangGraveAccent","codeFencedMetaGraveAccent"]},{character:"`",inConstruct:"phrasing",notInConstruct:O},{atBreak:!0,character:"~"}];var E=r(38251);function T(e){return e.label||!e.identifier?e.label||"":(0,E.s)(e.identifier)}function P(e){if(!e._compiled){let t=(e.atBreak?"[\\r\\n][\\t ]*":"")+(e.before?"(?:"+e.before+")":"");e._compiled=RegExp((t?"("+t+")":"")+(/[|\\{}()[\]^$+*?.-]/.test(e.character)?"\\":"")+e.character+(e.after?"(?:"+e.after+")":""),"g")}return e._compiled}var D=r(82629);let L=/\r?\n|\r/g;function F(e,t){let r,a=[],n=0,s=0;for(;r=L.exec(e);)i(e.slice(n,r.index)),a.push(r[0]),n=r.index+r[0].length,s++;return i(e.slice(n)),a.join("");function i(e){a.push(t(e,s,!e))}}var $=r(20610);function R(e,t){return e-t}function U(e,t){let r,a=/\\(?=[!-/:-@[-`{-~])/g,n=[],s=[],i=e+t,o=-1,l=0;for(;r=a.exec(i);)n.push(r.index);for(;++o<n.length;)l!==n[o]&&s.push(e.slice(l,n[o])),s.push("\\"),l=n[o];return s.push(e.slice(l)),s.join("")}function B(e){let t=e||{},r=t.now||{},a=t.lineShift||0,n=r.line||1,s=r.column||1;return{move:function(e){let t=e||"",r=t.split(/\r?\n|\r/g),i=r[r.length-1];return n+=r.length-1,s=1===r.length?s+i.length:1+i.length+a,t},current:function(){return{now:{line:n,column:s},lineShift:a}},shift:function(e){a+=e}}}function z(e){throw Error("Cannot handle value `"+e+"`, expected node")}function M(e){throw Error("Cannot handle unknown node `"+e.type+"`")}function G(e,t){if("definition"===e.type&&e.type===t.type)return 0}function H(e,t){return function(e,t,r){let a,n=t.indexStack,s=e.children||[],i=[],o=-1,l=r.before;n.push(-1);let c=t.createTracker(r);for(;++o<s.length;){let d,u=s[o];if(n[n.length-1]=o,o+1<s.length){let r=t.handle.handlers[s[o+1].type];r&&r.peek&&(r=r.peek),d=r?r(s[o+1],e,t,{before:"",after:"",...c.current()}).charAt(0):""}else d=r.after;i.length>0&&("\r"===l||"\n"===l)&&"html"===u.type&&(i[i.length-1]=i[i.length-1].replace(/(\r?\n|\r)$/," "),l=" ",(c=t.createTracker(r)).move(i.join("")));let m=t.handle(u,e,t,{...c.current(),after:d,before:l});a&&a===m.slice(0,1)&&(m=(0,D.T)(a.charCodeAt(0))+m.slice(1));let p=t.attentionEncodeSurroundingInfo;t.attentionEncodeSurroundingInfo=void 0,a=void 0,p&&(i.length>0&&p.before&&l===i[i.length-1].slice(-1)&&(i[i.length-1]=i[i.length-1].slice(0,-1)+(0,D.T)(l.charCodeAt(0))),p.after&&(a=d)),c.move(m),i.push(m),l=m.slice(-1)}return n.pop(),i.join("")}(e,this,t)}function J(e,t){return function(e,t,r){let a=t.indexStack,n=e.children||[],s=t.createTracker(r),i=[],o=-1;for(a.push(-1);++o<n.length;){let r=n[o];a[a.length-1]=o,i.push(s.move(t.handle(r,e,t,{before:"\n",after:"\n",...s.current()}))),"list"!==r.type&&(t.bulletLastUsed=void 0),o<n.length-1&&i.push(s.move(function(e,t,r,a){let n=a.join.length;for(;n--;){let s=a.join[n](e,t,r,a);if(!0===s||1===s)break;if("number"==typeof s)return"\n".repeat(1+s);if(!1===s)return"\n\n\x3c!----\x3e\n\n"}return"\n\n"}(r,n[o+1],e,t)))}return a.pop(),i.join("")}(e,this,t)}function W(e,t){return function(e,t,r){let a=(r.before||"")+(t||"")+(r.after||""),n=[],s=[],i={},o=-1;for(;++o<e.unsafe.length;){let t,r=e.unsafe[o];if(!(0,$.q)(e.stack,r))continue;let s=e.compilePattern(r);for(;t=s.exec(a);){let e="before"in r||!!r.atBreak,a="after"in r,s=t.index+(e?t[1].length:0);n.includes(s)?(i[s].before&&!e&&(i[s].before=!1),i[s].after&&!a&&(i[s].after=!1)):(n.push(s),i[s]={before:e,after:a})}}n.sort(R);let l=r.before?r.before.length:0,c=a.length-(r.after?r.after.length:0);for(o=-1;++o<n.length;){let e=n[o];!(e<l)&&!(e>=c)&&(!(e+1<c)||n[o+1]!==e+1||!i[e].after||i[e+1].before||i[e+1].after)&&(n[o-1]!==e-1||!i[e].before||i[e-1].before||i[e-1].after)&&(l!==e&&s.push(U(a.slice(l,e),"\\")),l=e,!/[!-/:-@[-`{-~]/.test(a.charAt(e))||r.encode&&r.encode.includes(a.charAt(e))?(s.push((0,D.T)(a.charCodeAt(e))),l++):s.push("\\"))}return s.push(U(a.slice(l,c),r.after)),s.join("")}(this,e,t)}let Z=(0,r(2557).l)().use(w.A).use(function(e){let t=this;t.compiler=function(r){var a={...t.data("settings"),...e,extensions:t.data("toMarkdownExtensions")||[]};let n={associationId:T,containerPhrasing:H,containerFlow:J,createTracker:B,compilePattern:P,enter:function(e){return n.stack.push(e),function(){n.stack.pop()}},handlers:{...k.p},handle:void 0,indentLines:F,indexStack:[],join:[...I],options:{},safe:W,stack:[],unsafe:[...q]};!function e(t,r){var a,n,s,i;let o,l=-1;if(r.extensions)for(;++l<r.extensions.length;)e(t,r.extensions[l]);for(o in r)if(C.call(r,o))switch(o){case"extensions":break;case"unsafe":case"join":a=t[o],(n=r[o])&&a.push(...n);break;case"handlers":s=t[o],(i=r[o])&&Object.assign(s,i);break;default:t.options[o]=r[o]}return t}(n,a||{}),n.options.tightDefinitions&&n.join.push(G),n.handle=A("type",{invalid:z,unknown:M,handlers:n.handlers});let s=n.handle(r,void 0,n,{before:"\n",after:"\n",now:{line:1,column:1},lineShift:0});return s&&10!==s.charCodeAt(s.length-1)&&13!==s.charCodeAt(s.length-1)&&(s+="\n"),s}}).freeze();var Y=r(34967),K=r(4698);let Q=["ariaDescribedBy","ariaLabel","ariaLabelledBy"],V={ancestors:{tbody:["table"],td:["table"],th:["table"],thead:["table"],tfoot:["table"],tr:["table"]},attributes:{a:[...Q,"dataFootnoteBackref","dataFootnoteRef",["className","data-footnote-backref"],"href"],blockquote:["cite"],code:[["className",/^language-./]],del:["cite"],div:["itemScope","itemType"],dl:[...Q],h2:[["className","sr-only"]],img:[...Q,"longDesc","src"],input:[["disabled",!0],["type","checkbox"]],ins:["cite"],li:[["className","task-list-item"]],ol:[...Q,["className","contains-task-list"]],q:["cite"],section:["dataFootnotes",["className","footnotes"]],source:["srcSet"],summary:[...Q],table:[...Q],ul:[...Q,["className","contains-task-list"]],"*":["abbr","accept","acceptCharset","accessKey","action","align","alt","axis","border","cellPadding","cellSpacing","char","charOff","charSet","checked","clear","colSpan","color","cols","compact","coords","dateTime","dir","encType","frame","hSpace","headers","height","hrefLang","htmlFor","id","isMap","itemProp","label","lang","maxLength","media","method","multiple","name","noHref","noShade","noWrap","open","prompt","readOnly","rev","rowSpan","rows","rules","scope","selected","shape","size","span","start","summary","tabIndex","title","useMap","vAlign","value","width"]},clobber:["ariaDescribedBy","ariaLabelledBy","id","name"],clobberPrefix:"user-content-",protocols:{cite:["http","https"],href:["http","https","irc","ircs","mailto","xmpp"],longDesc:["http","https"],src:["http","https"]},required:{input:{disabled:!0,type:"checkbox"}},strip:["script"],tagNames:["a","b","blockquote","br","code","dd","del","details","div","dl","dt","em","h1","h2","h3","h4","h5","h6","hr","i","img","input","ins","kbd","li","ol","p","picture","pre","q","rp","rt","ruby","s","samp","section","source","span","strike","strong","sub","summary","sup","table","tbody","td","tfoot","th","thead","tr","tt","ul","var"]},X={}.hasOwnProperty;function ee(e,t){if(t&&"object"==typeof t)switch("string"==typeof t.type?t.type:""){case"comment":var r=e,a=t;if(r.schema.allowComments){let e="string"==typeof a.value?a.value:"",t=e.indexOf("--\x3e"),r={type:"comment",value:t<0?e:e.slice(0,t)};return en(r,a),r}return;case"doctype":var n=e,s=t;if(n.schema.allowDoctypes){let e={type:"doctype"};return en(e,s),e}return;case"element":return function(e,t){let r="string"==typeof t.tagName?t.tagName:"";e.stack.push(r);let a=et(e,t.children),n=function(e,t){let r,a=e.stack[e.stack.length-1],n=e.schema.attributes,s=e.schema.required,i=n&&X.call(n,a)?n[a]:void 0,o=n&&X.call(n,"*")?n["*"]:void 0,l=t&&"object"==typeof t?t:{},c={};for(r in l)if(X.call(l,r)){let t=l[r],a=er(e,es(i,r),r,t);null==a&&(a=er(e,es(o,r),r,t)),null!=a&&(c[r]=a)}if(s&&X.call(s,a)){let e=s[a];for(r in e)X.call(e,r)&&!X.call(c,r)&&(c[r]=e[r])}return c}(e,t.properties);e.stack.pop();let s=!1;if(r&&"*"!==r&&(!e.schema.tagNames||e.schema.tagNames.includes(r))&&(s=!0,e.schema.ancestors&&X.call(e.schema.ancestors,r))){let t=e.schema.ancestors[r],a=-1;for(s=!1;++a<t.length;)e.stack.includes(t[a])&&(s=!0)}if(!s)return e.schema.strip&&!e.schema.strip.includes(r)?a:void 0;let i={type:"element",tagName:r,properties:n,children:a};return en(i,t),i}(e,t);case"root":var i=e,o=t;let l={type:"root",children:et(i,o.children)};return en(l,o),l;case"text":var c=0,d=t;let u={type:"text",value:"string"==typeof d.value?d.value:""};return en(u,d),u}}function et(e,t){let r=[];if(Array.isArray(t)){let a=-1;for(;++a<t.length;){let n=ee(e,t[a]);n&&(Array.isArray(n)?r.push(...n):r.push(n))}}return r}function er(e,t,r,a){return t?Array.isArray(a)?function(e,t,r,a){let n=-1,s=[];for(;++n<a.length;){let i=ea(e,t,r,a[n]);("number"==typeof i||"string"==typeof i)&&s.push(i)}return s}(e,t,r,a):ea(e,t,r,a):void 0}function ea(e,t,r,a){if(("boolean"==typeof a||"number"==typeof a||"string"==typeof a)&&function(e,t,r){let a=e.schema.protocols&&X.call(e.schema.protocols,t)?e.schema.protocols[t]:void 0;if(!a||0===a.length)return!0;let n=String(r),s=n.indexOf(":"),i=n.indexOf("?"),o=n.indexOf("#"),l=n.indexOf("/");if(s<0||l>-1&&s>l||i>-1&&s>i||o>-1&&s>o)return!0;let c=-1;for(;++c<a.length;){let e=a[c];if(s===e.length&&n.slice(0,e.length)===e)return!0}return!1}(e,r,a)){if("object"==typeof t&&t.length>1){let e=!1,r=0;for(;++r<t.length;){let n=t[r];if(n&&"object"==typeof n&&"flags"in n){if(n.test(String(a))){e=!0;break}}else if(n===a){e=!0;break}}if(!e)return}return e.schema.clobber&&e.schema.clobberPrefix&&e.schema.clobber.includes(r)?e.schema.clobberPrefix+a:a}}function en(e,t){let r=(0,K.G1)(t);t.data&&(e.data=(0,Y.Ay)(t.data)),r&&(e.position=r)}function es(e,t){let r,a=-1;if(e)for(;++a<e.length;){let n=e[a],s="string"==typeof n?n:n[0];if(s===t)return n;"data*"===s&&(r=n)}if(t.length>4&&"data"===t.slice(0,4).toLowerCase())return r}var ei=r(51104);let eo=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"];var el=r(91256);let ec=/["&'<>`]/g,ed=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,eu=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,em=/[|\\{}()[\]^$+*?.]/g,ep=new WeakMap,eh=/[\dA-Fa-f]/,eg=/\d/,ef=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],ex={nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},eb=["cent","copy","divide","gt","lt","not","para","times"],ey={}.hasOwnProperty,ev={};for(a in ex)ey.call(ex,a)&&(ev[ex[a]]=a);let eN=/[^\dA-Za-z]/;function ew(e,t,r){let a,n=function(e,t,r){let a="&#x"+e.toString(16).toUpperCase();return r&&t&&!eh.test(String.fromCharCode(t))?a:a+";"}(e,t,r.omitOptionalSemicolons);if((r.useNamedReferences||r.useShortestReferences)&&(a=function(e,t,r,a){let n=String.fromCharCode(e);if(ey.call(ev,n)){let e=ev[n],s="&"+e;return r&&ef.includes(e)&&!eb.includes(e)&&(!a||t&&61!==t&&eN.test(String.fromCharCode(t)))?s:s+";"}return""}(e,t,r.omitOptionalSemicolons,r.attribute)),(r.useShortestReferences||!a)&&r.useShortestReferences){let a=function(e,t,r){let a="&#"+String(e);return r&&t&&!eg.test(String.fromCharCode(t))?a:a+";"}(e,t,r.omitOptionalSemicolons);a.length<n.length&&(n=a)}return a&&(!r.useShortestReferences||a.length<n.length)?a:n}function ej(e,t){let r;var a,n=e,s=Object.assign({format:ew},t);if(n=n.replace(s.subset?(a=s.subset,(r=ep.get(a))||(r=function(e){let t=[],r=-1;for(;++r<e.length;)t.push(e[r].replace(em,"\\$&"));return RegExp("(?:"+t.join("|")+")","g")}(a),ep.set(a,r)),r):ec,i),s.subset||s.escapeOnly)return n;return n.replace(ed,function(e,t,r){return s.format((e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536,r.charCodeAt(t+2),s)}).replace(eu,i);function i(e,t,r){return s.format(e.charCodeAt(0),r.charCodeAt(t+1),s)}}let eA=/^>|^->|<!--|-->|--!>|<!-$/g,eC=[">"],ek=["<",">"];var e_=r(98270),eS=r(33788),eI=r(277),eO=r(11715),eq=r(45717);let eE=eD(1),eT=eD(-1),eP=[];function eD(e){return function(t,r,a){let n=t?t.children:eP,s=(r||0)+e,i=n[s];if(!a)for(;i&&(0,eq.m)(i);)s+=e,i=n[s];return i}}let eL={}.hasOwnProperty;function eF(e){return function(t,r,a){return eL.call(e,t.tagName)&&e[t.tagName](t,r,a)}}let e$=eF({body:function(e,t,r){let a=eE(r,t);return!a||"comment"!==a.type},caption:eR,colgroup:eR,dd:function(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&("dt"===a.tagName||"dd"===a.tagName)},dt:function(e,t,r){let a=eE(r,t);return!!(a&&"element"===a.type&&("dt"===a.tagName||"dd"===a.tagName))},head:eR,html:function(e,t,r){let a=eE(r,t);return!a||"comment"!==a.type},li:function(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&"li"===a.tagName},optgroup:function(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&"optgroup"===a.tagName},option:function(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&("option"===a.tagName||"optgroup"===a.tagName)},p:function(e,t,r){let a=eE(r,t);return a?"element"===a.type&&("address"===a.tagName||"article"===a.tagName||"aside"===a.tagName||"blockquote"===a.tagName||"details"===a.tagName||"div"===a.tagName||"dl"===a.tagName||"fieldset"===a.tagName||"figcaption"===a.tagName||"figure"===a.tagName||"footer"===a.tagName||"form"===a.tagName||"h1"===a.tagName||"h2"===a.tagName||"h3"===a.tagName||"h4"===a.tagName||"h5"===a.tagName||"h6"===a.tagName||"header"===a.tagName||"hgroup"===a.tagName||"hr"===a.tagName||"main"===a.tagName||"menu"===a.tagName||"nav"===a.tagName||"ol"===a.tagName||"p"===a.tagName||"pre"===a.tagName||"section"===a.tagName||"table"===a.tagName||"ul"===a.tagName):!r||"element"!==r.type||"a"!==r.tagName&&"audio"!==r.tagName&&"del"!==r.tagName&&"ins"!==r.tagName&&"map"!==r.tagName&&"noscript"!==r.tagName&&"video"!==r.tagName},rp:eU,rt:eU,tbody:function(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&("tbody"===a.tagName||"tfoot"===a.tagName)},td:eB,tfoot:function(e,t,r){return!eE(r,t)},th:eB,thead:function(e,t,r){let a=eE(r,t);return!!(a&&"element"===a.type&&("tbody"===a.tagName||"tfoot"===a.tagName))},tr:function(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&"tr"===a.tagName}});function eR(e,t,r){let a=eE(r,t,!0);return!a||"comment"!==a.type&&!("text"===a.type&&(0,eq.m)(a.value.charAt(0)))}function eU(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&("rp"===a.tagName||"rt"===a.tagName)}function eB(e,t,r){let a=eE(r,t);return!a||"element"===a.type&&("td"===a.tagName||"th"===a.tagName)}let ez=eF({body:function(e){let t=eE(e,-1,!0);return!t||"comment"!==t.type&&!("text"===t.type&&(0,eq.m)(t.value.charAt(0)))&&("element"!==t.type||"meta"!==t.tagName&&"link"!==t.tagName&&"script"!==t.tagName&&"style"!==t.tagName&&"template"!==t.tagName)},colgroup:function(e,t,r){let a=eT(r,t),n=eE(e,-1,!0);return!(r&&a&&"element"===a.type&&"colgroup"===a.tagName&&e$(a,r.children.indexOf(a),r))&&!!(n&&"element"===n.type&&"col"===n.tagName)},head:function(e){let t=new Set;for(let r of e.children)if("element"===r.type&&("base"===r.tagName||"title"===r.tagName)){if(t.has(r.tagName))return!1;t.add(r.tagName)}let r=e.children[0];return!r||"element"===r.type},html:function(e){let t=eE(e,-1);return!t||"comment"!==t.type},tbody:function(e,t,r){let a=eT(r,t),n=eE(e,-1);return!(r&&a&&"element"===a.type&&("thead"===a.tagName||"tbody"===a.tagName)&&e$(a,r.children.indexOf(a),r))&&!!(n&&"element"===n.type&&"tr"===n.tagName)}}),eM={name:[["	\n\f\r &/=>".split(""),"	\n\f\r \"&'/=>`".split("")],["\0	\n\f\r \"&'/<=>".split(""),"\0	\n\f\r \"&'/<=>`".split("")]],unquoted:[["	\n\f\r &>".split(""),"\0	\n\f\r \"&'<=>`".split("")],["\0	\n\f\r \"&'<=>`".split(""),"\0	\n\f\r \"&'<=>`".split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]},eG=["<","&"];function eH(e,t,r,a){return r&&"element"===r.type&&("script"===r.tagName||"style"===r.tagName)?e.value:ej(e.value,Object.assign({},a.settings.characterReferences,{subset:eG}))}let eJ=A("type",{invalid:function(e){throw Error("Expected node, not `"+e+"`")},unknown:function(e){throw Error("Cannot compile unknown node `"+e.type+"`")},handlers:{comment:function(e,t,r,a){return a.settings.bogusComments?"<?"+ej(e.value,Object.assign({},a.settings.characterReferences,{subset:eC}))+">":"\x3c!--"+e.value.replace(eA,function(e){return ej(e,Object.assign({},a.settings.characterReferences,{subset:ek}))})+"--\x3e"},doctype:function(e,t,r,a){return"<!"+(a.settings.upperDoctype?"DOCTYPE":"doctype")+(a.settings.tightDoctype?"":" ")+"html>"},element:function(e,t,r,a){let n,s=a.schema,i="svg"!==s.space&&a.settings.omitOptionalTags,o="svg"===s.space?a.settings.closeEmptyElements:a.settings.voids.includes(e.tagName.toLowerCase()),l=[];"html"===s.space&&"svg"===e.tagName&&(a.schema=el.JW);let c=function(e,t){let r,a=[],n=-1;if(t){for(r in t)if(null!==t[r]&&void 0!==t[r]){let n=function(e,t,r){let a,n=(0,eI.I)(e.schema,t),s=e.settings.allowParseErrors&&"html"===e.schema.space?0:1,i=+!e.settings.allowDangerousCharacters,o=e.quote;if(n.overloadedBoolean&&(r===n.attribute||""===r)?r=!0:(n.boolean||n.overloadedBoolean)&&("string"!=typeof r||r===n.attribute||""===r)&&(r=!!r),null==r||!1===r||"number"==typeof r&&Number.isNaN(r))return"";let l=ej(n.attribute,Object.assign({},e.settings.characterReferences,{subset:eM.name[s][i]}));return!0===r||(r=Array.isArray(r)?(n.commaSeparated?eS.A:eO.A)(r,{padLeft:!e.settings.tightCommaSeparatedLists}):String(r),e.settings.collapseEmptyAttributes&&!r)?l:(e.settings.preferUnquoted&&(a=ej(r,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:eM.unquoted[s][i]}))),a!==r&&(e.settings.quoteSmart&&(0,e_.D)(r,o)>(0,e_.D)(r,e.alternative)&&(o=e.alternative),a=o+ej(r,Object.assign({},e.settings.characterReferences,{subset:("'"===o?eM.single:eM.double)[s][i],attribute:!0}))+o),l+(a?"="+a:a))}(e,r,t[r]);n&&a.push(n)}}for(;++n<a.length;){let t=e.settings.tightAttributes?a[n].charAt(a[n].length-1):void 0;n!==a.length-1&&'"'!==t&&"'"!==t&&(a[n]+=" ")}return a.join("")}(a,e.properties),d=a.all("html"===s.space&&"template"===e.tagName?e.content:e);return a.schema=s,d&&(o=!1),!c&&i&&ez(e,t,r)||(l.push("<",e.tagName,c?" "+c:""),o&&("svg"===s.space||a.settings.closeSelfClosing)&&(n=c.charAt(c.length-1),(!a.settings.tightSelfClosing||"/"===n||n&&'"'!==n&&"'"!==n)&&l.push(" "),l.push("/")),l.push(">")),l.push(d),o||i&&e$(e,t,r)||l.push("</"+e.tagName+">"),l.join("")},raw:function(e,t,r,a){return a.settings.allowDangerousHtml?e.value:eH(e,t,r,a)},root:function(e,t,r,a){return a.all(e)},text:eH}}),eW={},eZ={},eY=[];function eK(e,t,r){return eJ(e,t,r,this)}function eQ(e){let t=[],r=e&&e.children||eY,a=-1;for(;++a<r.length;)t[a]=this.one(r[a],a,e);return t.join("")}let eV={};function eX(e){let t,{handlers:r,sanitize:a,...n}=e||eV,s=!1;"boolean"==typeof a?s=!a:a&&(t=a),this.compiler=function(e,a){let i=(0,ei._)(e,{handlers:r,allowDangerousHtml:s}),o=function(e,t){let r=t||eW,a=r.quote||'"';if('"'!==a&&"'"!==a)throw Error("Invalid quote `"+a+"`, expected `'` or `\"`");return({one:eK,all:eQ,settings:{omitOptionalTags:r.omitOptionalTags||!1,allowParseErrors:r.allowParseErrors||!1,allowDangerousCharacters:r.allowDangerousCharacters||!1,quoteSmart:r.quoteSmart||!1,preferUnquoted:r.preferUnquoted||!1,tightAttributes:r.tightAttributes||!1,upperDoctype:r.upperDoctype||!1,tightDoctype:r.tightDoctype||!1,bogusComments:r.bogusComments||!1,tightCommaSeparatedLists:r.tightCommaSeparatedLists||!1,tightSelfClosing:r.tightSelfClosing||!1,collapseEmptyAttributes:r.collapseEmptyAttributes||!1,allowDangerousHtml:r.allowDangerousHtml||!1,voids:r.voids||eo,characterReferences:r.characterReferences||eZ,closeSelfClosing:r.closeSelfClosing||!1,closeEmptyElements:r.closeEmptyElements||!1},schema:"svg"===r.space?el.JW:el.qy,quote:a,alternative:'"'===a?"'":'"'}).one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}(s?i:function(e,t){let r={type:"root",children:[]},a=ee({schema:t?{...V,...t}:V,stack:[]},e);return a&&(Array.isArray(a)?1===a.length?r=a[0]:r.children=a:r=a),r}(i,t),{...n,allowDangerousHtml:s});return a.extname&&(a.extname=".html"),e&&"root"===e.type&&o&&/[^\r\n]/.test(o.charAt(o.length-1))?o+"\n":o}}var e0=r(68798);async function e1({content:e,className:t=""}){let r=(await Z().use(e0.A).use(eX,{sanitize:!1}).process(e)).toString();return(0,n.jsx)("div",{className:`prose prose-slate max-w-none ${t}`,style:{listStylePosition:"inside",paddingLeft:"1.25rem"},dangerouslySetInnerHTML:{__html:r}})}async function e3(e,t){try{let r=await g.u.getTools({category:e,limit:4,status:"approved"});if(r.success&&r.data)return r.data.tools.filter(e=>e._id!==t);return[]}catch(e){return console.error("Error fetching related tools:",e),[]}}async function e2({initialTool:e,toolId:t,locale:r}){let a=await (0,o.A)({locale:r,namespace:"tool_detail"}),i=await (0,o.A)({locale:r,namespace:"tags"}),c=await e3(e.category,t);return(0,n.jsx)(h.default,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,n.jsxs)("div",{className:"lg:col-span-2",children:[(0,n.jsxs)("article",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,n.jsxs)("header",{className:"flex items-start justify-between mb-6",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,n.jsx)(N.A,{tool:e,size:"sm"}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:e.name}),e.tagline&&(0,n.jsx)("p",{className:"text-gray-600 mb-2",children:e.tagline}),(0,n.jsx)("span",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${(0,f.Ef)(e.pricing)}`,children:(0,f.mV)(e.pricing)})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)(l.default,{toolId:t,size:"lg"}),(0,n.jsxs)("a",{href:e.website,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,n.jsx)(x.A,{className:"mr-2 h-4 w-4"}),a("visit_website")]})]})]}),(0,n.jsx)("div",{className:"prose max-w-none markdown-body",children:(0,n.jsx)(e1,{content:e.description})}),e.tags&&e.tags.length>0&&(0,n.jsxs)("div",{className:"mt-6",children:[(0,n.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-3 flex items-center",children:[(0,n.jsx)(b.A,{className:"mr-2 h-5 w-5"}),a("tags")]}),(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map((e,t)=>(0,n.jsx)("span",{className:"inline-flex items-center px-3 py-1 rounded-full text-sm bg-gray-100 text-gray-800",children:i(e)},t))})]}),(0,n.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,n.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,n.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,n.jsx)(y.A,{className:"mr-2 h-5 w-5"}),(0,n.jsxs)("span",{children:[e.views||0," ",a("views")]})]}),(0,n.jsxs)("div",{className:"flex items-center text-gray-600",children:[(0,n.jsx)(v.A,{className:"mr-2 h-5 w-5"}),(0,n.jsxs)("span",{children:[e.likes||0," ",a("likes")]})]})]})})]}),(0,n.jsx)(p,{toolId:t})]}),(0,n.jsxs)("aside",{className:"lg:col-span-1",children:[(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:a("tool_info")}),(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-gray-600",children:a("category")}),(0,n.jsx)("span",{className:"text-gray-900 font-medium",children:e.category})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-gray-600",children:a("pricing_model")}),(0,n.jsx)("span",{className:`px-2 py-1 rounded text-sm font-medium ${(0,f.Ef)(e.pricing)}`,children:(0,f.mV)(e.pricing)})]}),e.launchDate&&(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("span",{className:"text-gray-600",children:a("launch_date")}),(0,n.jsx)("span",{className:"text-gray-900 font-medium",children:new Date(e.launchDate).toLocaleDateString()})]})]})]}),c.length>0&&(0,n.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:a("related_tools")}),(0,n.jsx)("div",{className:"space-y-4",children:c.map(e=>(0,n.jsx)(s.N_,{href:`/tools/${e._id}`,className:"block p-3 rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-sm transition-all",children:(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[e.logo?(0,n.jsx)("img",{src:e.logo,alt:`${e.name} logo`,className:"w-10 h-10 rounded object-cover"}):(0,n.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-white font-bold text-sm",children:e.name.charAt(0).toUpperCase()})}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate",children:e.name}),(0,n.jsx)("p",{className:"text-xs text-gray-500 truncate",children:e.tagline})]})]})},e._id))})]})]})]})})}var e6=r(54290);async function e4({params:e}){try{let{id:t,locale:r}=await e,a=await (0,o.A)({locale:r,namespace:"tool_detail"}),n=await g.u.getTool(t);if(!n.success||!n.data){let e=await (0,o.A)({locale:r,namespace:"site"});return{title:`${a("not_found")} - ${e("title")}`,description:a("not_found_desc")}}let s=n.data,i=await (0,o.A)({locale:r,namespace:"site"}),l=`${s.name} - ${i("title")}`,c=s.description||`${s.name} is an excellent AI tool to boost your productivity.`,d=[s.name,...s.tags||[],"AI tools",s.category].join(", "),u=process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",m=`/tools/${s._id}`,p=s.logo||"/og-tool-default.jpg";return{title:l,description:c,keywords:d,authors:[{name:i("title")}],robots:{index:!0,follow:!0},openGraph:{type:"article",locale:"zh"===r?"zh_CN":"en_US",url:`${u}${m}`,siteName:i("title"),title:l,description:c,images:[{url:p.startsWith("http")?p:`${u}${p}`,width:1200,height:630,alt:`${s.name} - ${i("title")}`}],publishedTime:s.launchDate?new Date(s.launchDate).toISOString():void 0,modifiedTime:s.updatedAt?new Date(s.updatedAt).toISOString():void 0},twitter:{card:"summary_large_image",title:l,description:c,images:[p.startsWith("http")?p:`${u}${p}`]},alternates:{canonical:`${u}${m}`}}}catch(r){let e=await (0,o.A)({locale:"zh",namespace:"site"}),t=await (0,o.A)({locale:"zh",namespace:"tool_detail"});return{title:`${t("page_title")} - ${e("title")}`,description:t("not_found_desc")}}}async function e5({params:e}){try{let{id:t,locale:r}=await e,a=await (0,o.A)({locale:r,namespace:"tool_detail"}),l=await g.u.getTool(t);l.success&&l.data||(0,i.notFound)();let c=l.data,d=(0,e6.sU)(c),u=(0,e6.hC)([{name:a("breadcrumb_home"),url:"/"},{name:a("breadcrumb_tools"),url:"/tools"},{name:c.name,url:`/tools/${c._id}`}]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(d)}}),(0,n.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(u)}}),(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,n.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6","aria-label":a("breadcrumb_aria_label"),children:[(0,n.jsx)(s.N_,{href:"/",className:"hover:text-blue-600",children:a("breadcrumb_home")}),(0,n.jsx)("span",{children:"/"}),(0,n.jsx)(s.N_,{href:"/tools",className:"hover:text-blue-600",children:a("breadcrumb_tools")}),(0,n.jsx)("span",{children:"/"}),(0,n.jsx)("span",{className:"text-gray-900",children:c.name})]}),(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)(s.N_,{href:"/tools",className:"inline-flex items-center text-blue-600 hover:text-blue-700",children:[(0,n.jsx)("svg",{className:"mr-2 h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),a("back_to_tools")]})}),(0,n.jsx)(e2,{initialTool:c,toolId:t,locale:r})]})]})}catch(e){console.error("Error loading tool:",e),(0,i.notFound)()}}},73136:e=>{"use strict";e.exports=require("node:url")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(56037),n=r.n(a);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let i=global.mongoose;i||(i=global.mongoose={conn:null,promise:null});let o=async function(){if(i.conn)return i.conn;i.promise||(i.promise=n().connect(s,{bufferCommands:!1}));try{i.conn=await i.promise}catch(e){throw i.promise=null,e}return i.conn}},76760:e=>{"use strict";e.exports=require("node:path")},79551:e=>{"use strict";e.exports=require("url")},80555:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx","default")},86442:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var a=r(65239),n=r(48088),s=r(88170),i=r.n(s),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["tools",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65030)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/[locale]/tools/[id]/page",pathname:"/[locale]/tools/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91966:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80555)),Promise.resolve().then(r.bind(r,21659)),Promise.resolve().then(r.bind(r,93331))},93331:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,6435,6699,1430,3577,2585,2608],()=>r(86442));module.exports=a})();