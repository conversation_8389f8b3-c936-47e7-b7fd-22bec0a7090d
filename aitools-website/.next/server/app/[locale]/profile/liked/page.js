(()=>{var e={};e.id=2797,e.ids=[2797],e.modules={124:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var i=r(65239),s=r(48088),a=r(88170),o=r.n(a),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["profile",{children:["liked",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,77405)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/[locale]/profile/liked/page",pathname:"/[locale]/profile/liked",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11011:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var i=r(60687),s=r(93613),a=r(11860);function o({message:e,onClose:t,className:r=""}){return(0,i.jsx)("div",{className:`bg-red-50 border border-red-200 rounded-lg p-4 ${r}`,children:(0,i.jsxs)("div",{className:"flex items-start",children:[(0,i.jsx)(s.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsx)("p",{className:"text-red-800 text-sm",children:e})}),t&&(0,i.jsx)("button",{onClick:t,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,i.jsx)(a.A,{className:"w-4 h-4"})})]})})}},11723:e=>{"use strict";e.exports=require("querystring")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var i=r(36344),s=r(65752),a=r(13581),o=r(75745),n=r(17063);let l={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,s.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let t=await n.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,s]=r.split(":");if(i!==e.token||s!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,o.A)();try{let i=await n.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new n.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),s=r.n(i);let a=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let n=s().models.User||s().model("User",o)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26860:(e,t,r)=>{"use strict";r.d(t,{J2:()=>a,VP:()=>o});var i=r(77618);let s=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];function a(){let e=(0,i.c3)("categories"),t=(function(){let e=(0,i.c3)("categories");return s.map(t=>({slug:t.slug,name:e(`category_names.${t.slug}`),description:e(`category_descriptions.${t.slug}`),icon:t.icon,color:t.color}))})().map(e=>({value:e.slug,label:e.name}));return[{value:"",label:e("all_categories")},...t]}function o(e){return(0,i.c3)("categories")(`category_names.${e}`)||e}async function n(e){let t=await getTranslations({locale:e||"en",namespace:"categories"});return s.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}s.map(e=>e.slug),s.reduce((e,t)=>(e[t.slug]=t,e),{})},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29703:(e,t,r)=>{"use strict";r.d(t,{P5:()=>n,Tr:()=>a,Y5:()=>i,kx:()=>l,w8:()=>o});let i={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:8e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},s={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function a(e){let t=s.BASE_DIR,r=s.DIRECTORIES[e];return`${t}/${r}`}function o(e,t){let r=s.DIRECTORIES[e];return`/api/uploads/${r}/${t}`}function n(e,t){let r=i[e];if(!r)return{isValid:!0};let s=t.trim().length;return s<r.min?{isValid:!1,error:`${r.label} must be at least ${r.min} characters long`}:s>r.max?{isValid:!1,error:`${r.label} cannot exceed ${r.max} characters`}:{isValid:!0}}function l(e,t){var r;let i=function(e,t){let r=s.FILE_SIZE_LIMITS[e];if(t>r){let e=Math.round(r/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!i.isValid)return i;let a=s.ALLOWED_TYPES.IMAGES,o=(r=e.type,a.includes(r)?{isValid:!0}:{isValid:!1,error:`File type ${r} is not allowed. Allowed types: ${a.join(", ")}`});return o.isValid?{isValid:!0}:o}},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var i=r(56037),s=r.n(i),a=r(60366),o=r(29703);let n=new i.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,minlength:[o.Y5.TOOL_NAME.min,`Tool name must be at least ${o.Y5.TOOL_NAME.min} characters`],maxlength:[o.Y5.TOOL_NAME.max,`Tool name cannot exceed ${o.Y5.TOOL_NAME.max} characters`]},tagline:{type:String,trim:!0,maxlength:[o.Y5.TAGLINE.max,`Tagline cannot exceed ${o.Y5.TAGLINE.max} characters`]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,minlength:[o.Y5.DESCRIPTION.min,`Description must be at least ${o.Y5.DESCRIPTION.min} characters`],maxlength:[o.Y5.DESCRIPTION.max,`Description cannot exceed ${o.Y5.DESCRIPTION.max} characters`]},longDescription:{type:String,trim:!0,maxlength:[o.Y5.LONG_DESCRIPTION.max,`Long description cannot exceed ${o.Y5.LONG_DESCRIPTION.max} characters`]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,maxlength:[o.Y5.WEBSITE_URL.max,`Website URL cannot exceed ${o.Y5.WEBSITE_URL.max} characters`],validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:a.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[o.Y5.PRICING_DETAILS.max,`Pricing details cannot exceed ${o.Y5.PRICING_DETAILS.max} characters`]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},badgeVerified:{type:Boolean,default:!1},badgeVerifiedAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=s().models.Tool||s().model("Tool",n)},33873:e=>{"use strict";e.exports=require("path")},41801:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx","default")},52763:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var i=r(60687),s=r(43210),a=r(16189),o=r(12340),n=r(11011),l=r(26860),c=r(28559),d=r(99270),u=r(80462),m=r(67760),p=r(39581);function g({slug:e}){let t=(0,l.VP)(e);return(0,i.jsx)(i.Fragment,{children:t})}function x({initialTools:e}){let t=(0,a.useParams)(),r=t?.locale||"zh",[l,x]=(0,s.useState)(e),[h,v]=(0,s.useState)(e),[y,f]=(0,s.useState)(""),[b,A]=(0,s.useState)(""),[w,S]=(0,s.useState)("all"),E=async e=>{try{x(t=>t.filter(t=>t._id!==e)),f("")}catch(e){console.error("Error unliking tool:",e),f("取消收藏失败，请重试")}},I=Array.from(new Set(l.map(e=>e.category))),T="en"===r?{title:"My Favorites",subtitle:`Your favorite AI tools (${l.length})`,searchPlaceholder:"Search favorite tools...",allCategories:"All Categories",noToolsFound:"No matching tools found",noToolsYet:"No favorite tools yet",adjustFilters:"Try adjusting your search criteria or filters",startExploring:"Start exploring and favorite your preferred AI tools!",browseTools:"Browse Tools"}:{title:"我的收藏",subtitle:`您收藏的AI工具 (${l.length})`,searchPlaceholder:"搜索收藏的工具...",allCategories:"所有分类",noToolsFound:"没有找到匹配的工具",noToolsYet:"还没有收藏任何工具",adjustFilters:"尝试调整搜索条件或筛选器",startExploring:"开始探索并收藏您喜欢的AI工具吧！",browseTools:"浏览工具"};return(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,i.jsx)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-8",children:(0,i.jsxs)("div",{children:[(0,i.jsxs)("div",{className:"flex items-center mb-2",children:[(0,i.jsx)(o.N_,{href:"/profile",className:"mr-4 p-2 text-gray-400 hover:text-gray-600 transition-colors",children:(0,i.jsx)(c.A,{className:"h-5 w-5"})}),(0,i.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:T.title})]}),(0,i.jsx)("p",{className:"text-lg text-gray-600",children:T.subtitle})]})}),(0,i.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6",children:(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,i.jsx)("div",{className:"flex-1",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)(d.A,{className:"h-5 w-5 text-gray-400"})}),(0,i.jsx)("input",{type:"text",placeholder:T.searchPlaceholder,value:b,onChange:e=>A(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})}),(0,i.jsx)("div",{className:"sm:w-48",children:(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,i.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,i.jsxs)("select",{value:w,onChange:e=>S(e.target.value),className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,i.jsx)("option",{value:"all",children:T.allCategories}),I.map(e=>(0,i.jsx)("option",{value:e,children:(0,i.jsx)(g,{slug:e})},e))]})]})})]})}),y&&(0,i.jsx)(n.default,{message:y,onClose:()=>f(""),className:"mb-6"}),h.length>0?(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:h.map(e=>(0,i.jsx)(p.A,{tool:e,onUnlike:E,isInLikedPage:!0},e._id))}):(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center",children:[(0,i.jsx)("div",{className:"text-gray-400 mb-4",children:(0,i.jsx)(m.A,{className:"h-12 w-12 mx-auto"})}),(0,i.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:b||"all"!==w?T.noToolsFound:T.noToolsYet}),(0,i.jsx)("p",{className:"text-gray-600 mb-4",children:b||"all"!==w?T.adjustFilters:T.startExploring}),!b&&"all"===w&&(0,i.jsx)(o.N_,{href:"/tools",className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 transition-colors",children:T.browseTools})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55779:(e,t,r)=>{Promise.resolve().then(r.bind(r,75788)),Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,41801))},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>o,PZ:()=>n,RI:()=>c,ut:()=>l});var i=r(64348);let s=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function a(e){let t=await (0,i.A)({locale:e||"en",namespace:"categories"});return s.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function o(e){return(await a(e)).map(e=>({value:e.slug,label:e.name}))}async function n(e,t){return(await a(t)).find(t=>t.slug===e)}let l=s.map(e=>e.slug),c=s.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69851:(e,t,r)=>{Promise.resolve().then(r.bind(r,39130)),Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,52763))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var i=r(56037),s=r.n(i);let a=process.env.MONGODB_URI;if(!a)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let n=async function(){if(o.conn)return o.conn;o.promise||(o.promise=s().connect(a,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},77405:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var i=r(37413),s=r(61120),a=r(35426),o=r(78878),n=r(12909),l=r(75745),c=r(17063),d=r(30762),u=r(41801);async function m(e){await (0,l.A)();let t=await c.A.findOne({email:e});if(!t)return[];let r=t.likedTools||[];return 0===r.length?[]:(await d.A.find({_id:{$in:r},isActive:!0,status:"approved"}).sort({updatedAt:-1}).lean()).map(e=>({_id:e._id.toString(),name:e.name,description:e.description,website:e.website,logo:e.logo,category:e.category,tags:e.tags||[],pricing:e.pricing,views:e.views||0,likes:e.likes||0,status:e.status,isActive:e.isActive,createdAt:e.createdAt,updatedAt:e.updatedAt,launchDate:e.launchDate,submittedBy:e.submittedBy?.toString(),submittedAt:e.submittedAt||e.createdAt,reviewedBy:e.reviewedBy?.toString(),reviewNotes:e.reviewNotes,rejectReason:e.rejectReason,likedBy:e.likedBy?.map(e=>e.toString())||[],tagline:e.tagline}))}async function p({params:e}){let t=await (0,a.getServerSession)(n.N),{locale:r}=await e;t?.user?.email||(0,o.V2)({href:"/",locale:r});let l=await m(t?.user?.email||"");return(0,i.jsx)(s.Fragment,{children:(0,i.jsx)(u.default,{initialTools:l})})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80462:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},81630:e=>{"use strict";e.exports=require("http")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,9658,6435,6699,1430,3136,2585,9581],()=>r(124));module.exports=i})();