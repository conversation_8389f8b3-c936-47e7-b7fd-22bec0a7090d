(()=>{var e={};e.id=5230,e.ids=[5230],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8893:(e,t,r)=>{Promise.resolve().then(r.bind(r,48021))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27390:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=r(65239),o=r(48088),n=r(88170),a=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c={children:["",{children:["[locale]",{children:["search",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46252)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,32410)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/[locale]/search/page",pathname:"/[locale]/search",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34960:(e,t,r)=>{"use strict";function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3011";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3011";return`http://localhost:${e}`}}function o(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function n(){return"production"}function a(){return"development"===n()}r.d(t,{aZ:()=>l,hY:()=>o});let i={baseUrl:s(),apiBaseUrl:o(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:n(),isDevelopment:a(),isProduction:"production"===n(),port:process.env.PORT||"3011"};function l(){return{generateProductInfo:process.env.AI_GENERATE_PRODUCT_INFO_URL||"http://localhost:50058/ai/generateProductInfo"}}a()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port),console.log("  AI Service URLs:",l()))},46252:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,generateMetadata:()=>l});var s=r(37413);r(61120);var o=r(39916),n=r(64348),a=r(49039),i=r(52343);async function l({params:e,searchParams:t}){let{locale:r}=await e,s=(await t).q||"",o=await (0,n.A)({locale:r,namespace:"search"});if(!s.trim())return{title:o("page_title"),description:o("page_description")};let a=o("page_title_with_query",{query:s}),i=o("page_description_with_query",{query:s});return{title:a,description:i,openGraph:{title:a,description:i}}}async function c({params:e,searchParams:t}){let{locale:r}=await e,n=await t,l=n.q||"",c=parseInt(n.page||"1"),d=n.category||"",u=n.sort||"createdAt";if(!l.trim())return(0,s.jsx)(a.default,{initialQuery:"",initialResults:null,initialCategories:[],locale:r});try{let[e,t]=await Promise.all([i.u.getTools({search:l,page:c,limit:12,category:d||void 0,sort:u,order:"desc"}),i.u.getCategories()]);if(!e.success)throw Error(e.error||"获取搜索结果失败");if(!t.success)throw Error(t.error||"获取分类失败");return(0,s.jsx)(a.default,{initialQuery:l,initialResults:e.data||null,initialCategories:t.data?.categories||[],initialPage:c,initialCategory:d,initialSort:u,locale:r})}catch(e){console.error("Search page error:",e),(0,o.notFound)()}}},48021:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(60687),o=r(43210),n=r(12340),a=r(16189),i=r(77618),l=r(56976),c=r(99270),d=r(39581);function u({initialQuery:e,initialResults:t,initialCategories:r,initialPage:u=1,initialCategory:p="",initialSort:h="createdAt",locale:g}){let m=(0,n.rd)(),y=(0,a.useSearchParams)(),x=(0,i.c3)("search"),[f,v]=(0,o.useState)(e),[P,b]=(0,o.useState)(t),[_]=(0,o.useState)(r),[U,L]=(0,o.useState)(p),[T,R]=(0,o.useState)(h),[w,S]=(0,o.useState)("grid"),[A,E]=(0,o.useState)(!1),[N,j]=(0,o.useState)(u),q=e=>{let t=new URLSearchParams(y||"");Object.entries(e).forEach(([e,r])=>{r?t.set(e,r):t.delete(e)}),m.push(`/search?${t.toString()}`)},$=async(e="",t=1,r="",s="createdAt")=>{E(!0);try{let o=await l.u.getTools({search:e,page:t,limit:12,category:r||void 0,sort:s,order:"desc"});o.success?b(o.data||null):(console.error("Search failed:",o.error),b(null))}catch(e){console.error("Search error:",e),b(null)}finally{E(!1)}},I=e=>{j(e),q({q:f,page:e.toString(),category:U,sort:T}),$(f,e,U,T)};return(0,s.jsx)(o.Fragment,{children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:x("title")}),f&&(0,s.jsxs)("p",{className:"text-lg text-gray-600",children:[x("search_results",{term:f}),P&&` - ${x("found_tools",{count:P.pagination.totalItems})}`]})]}),(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:(0,s.jsx)("form",{onSubmit:e=>{e?.preventDefault(),j(1),q({q:f?.trim(),page:"1",category:U,sort:T}),$(f?.trim(),1,U,T)},children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",placeholder:x("placeholder"),value:f,onChange:e=>v(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)(c.A,{className:"absolute left-3 top-3 h-5 w-5 text-gray-400"}),(0,s.jsxs)("button",{type:"submit",className:"absolute right-2 top-2 px-4 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",style:{whiteSpace:"nowrap",marginLeft:10},children:[!f.trim()&&x("all"),f.trim()&&x("search_button")]})]})})}),!f&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)(c.A,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:x("start_search_title")}),(0,s.jsx)("p",{className:"text-gray-600",children:x("start_search_desc")})]}),A&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-gray-600",children:x("searching")})]}),!A&&P&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("p",{className:"text-gray-600",children:[x("results_count",{showing:P.tools.length,total:P.pagination.totalItems}),U&&` ${x("in_category",{category:_.find(e=>e.id===U)?.name||""})}`]})}),P.tools.length>0?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"grid"===w?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8":"space-y-4 mb-8",children:P.tools.map(e=>(0,s.jsx)(d.A,{tool:e},e._id))}),P.pagination.totalPages>1&&(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("nav",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:()=>I(N-1),disabled:!P.pagination.hasPrevPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:x("prev_page")}),(0,s.jsx)("span",{className:"px-3 py-2 text-sm text-gray-700",children:x("page_info",{current:N,total:P.pagination.totalPages})}),(0,s.jsx)("button",{onClick:()=>I(N+1),disabled:!P.pagination.hasNextPage,className:"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:x("next_page")})]})})]}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)(c.A,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:x("no_results")}),(0,s.jsx)("p",{className:"text-gray-600",children:x("try_different_keywords")})]})]})]})})}},49039:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx","default")},52343:(e,t,r)=>{"use strict";r.d(t,{u:()=>n});let s=(0,r(34960).hY)();class o{constructor(e=s){this.baseURL=e}async request(e,t={}){try{let r=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...t.headers},...t};console.log("API request:",{url:r,config:s});let o=await fetch(r,s),n=await o.json();if(!o.ok)throw Error(n.error||`HTTP error! status: ${o.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/tools${r?`?${r}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/user/liked-tools${r?`?${r}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/admin/tools${r?`?${r}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}}let n=new o},56976:(e,t,r)=>{"use strict";function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3011";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3011";return`http://localhost:${e}`}}function o(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function n(){return"production"}function a(){return"development"===n()}r.d(t,{u:()=>d});let i={baseUrl:s(),apiBaseUrl:o(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:n(),isDevelopment:a(),isProduction:"production"===n(),port:process.env.PORT||"3011"};a()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",i.baseUrl),console.log("  API Base URL:",i.apiBaseUrl),console.log("  NextAuth URL:",i.nextAuthUrl),console.log("  Environment:",i.environment),console.log("  Port:",i.port),console.log("  AI Service URLs:",{generateProductInfo:process.env.AI_GENERATE_PRODUCT_INFO_URL||"http://localhost:50058/ai/generateProductInfo"}));let l=o();class c{constructor(e=l){this.baseURL=e}async request(e,t={}){try{let r=`${this.baseURL}${e}`,s={headers:{"Content-Type":"application/json",...t.headers},...t};console.log("API request:",{url:r,config:s});let o=await fetch(r,s),n=await o.json();if(!o.ok)throw Error(n.error||`HTTP error! status: ${o.status}`);return n}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/tools${r?`?${r}`:""}`)}async getTool(e){return this.request(`/tools/${e}`)}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request(`/tools/${e}`,{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request(`/tools/${e}`,{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/user/liked-tools${r?`?${r}`:""}`)}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(([e,r])=>{void 0!==r&&t.append(e,r.toString())});let r=t.toString();return this.request(`/admin/tools${r?`?${r}`:""}`)}async approveTool(e,t){return this.request(`/admin/tools/${e}/approve`,{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request(`/admin/tools/${e}/reject`,{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){let t=e?`?timeRange=${e}`:"";return this.request(`/admin/stats${t}`)}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request(`/orders/${e}`)}async processOrderPayment(e,t){return this.request(`/orders/${e}/pay`,{method:"POST",body:JSON.stringify(t)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}}let d=new c},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79551:e=>{"use strict";e.exports=require("url")},81277:(e,t,r)=>{Promise.resolve().then(r.bind(r,49039))},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,6435,6699,1430,2585,9581],()=>r(27390));module.exports=s})();