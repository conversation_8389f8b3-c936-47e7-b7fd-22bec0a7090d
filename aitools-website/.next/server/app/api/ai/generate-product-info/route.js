(()=>{var e={};e.id=5481,e.ids=[5481],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34960:(e,r,t)=>{"use strict";function s(){if(process.env.NEXT_PUBLIC_APP_URL)return process.env.NEXT_PUBLIC_APP_URL;{if(!process.env.VERCEL_URL&&!process.env.NETLIFY)return"http://localhost:3011";if(process.env.VERCEL_URL)return`https://${process.env.VERCEL_URL}`;if(process.env.NETLIFY&&process.env.URL)return process.env.URL;if(process.env.RAILWAY_STATIC_URL)return process.env.RAILWAY_STATIC_URL;if(process.env.APP_URL)return process.env.APP_URL;let e=process.env.PORT||"3011";return`http://localhost:${e}`}}function n(){if(process.env.NEXT_PUBLIC_API_BASE_URL)return process.env.NEXT_PUBLIC_API_BASE_URL;let e=s();return`${e}/api`}function o(){return"production"}function i(){return"development"===o()}t.d(r,{aZ:()=>c,hY:()=>n});let a={baseUrl:s(),apiBaseUrl:n(),nextAuthUrl:process.env.NEXTAUTH_URL?process.env.NEXTAUTH_URL:s(),environment:o(),isDevelopment:i(),isProduction:"production"===o(),port:process.env.PORT||"3011"};function c(){return{generateProductInfo:process.env.AI_GENERATE_PRODUCT_INFO_URL||"http://localhost:50058/ai/generateProductInfo"}}i()&&(console.log("\uD83D\uDD27 Dynamic Environment Configuration:"),console.log("  Base URL:",a.baseUrl),console.log("  API Base URL:",a.apiBaseUrl),console.log("  NextAuth URL:",a.nextAuthUrl),console.log("  Environment:",a.environment),console.log("  Port:",a.port),console.log("  AI Service URLs:",c()))},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return s},wellKnownProperties:function(){return o}});let t=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function s(e,r){return t.test(r)?"`"+e+"."+r+"`":"`"+e+"["+JSON.stringify(r)+"]`"}function n(e,r){let t=JSON.stringify(r);return"`Reflect.has("+e+", "+t+")`, `"+t+" in "+e+"`, or similar"}let o=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},93552:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{POST:()=>u});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),c=t(34960);async function u(e){try{let{website:r}=await e.json();if(!r)return a.NextResponse.json({success:!1,error:"Website URL is required"},{status:400});try{new URL(r)}catch(e){return a.NextResponse.json({success:!1,error:"Invalid website URL format"},{status:400})}let t=(0,c.aZ)(),s=await fetch(t.generateProductInfo,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({data:{url:r}})});if(!s.ok)return console.error("AI service error:",s.status,s.statusText),a.NextResponse.json({success:!1,error:"AI service is currently unavailable"},{status:503});let n=await s.json();if(!n.data?.generated?.productInfo)return console.error("Invalid AI response format:",n),a.NextResponse.json({success:!1,error:"Invalid response from AI service"},{status:502});let o=n.data.generated.productInfo,i=["name","tagline","description","category"].filter(e=>!o[e]);if(i.length>0)return console.error("Missing required fields in AI response:",i),a.NextResponse.json({success:!1,error:"Incomplete data from AI service"},{status:502});return a.NextResponse.json({success:!0,data:{name:o.name||"",tagline:o.tagline||"",description:o.description||"",category:o.category||"",tags:Array.isArray(o.tags)?o.tags:[]}})}catch(e){return console.error("Generate product info error:",e),a.NextResponse.json({success:!1,error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/ai/generate-product-info/route",pathname:"/api/ai/generate-product-info",filename:"route",bundlePath:"app/api/ai/generate-product-info/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/ai/generate-product-info/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:f}=p;function v(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580],()=>t(93552));module.exports=s})();