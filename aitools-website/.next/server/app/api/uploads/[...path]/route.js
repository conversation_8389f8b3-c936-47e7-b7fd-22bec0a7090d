(()=>{var e={};e.id=5758,e.ids=[5758],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29703:(e,t,s)=>{"use strict";s.d(t,{P5:()=>o,Tr:()=>i,Y5:()=>r,kx:()=>l,w8:()=>n});let r={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:8e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},a={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function i(e){let t=a.BASE_DIR,s=a.DIRECTORIES[e];return`${t}/${s}`}function n(e,t){let s=a.DIRECTORIES[e];return`/api/uploads/${s}/${t}`}function o(e,t){let s=r[e];if(!s)return{isValid:!0};let a=t.trim().length;return a<s.min?{isValid:!1,error:`${s.label} must be at least ${s.min} characters long`}:a>s.max?{isValid:!1,error:`${s.label} cannot exceed ${s.max} characters`}:{isValid:!0}}function l(e,t){var s;let r=function(e,t){let s=a.FILE_SIZE_LIMITS[e];if(t>s){let e=Math.round(s/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!r.isValid)return r;let i=a.ALLOWED_TYPES.IMAGES,n=(s=e.type,i.includes(s)?{isValid:!0}:{isValid:!1,error:`File type ${s} is not allowed. Allowed types: ${i.join(", ")}`});return n.isValid?{isValid:!0}:n}},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return r},wellKnownProperties:function(){return i}});let s=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function r(e,t){return s.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function a(e,t){let s=JSON.stringify(t);return"`Reflect.has("+e+", "+s+")`, `"+s+" in "+e+"`, or similar"}let i=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},79748:e=>{"use strict";e.exports=require("fs/promises")},89606:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>E,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var r={};s.r(r),s.d(r,{GET:()=>c});var a=s(96559),i=s(48088),n=s(37719),o=s(32190),l=s(79748),u=s(33873),p=s(29703);async function c(e,{params:t}){try{let{path:e}=await t;if(!e||0===e.length)return o.NextResponse.json({success:!1,message:"File path is required"},{status:400});let[s,...r]=e,a=r.join("/");if(!a)return o.NextResponse.json({success:!1,message:"File name is required"},{status:400});if(a.includes("..")||a.includes("\\")||s.includes(".."))return o.NextResponse.json({success:!1,message:"Invalid file path"},{status:400});let i=(0,p.Tr)(s?.toUpperCase()),n=(0,u.join)(i,a);try{let e=await (0,l.stat)(n);if(!e.isFile())return o.NextResponse.json({success:!1,message:"File not found"},{status:404});let t=await (0,l.readFile)(n),s=a.split(".").pop()?.toLowerCase(),r="application/octet-stream";switch(s){case"jpg":case"jpeg":r="image/jpeg";break;case"png":r="image/png";break;case"gif":r="image/gif";break;case"webp":r="image/webp";break;case"svg":r="image/svg+xml"}let i=new Headers;return i.set("Content-Type",r),i.set("Cache-Control","public, max-age=31536000, immutable"),i.set("Content-Length",e.size.toString()),new o.NextResponse(t,{status:200,headers:i})}catch(e){return o.NextResponse.json({success:!1,message:"File not found"},{status:404})}}catch(e){return console.error("File serving error:",e),o.NextResponse.json({success:!1,message:"Internal server error"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/uploads/[...path]/route",pathname:"/api/uploads/[...path]",filename:"route",bundlePath:"app/api/uploads/[...path]/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/uploads/[...path]/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:E}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[4243,580],()=>s(89606));module.exports=r})();