"use strict";(()=>{var e={};e.id=6792,e.ids=[6792],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12269:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{e.exports=require("assert")},12909:(e,t,r)=>{r.d(t,{N:()=>d});var i=r(36344),a=r(65752),s=r(13581),o=r(75745),n=r(17063);let d={...!1,providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,s.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let t=await n.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[i,a]=r.split(":");if(i!==e.token||a!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,o.A)();try{let i=await n.A.findOne({email:e.email});return i?i.lastLoginAt=new Date:i=new n.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await i.save(),t&&"email-code"!==t.provider&&(i.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await i.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{r.d(t,{A:()=>n});var i=r(56037),a=r.n(i);let s=new i.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new i.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[s],submittedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:i.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:i.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let n=a().models.User||a().model("User",o)},19854:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var i={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s.default}});var a=r(12269);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var s=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=o(t);if(r&&r.has(e))return r.get(e);var i={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&({}).hasOwnProperty.call(e,s)){var n=a?Object.getOwnPropertyDescriptor(e,s):null;n&&(n.get||n.set)?Object.defineProperty(i,s,n):i[s]=e[s]}return i.default=e,r&&r.set(e,i),i}(r(35426));function o(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(o=function(e){return e?r:t})(e)}Object.keys(s).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(i,e))&&(e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))})},24443:(e,t,r)=>{r.d(t,{A:()=>o});var i=r(56037),a=r.n(i);let s=new i.Schema({toolId:{type:i.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},userId:{type:i.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},content:{type:String,required:[!0,"Comment content is required"],trim:!0,maxlength:[1e3,"Comment cannot exceed 1000 characters"],minlength:[1,"Comment cannot be empty"]},parentId:{type:i.Schema.Types.ObjectId,ref:"Comment",default:null},likes:{type:Number,default:0,min:0},isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({toolId:1,createdAt:-1}),s.index({userId:1}),s.index({parentId:1}),s.index({isActive:1}),s.virtual("replyCount",{ref:"Comment",localField:"_id",foreignField:"parentId",count:!0,match:{isActive:!0}}),s.statics.getToolComments=function(e){return this.find({toolId:e,isActive:!0}).populate("userId","name avatar").populate({path:"parentId",select:"content userId",populate:{path:"userId",select:"name"}}).sort({createdAt:-1})},s.methods.getReplies=function(){return a().model("Comment").find({parentId:this._id,isActive:!0}).populate("userId","name avatar").sort({createdAt:1})};let o=a().models.Comment||a().model("Comment",s)},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},80972:(e,t,r)=>{r.d(t,{Q$:()=>s,y3:()=>o});let i={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",website_exists:"该网站已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},a={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",website_exists:"Website already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function s(e,t){let r=t.split("."),s="zh"===e?i:a;for(let t of r)if(!s||"object"!=typeof s||!(t in s))return"zh"===e?"操作失败":"Operation failed";else s=s[t];return"string"==typeof s?s:"zh"===e?"操作失败":"Operation failed"}function o(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let r=e.headers.get("accept-language")||"",i=new URL(e.url).pathname;return i.startsWith("/en/")?"en":i.startsWith("/zh/")?"zh":r.includes("en")?"en":"zh"}},81630:e=>{e.exports=require("http")},82652:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>h,serverHooks:()=>b,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>v});var i={};r.r(i),r.d(i,{GET:()=>_,POST:()=>y});var a=r(96559),s=r(48088),o=r(37719),n=r(32190),d=r(19854),l=r(12909),u=r(75745),c=r(24443),p=r(17063),m=r(30762),f=r(80972);async function _(e,{params:t}){try{await (0,u.A)();let{id:r}=await t,{searchParams:i}=new URL(e.url),a=parseInt(i.get("page")||"1"),s=parseInt(i.get("limit")||"10"),o=(a-1)*s,d=await c.A.find({toolId:r,isActive:!0,parentId:null}).populate("userId","name email image").sort({createdAt:-1}).skip(o).limit(s),l=await Promise.all(d.map(async e=>{let t=await c.A.find({parentId:e._id,isActive:!0}).populate("userId","name email image").sort({createdAt:1});return{...e.toObject(),replies:t}})),p=await c.A.countDocuments({toolId:r,isActive:!0,parentId:{$exists:!1}});return n.NextResponse.json({success:!0,data:{comments:l,pagination:{page:a,limit:s,total:p,pages:Math.ceil(p/s)}}})}catch(r){console.error("Get comments error:",r);let t=(0,f.y3)(e);return n.NextResponse.json({success:!1,message:(0,f.Q$)(t,"errors.internal_error")},{status:500})}}async function y(e,{params:t}){try{let r=await (0,d.getServerSession)(l.N),i=(0,f.y3)(e);if(!r?.user?.email)return n.NextResponse.json({success:!1,message:(0,f.Q$)(i,"user.unauthorized")},{status:401});await (0,u.A)();let a=await p.A.findOne({email:r.user.email});if(!a)return n.NextResponse.json({success:!1,message:(0,f.Q$)(i,"user.not_found")},{status:404});let{id:s}=await t,{content:o,parentId:_}=await e.json();if(!o||0===o.trim().length||o.length>1e3)return n.NextResponse.json({success:!1,message:(0,f.Q$)(i,"errors.invalid_request")},{status:400});if(!await m.A.findById(s))return n.NextResponse.json({success:!1,message:(0,f.Q$)(i,"tools.not_found")},{status:404});if(_){let e=await c.A.findById(_);if(!e||e.toolId?.toString()!==s)return n.NextResponse.json({success:!1,message:(0,f.Q$)(i,"errors.not_found")},{status:404})}let y=new c.A({toolId:s,userId:a._id,content:o.trim(),parentId:_||void 0});return await y.save(),await y.populate("userId","name email image"),a.comments.includes(y._id.toString())||(a.comments.push(y._id.toString()),await a.save()),n.NextResponse.json({success:!0,data:y},{status:201})}catch(r){console.error("Create comment error:",r);let t=(0,f.y3)(e);return n.NextResponse.json({success:!1,message:(0,f.Q$)(t,"errors.internal_error")},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/tools/[id]/comments/route",pathname:"/api/tools/[id]/comments",filename:"route",bundlePath:"app/api/tools/[id]/comments/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:g,workUnitAsyncStorage:v,serverHooks:b}=h;function w(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:v})}},94735:e=>{e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4243,4999,9658,580,3136,2224],()=>r(82652));module.exports=i})();