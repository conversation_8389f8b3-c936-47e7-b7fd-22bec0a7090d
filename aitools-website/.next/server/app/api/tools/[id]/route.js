"use strict";(()=>{var e={};e.id=7227,e.ids=[7227],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},12909:(e,t,r)=>{r.d(t,{N:()=>d});var s=r(36344),i=r(65752),a=r(13581),o=r(75745),n=r(17063);let d={...!1,providers:[(0,s.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,i.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,a.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let t=await n.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[s,i]=r.split(":");if(s!==e.token||i!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,o.A)();try{let s=await n.A.findOne({email:e.email});return s?s.lastLoginAt=new Date:s=new n.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await s.save(),t&&"email-code"!==t.provider&&(s.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await s.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{r.d(t,{A:()=>n});var s=r(56037),i=r.n(s);let a=new s.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new s.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[a],submittedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:s.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let n=i().models.User||i().model("User",o)},18508:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>w,serverHooks:()=>q,workAsyncStorage:()=>b,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{DELETE:()=>g,GET:()=>v,PUT:()=>h});var i=r(96559),a=r(48088),o=r(37719),n=r(32190),d=r(35426),l=r(75745),u=r(30762),c=r(17063),p=r(12909),f=r(56037),_=r.n(f),m=r(80972),y=r(29703);async function v(e,{params:t}){try{let r;await (0,l.A)();let{id:s}=await t,i=(0,m.y3)(e);if(!_().Types.ObjectId.isValid(s))return n.NextResponse.json({success:!1,message:(0,m.Q$)(i,"errors.invalid_request")},{status:400});let a=await u.A.findById(s);if(!a)return n.NextResponse.json({success:!1,message:(0,m.Q$)(i,"tools.not_found")},{status:404});return"approved"===a.status&&a.launchDate&&new Date(a.launchDate)<=new Date&&a.isActive&&await u.A.findByIdAndUpdate(s,{$inc:{views:1}}),r="draft"===a.status?a.toObject():{...a.toObject(),submittedBy:void 0,reviewNotes:void 0,reviewedBy:void 0},n.NextResponse.json({success:!0,data:r})}catch(r){console.error("Error fetching tool:",r);let t=(0,m.y3)(e);return n.NextResponse.json({success:!1,message:(0,m.Q$)(t,"tools.fetch_failed")},{status:500})}}async function h(e,{params:t}){try{let r=await (0,d.getServerSession)(p.N),s=(0,m.y3)(e);if(!r?.user?.email)return n.NextResponse.json({success:!1,error:(0,m.Q$)(s,"user.unauthorized")},{status:401});await (0,l.A)();let{id:i}=await t,a=await e.json();if(!_().Types.ObjectId.isValid(i))return n.NextResponse.json({success:!1,error:(0,m.Q$)(s,"errors.invalid_request")},{status:400});let o=await c.A.findOne({email:r.user.email});if(!o)return n.NextResponse.json({success:!1,error:(0,m.Q$)(s,"user.not_found")},{status:404});let f=await u.A.findById(i);if(!f)return n.NextResponse.json({success:!1,error:(0,m.Q$)(s,"tools.not_found")},{status:404});if(f.submittedBy!==o._id.toString())return n.NextResponse.json({success:!1,error:(0,m.Q$)(s,"errors.forbidden")},{status:403});if(!["draft","pending","rejected","approved","published"].includes(f.status))return n.NextResponse.json({success:!1,error:(0,m.Q$)(s,"tools.edit_not_allowed")},{status:400});let v=[];["draft","pending","rejected"].includes(f.status)?v=["name","tagline","description","website","logo","category","pricing","tags"]:"approved"===f.status&&(v=f.launchDate&&new Date(f.launchDate)<=new Date?["name","tagline","description"]:["name","tagline","description","logo"]);let h={};for(let e of v)void 0!==a[e]&&(h[e]=a[e]);let g={name:"TOOL_NAME",tagline:"TAGLINE",description:"DESCRIPTION"};for(let[e,t]of Object.entries(h))if(g[e]&&"string"==typeof t){let r=(0,y.P5)(g[e],t);if(!r.isValid)return n.NextResponse.json({success:!1,error:r.error},{status:400})}if(h.name&&h.name!==f.name&&await u.A.findOne({name:h.name,_id:{$ne:i}}))return n.NextResponse.json({success:!1,error:(0,m.Q$)(s,"tools.name_exists")},{status:400});"rejected"===f.status&&(h.status="draft",h.isActive=!0,h.reviewedAt=void 0,h.reviewedBy=void 0,h.reviewNotes=void 0);let w={$set:h};"rejected"===f.status&&(w.$unset={reviewedAt:1,reviewedBy:1,reviewNotes:1});let b=await u.A.findByIdAndUpdate(i,w,{new:!0,runValidators:!0}).select("-submittedBy -reviewNotes -reviewedBy");return n.NextResponse.json({success:!0,data:b,message:(0,m.Q$)(s,"tools.update_success")})}catch(r){console.error("Error updating tool:",r);let t=(0,m.y3)(e);if(r?.name==="ValidationError"){let e=Object.values(r.errors).map(e=>e.message);return n.NextResponse.json({success:!1,error:(0,m.Q$)(t,"errors.validation_failed"),details:e},{status:400})}return n.NextResponse.json({success:!1,error:(0,m.Q$)(t,"tools.update_failed")},{status:500})}}async function g(e,{params:t}){try{await (0,l.A)();let r=(0,m.y3)(e),{id:s}=await t;if(!_().Types.ObjectId.isValid(s))return n.NextResponse.json({success:!1,error:(0,m.Q$)(r,"errors.invalid_request")},{status:400});if(!await u.A.findByIdAndDelete(s))return n.NextResponse.json({success:!1,error:(0,m.Q$)(r,"tools.not_found")},{status:404});return n.NextResponse.json({success:!0,message:(0,m.Q$)(r,"tools.delete_success")})}catch(r){console.error("Error deleting tool:",r);let t=(0,m.y3)(e);return n.NextResponse.json({success:!1,error:(0,m.Q$)(t,"tools.delete_failed")},{status:500})}}let w=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/tools/[id]/route",pathname:"/api/tools/[id]",filename:"route",bundlePath:"app/api/tools/[id]/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:b,workUnitAsyncStorage:x,serverHooks:q}=w;function j(){return(0,o.patchFetch)({workAsyncStorage:b,workUnitAsyncStorage:x})}},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},80972:(e,t,r)=>{r.d(t,{Q$:()=>a,y3:()=>o});let s={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",website_exists:"该网站已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},i={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",website_exists:"Website already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function a(e,t){let r=t.split("."),a="zh"===e?s:i;for(let t of r)if(!a||"object"!=typeof a||!(t in a))return"zh"===e?"操作失败":"Operation failed";else a=a[t];return"string"==typeof a?a:"zh"===e?"操作失败":"Operation failed"}function o(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let r=e.headers.get("accept-language")||"",s=new URL(e.url).pathname;return s.startsWith("/en/")?"en":s.startsWith("/zh/")?"zh":r.includes("en")?"en":"zh"}},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,580,3136,2224],()=>r(18508));module.exports=s})();