(()=>{var e={};e.id=8644,e.ids=[8644],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var s={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function a(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],a=t[0];return r.e(t[1]).then(()=>r.t(a,19))}a.keys=()=>Object.keys(s),a.id=3845,e.exports=a},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>s,q:()=>a});let s=["en","zh"],a="en"},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var s=r(36344),a=r(65752),i=r(13581),n=r(75745),o=r(17063);let l={...!1,providers:[(0,s.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,i.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,n.A)();let t=await o.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let r=t.emailVerificationToken;if(!r||!r.includes(":"))return null;let[s,a]=r.split(":");if(s!==e.token||a!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:r}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,n.A)();try{let s=await o.A.findOne({email:e.email});return s?s.lastLoginAt=new Date:s=new o.A({email:e.email,name:e.name||r?.name||"User",avatar:e.image||r?.image,emailVerified:!0,lastLoginAt:new Date}),await s.save(),t&&"email-code"!==t.provider&&(s.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await s.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),a=r.n(s);let i=new s.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),n=new s.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[i],submittedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:s.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:s.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({email:1}),n.index({role:1}),n.index({emailVerificationToken:1}),n.index({"accounts.provider":1,"accounts.providerAccountId":1}),n.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},n.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(r=>r.provider!==e||r.providerAccountId!==t)};let o=a().models.User||a().model("User",n)},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var s=r(35471),a=r(12688);let i=(0,s.A)(async({locale:e})=>(e&&a.IB.find(t=>t.toString()===e?.toString())||(e=a.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29703:(e,t,r)=>{"use strict";r.d(t,{P5:()=>o,Tr:()=>i,Y5:()=>s,kx:()=>l,w8:()=>n});let s={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},a={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function i(e){let t=a.BASE_DIR,r=a.DIRECTORIES[e];return`${t}/${r}`}function n(e,t){let r=a.DIRECTORIES[e];return`/api/uploads/${r}/${t}`}function o(e,t){let r=s[e];if(!r)return{isValid:!0};let a=t.trim().length;return a<r.min?{isValid:!1,error:`${r.label} must be at least ${r.min} characters long`}:a>r.max?{isValid:!1,error:`${r.label} cannot exceed ${r.max} characters`}:{isValid:!0}}function l(e,t){var r;let s=function(e,t){let r=a.FILE_SIZE_LIMITS[e];if(t>r){let e=Math.round(r/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!s.isValid)return s;let i=a.ALLOWED_TYPES.IMAGES,n=(r=e.type,i.includes(r)?{isValid:!0}:{isValid:!1,error:`File type ${r} is not allowed. Allowed types: ${i.join(", ")}`});return n.isValid?{isValid:!0}:n}},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(56037),a=r.n(s),i=r(60366),n=r(29703);let o=new s.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,minlength:[n.Y5.TOOL_NAME.min,`Tool name must be at least ${n.Y5.TOOL_NAME.min} characters`],maxlength:[n.Y5.TOOL_NAME.max,`Tool name cannot exceed ${n.Y5.TOOL_NAME.max} characters`]},tagline:{type:String,trim:!0,maxlength:[n.Y5.TAGLINE.max,`Tagline cannot exceed ${n.Y5.TAGLINE.max} characters`]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,minlength:[n.Y5.DESCRIPTION.min,`Description must be at least ${n.Y5.DESCRIPTION.min} characters`],maxlength:[n.Y5.DESCRIPTION.max,`Description cannot exceed ${n.Y5.DESCRIPTION.max} characters`]},longDescription:{type:String,trim:!0,maxlength:[n.Y5.LONG_DESCRIPTION.max,`Long description cannot exceed ${n.Y5.LONG_DESCRIPTION.max} characters`]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,maxlength:[n.Y5.WEBSITE_URL.max,`Website URL cannot exceed ${n.Y5.WEBSITE_URL.max} characters`],validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:i.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[n.Y5.PRICING_DETAILS.max,`Pricing details cannot exceed ${n.Y5.PRICING_DETAILS.max} characters`]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({status:1,isActive:1}),o.index({category:1,status:1}),o.index({tags:1,status:1}),o.index({submittedBy:1}),o.index({launchDate:-1}),o.index({views:-1}),o.index({likes:-1}),o.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=a().models.Tool||a().model("Tool",o)},31098:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(56037),a=r.n(s);let i=new s.Schema({userId:{type:s.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:s.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});i.index({userId:1,createdAt:-1}),i.index({toolId:1}),i.index({status:1}),i.index({paymentIntentId:1}),i.index({paymentSessionId:1}),i.index({stripePaymentIntentId:1}),i.index({stripeCustomerId:1}),i.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),i.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),i.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},i.methods.markAsFailed=function(){return this.status="failed",this.save()},i.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},i.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let n=a().models.Order||a().model("Order",i)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(78521),a=r(60687);function i({locale:e,...t}){if(!e)throw Error(void 0);return(0,a.jsx)(s.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>n,PZ:()=>o,RI:()=>d,ut:()=>l});var s=r(64348);let a=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function i(e){let t=await (0,s.A)({locale:e||"en",namespace:"categories"});return a.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function n(e){return(await i(e)).map(e=>({value:e.slug,label:e.name}))}async function o(e,t){return(await i(t)).find(t=>t.slug===e)}let l=a.map(e=>e.slug),d=a.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(61120),a=r(92440),i=r(84604),n=(0,s.cache)(function(e,t){return function({_cache:e=(0,i.d)(),_formatters:t=(0,i.b)(e),getMessageFallback:r=i.f,messages:s,namespace:a,onError:n=i.g,...o}){return function({messages:e,namespace:t,...r},s){return e=e["!"],t=(0,i.r)(t,"!"),(0,i.e)({...r,messages:e,namespace:t})}({...o,onError:n,cache:e,formatters:t,getMessageFallback:r,messages:{"!":s},namespace:a?`!.${a}`:"!"},"!")}({...e,namespace:t})}),o=(0,s.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),n(await (0,a.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},74075:e=>{"use strict";e.exports=require("zlib")},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(56037),a=r.n(s);let i=process.env.MONGODB_URI;if(!i)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let n=global.mongoose;n||(n=global.mongoose={conn:null,promise:null});let o=async function(){if(n.conn)return n.conn;n.promise||(n.promise=a().connect(i,{bufferCommands:!1}));try{n.conn=await n.promise}catch(e){throw n.promise=null,e}return n.conn}},79171:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Ef:()=>i,S9:()=>l,kX:()=>s,mV:()=>n,mp:()=>d});let s={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}};s.FREE_LAUNCH.description,s.FREE_LAUNCH.displayPrice,s.FREE_LAUNCH.features,s.PRIORITY_LAUNCH.description,s.PRIORITY_LAUNCH.displayPrice,s.PRIORITY_LAUNCH.features;let a={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}};a.FREE.value,a.FREE.label,a.FREEMIUM.value,a.FREEMIUM.label,a.PAID.value,a.PAID.label,a.FREE.value,a.FREE.label,a.FREEMIUM.value,a.FREEMIUM.label,a.PAID.value,a.PAID.label;let i=e=>{switch(e){case a.FREE.value:return a.FREE.color;case a.FREEMIUM.value:return a.FREEMIUM.color;case a.PAID.value:return a.PAID.color;default:return"bg-gray-100 text-gray-800"}},n=e=>{switch(e){case a.FREE.value:return a.FREE.label;case a.FREEMIUM.value:return a.FREEMIUM.label;case a.PAID.value:return a.PAID.label;default:return e}},o=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,l=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,d=()=>{let e=s.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80972:(e,t,r)=>{"use strict";r.d(t,{Q$:()=>i,y3:()=>n});let s={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",website_exists:"该网站已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},a={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",website_exists:"Website already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function i(e,t){let r=t.split("."),i="zh"===e?s:a;for(let t of r)if(!i||"object"!=typeof i||!(t in i))return"zh"===e?"操作失败":"Operation failed";else i=i[t];return"string"==typeof i?i:"zh"===e?"操作失败":"Operation failed"}function n(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let r=e.headers.get("accept-language")||"",s=new URL(e.url).pathname;return s.startsWith("/en/")?"en":s.startsWith("/zh/")?"zh":r.includes("en")?"en":"zh"}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")},81630:e=>{"use strict";e.exports=require("http")},89765:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>x,serverHooks:()=>A,workAsyncStorage:()=>S,workUnitAsyncStorage:()=>b});var s={};r.r(s),r.d(s,{GET:()=>I,PATCH:()=>v,POST:()=>h});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),l=r(35426),d=r(75745),u=r(30762),c=r(31098),p=r(17063),m=r(12909),f=r(79171),g=r(56037),_=r.n(g),y=r(80972);async function h(e,{params:t}){try{let r=await (0,l.getServerSession)(m.N),s=(0,y.y3)(e);if(!r?.user?.email)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"user.unauthorized")},{status:401});await (0,d.A)();let{id:a}=await t,{launchOption:i,selectedDate:n}=await e.json();if(!_().Types.ObjectId.isValid(a)||!i||!n||!["free","paid"].includes(i))return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"errors.invalid_request")},{status:400});let g=await p.A.findOne({email:r.user.email});if(!g)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"user.not_found")},{status:404});let h=await u.A.findById(a);if(!h)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.not_found")},{status:404});if(h.submittedBy!==g._id.toString())return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"errors.forbidden")},{status:403});if("approved"===h.status&&h.selectedLaunchDate<=new Date)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.launch_date_already_set")},{status:400});let I=new Date(n);if("free"===i){let e=new Date;e.setMonth(e.getMonth()+1),e.setHours(0,0,0,0);let t=new Date(I);if(t.setHours(0,0,0,0),t.getTime()<e.getTime())return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.free_date_restriction")},{status:400})}else{let e=new Date;if(e.setDate(e.getDate()+1),e.setHours(0,0,0,0),I<e)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.paid_date_restriction")},{status:400})}if("free"===i||"completed"===h.paymentStatus)return await u.A.findByIdAndUpdate(a,{$set:{launchDateSelected:!0,selectedLaunchDate:I,launchDate:I,launchOption:"completed"===h.paymentStatus?"paid":"free",paymentRequired:!1,status:"pending"}}),o.NextResponse.json({success:!0,data:{message:(0,y.Q$)(s,"tools.launch_date_set_success")}});{let e=f.kX.PRIORITY_LAUNCH.stripeAmount,t=new c.A({userId:g._id,toolId:a,type:"launch_date_priority",amount:e,currency:f.kX.PRIORITY_LAUNCH.currency,status:"pending",description:`工具 "${h.name}" 优先发布服务`,selectedLaunchDate:I});await t.save();let r={launchDateSelected:!0,selectedLaunchDate:I,launchDate:I,launchOption:"paid",paymentRequired:!0,paymentAmount:e,paymentStatus:"pending",orderId:t._id.toString()};r.status="draft",await u.A.findByIdAndUpdate(a,{$set:r});let i=`/${s}/payment/checkout?orderId=${t._id}`;return o.NextResponse.json({success:!0,data:{orderId:t._id,paymentUrl:i,amount:e,message:"pending"===h.status?(0,y.Q$)(s,"payment.upgrade_order_created"):(0,y.Q$)(s,"payment.order_created")}})}}catch(r){console.error("Launch date selection error:",r);let t=(0,y.y3)(e);return o.NextResponse.json({success:!1,message:(0,y.Q$)(t,"errors.internal_error")},{status:500})}}async function I(e,{params:t}){try{let r=await (0,l.getServerSession)(m.N),s=(0,y.y3)(e);if(!r?.user?.email)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"user.unauthorized")},{status:401});await (0,d.A)();let{id:a}=await t;if(!_().Types.ObjectId.isValid(a))return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"errors.invalid_request")},{status:400});let i=await p.A.findOne({email:r.user.email});if(!i)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"user.not_found")},{status:404});let n=await u.A.findById(a);if(!n)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.not_found")},{status:404});if(n.submittedBy!==i._id.toString())return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"errors.forbidden")},{status:403});let f=null;return n.orderId&&(f=await c.A.findById(n.orderId)),o.NextResponse.json({success:!0,data:{tool:{id:n._id,name:n.name,status:n.status,launchDateSelected:n.launchDateSelected,selectedLaunchDate:n.selectedLaunchDate,launchOption:n.launchOption,paymentRequired:n.paymentRequired,paymentStatus:n.paymentStatus},order:f}})}catch(r){console.error("Get launch date info error:",r);let t=(0,y.y3)(e);return o.NextResponse.json({success:!1,message:(0,y.Q$)(t,"errors.internal_error")},{status:500})}}async function v(e,{params:t}){try{let r=await (0,l.getServerSession)(m.N),s=(0,y.y3)(e);if(!r?.user?.email)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"user.unauthorized")},{status:401});await (0,d.A)();let{id:a}=await t,{selectedDate:i}=await e.json();if(!_().Types.ObjectId.isValid(a)||!i)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"errors.invalid_request")},{status:400});let n=await p.A.findOne({email:r.user.email});if(!n)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"user.not_found")},{status:404});let f=await u.A.findById(a);if(!f)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.not_found")},{status:404});if(f.submittedBy!==n._id.toString())return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"errors.forbidden")},{status:403});if(!["pending","approved"].includes(f.status))return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.edit_not_allowed")},{status:400});let g=new Date;if("approved"===f.status&&f.launchDate&&new Date(f.launchDate)<=g)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.already_published")},{status:400});let h=new Date(i);if("paid"===f.launchOption){let e=new Date;if(e.setDate(e.getDate()+1),e.setHours(0,0,0,0),h<e)return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.paid_date_restriction")},{status:400})}else{let e=new Date;e.setMonth(e.getMonth()+1),e.setHours(0,0,0,0);let t=new Date(h);if(t.setHours(0,0,0,0),t.getTime()<e.getTime())return o.NextResponse.json({success:!1,message:(0,y.Q$)(s,"tools.free_date_restriction")},{status:400})}return await u.A.findByIdAndUpdate(a,{$set:{selectedLaunchDate:h,launchDate:h}}),f.orderId&&await c.A.findByIdAndUpdate(f.orderId,{$set:{selectedLaunchDate:h}}),o.NextResponse.json({success:!0,data:{message:(0,y.Q$)(s,"tools.launch_date_updated"),selectedLaunchDate:h}})}catch(r){console.error("Update launch date error:",r);let t=(0,y.y3)(e);return o.NextResponse.json({success:!1,message:(0,y.Q$)(t,"errors.internal_error")},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tools/[id]/launch-date/route",pathname:"/api/tools/[id]/launch-date",filename:"route",bundlePath:"app/api/tools/[id]/launch-date/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:S,workUnitAsyncStorage:b,serverHooks:A}=x;function E(){return(0,n.patchFetch)({workAsyncStorage:S,workUnitAsyncStorage:b})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,4999,9658,580,3136],()=>r(89765));module.exports=s})();