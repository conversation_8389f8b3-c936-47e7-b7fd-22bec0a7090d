"use strict";(()=>{var e={};e.id=3839,e.ids=[3839],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},80972:(e,t,s)=>{s.d(t,{Q$:()=>i,y3:()=>o});let a={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",website_exists:"该网站已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},r={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",website_exists:"Website already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function i(e,t){let s=t.split("."),i="zh"===e?a:r;for(let t of s)if(!i||"object"!=typeof i||!(t in i))return"zh"===e?"操作失败":"Operation failed";else i=i[t];return"string"==typeof i?i:"zh"===e?"操作失败":"Operation failed"}function o(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let s=e.headers.get("accept-language")||"",a=new URL(e.url).pathname;return a.startsWith("/en/")?"en":a.startsWith("/zh/")?"zh":s.includes("en")?"en":"zh"}},96855:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>h,routeModule:()=>_,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>m});var a={};s.r(a),s.d(a,{GET:()=>c,POST:()=>p});var r=s(96559),i=s(48088),o=s(37719),d=s(32190),l=s(75745),u=s(30762),n=s(80972);async function c(e){try{await (0,l.A)();let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"12"),r=t.get("category"),i=t.get("status"),o=t.get("search"),n=t.get("sort")||"createdAt",c=t.get("order")||"desc",p=t.get("dateFrom"),_=t.get("dateTo"),f={};r&&"all"!==r&&(f.category=r),i&&"all"!==i?"published"===i?(f.status="approved",f.selectedLaunchDate={$lte:new Date}):f.status=i:(f.status="approved",f.selectedLaunchDate={$lte:new Date}),o&&(f.$or=[{name:{$regex:o,$options:"i"}},{description:{$regex:o,$options:"i"}},{tags:{$in:[RegExp(o,"i")]}}]),(p||_)&&(f.selectedLaunchDate=f.launchDate||{},p&&(f.selectedLaunchDate.$gte=new Date(p)),_&&(f.selectedLaunchDate.$lte=new Date(_)));let m=(s-1)*a,g={};g[n]="desc"===c?-1:1,console.log("query.................",f,g,m,a);let h=await u.A.find(f).sort(g).skip(m).limit(a).select("-submittedBy -reviewNotes -reviewedBy").lean(),y=await u.A.countDocuments(f),b=Math.ceil(y/a);return d.NextResponse.json({success:!0,data:{tools:h,pagination:{currentPage:s,totalPages:b,totalItems:y,itemsPerPage:a,hasNextPage:s<b,hasPrevPage:s>1}}})}catch(s){console.error("Error fetching tools:",s);let t=(0,n.y3)(e);return d.NextResponse.json({success:!1,error:(0,n.Q$)(t,"tools.fetch_failed")},{status:500})}}async function p(e){try{await (0,l.A)();let t=await e.json(),s=(0,n.y3)(e);for(let e of["name","description","website","category","pricing","submitterName","submitterEmail"])if(!t[e])return d.NextResponse.json({success:!1,error:(0,n.Q$)(s,`tools.${e}_required`)},{status:400});if(await u.A.findOne({website:t.website}))return d.NextResponse.json({success:!1,error:(0,n.Q$)(s,"tools.website_exists")},{status:400});let a={name:t.name,tagline:t.tagline,description:t.description,website:t.website,logo:t.logo,category:t.category,pricing:t.pricing,tags:t.tags||[],submittedBy:t.submitterName,submittedAt:new Date,selectedLaunchDate:t.publishDate?new Date(t.publishDate):void 0,status:"draft",views:0,likes:0,isActive:!0},r=new u.A(a);return await r.save(),d.NextResponse.json({success:!0,data:r,message:(0,n.Q$)(s,"tools.submit_success")},{status:201})}catch(s){console.error("Error creating tool:",s);let t=(0,n.y3)(e);if(s?.name==="ValidationError"){let e=Object.values(s.errors).map(e=>e.message);return d.NextResponse.json({success:!1,error:(0,n.Q$)(t,"errors.validation_failed"),details:e},{status:400})}return d.NextResponse.json({success:!1,error:(0,n.Q$)(t,"tools.create_failed")},{status:500})}}let _=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/tools/route",pathname:"/api/tools",filename:"route",bundlePath:"app/api/tools/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:f,workUnitAsyncStorage:m,serverHooks:g}=_;function h(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:m})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,580,2224],()=>s(96855));module.exports=a})();