"use strict";(()=>{var e={};e.id=3287,e.ids=[3287,6706],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9865:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>f,serverHooks:()=>v,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>E});var a={};r.r(a),r.d(a,{POST:()=>m});var n=r(96559),o=r(48088),i=r(37719),s=r(32190),d=r(44999),u=r(75745),c=r(31098),l=r(30762),p=r(96706);async function m(e){try{let t,r=await e.text(),a=(await (0,d.headers)()).get("stripe-signature");if(!a)return s.NextResponse.json({error:"Missing stripe-signature header"},{status:400});let n=process.env.STRIPE_WEBHOOK_SECRET;if(!n)return console.error("STRIPE_WEBHOOK_SECRET is not configured"),s.NextResponse.json({error:"Webhook secret not configured"},{status:500});try{t=(0,p.ZW)(r,a,n)}catch(e){return console.error("Webhook signature verification failed:",e),s.NextResponse.json({error:"Invalid signature"},{status:400})}switch(await (0,u.A)(),t.type){case"payment_intent.succeeded":await y(t.data.object);break;case"payment_intent.payment_failed":await I(t.data.object);break;case"payment_intent.canceled":await h(t.data.object);break;default:console.log(`Unhandled event type: ${t.type}`)}return s.NextResponse.json({received:!0})}catch(e){return console.error("Webhook processing error:",e),s.NextResponse.json({error:"Webhook processing failed"},{status:500})}}async function y(e){console.log("handlePaymentSucceeded called:...........",JSON.stringify(e,null,2));try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await c.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.markAsPaid(),r.stripePaymentIntentId=e.id,r.paymentMethod="stripe",r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created)},await r.save(),await l.A.findByIdAndUpdate(r.toolId,{$set:{paymentStatus:"completed",paidAt:new Date,status:"pending"}}),console.log(`Payment succeeded for order: ${t}`)}catch(e){console.error("Error handling payment succeeded:",e)}}async function I(e){try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await c.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.markAsFailed(),r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created),failureReason:e.last_payment_error?.message},await r.save(),console.log(`Payment failed for order: ${t}`)}catch(e){console.error("Error handling payment failed:",e)}}async function h(e){try{let t=e.metadata.orderId;if(!t)return void console.error("No orderId in payment intent metadata");let r=await c.A.findById(t);if(!r)return void console.error(`Order not found: ${t}`);await r.cancel(),r.stripePaymentDetails={paymentIntentId:e.id,amount:e.amount,currency:e.currency,status:e.status,created:new Date(1e3*e.created)},await r.save(),console.log(`Payment canceled for order: ${t}`)}catch(e){console.error("Error handling payment canceled:",e)}}let f=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/stripe/webhook/route",pathname:"/api/stripe/webhook",filename:"route",bundlePath:"app/api/stripe/webhook/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:g,workUnitAsyncStorage:E,serverHooks:v}=f;function R(){return(0,i.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:E})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31098:(e,t,r)=>{r.d(t,{A:()=>i});var a=r(56037),n=r.n(a);let o=new a.Schema({userId:{type:a.Schema.Types.ObjectId,ref:"User",required:[!0,"User ID is required"]},toolId:{type:a.Schema.Types.ObjectId,ref:"Tool",required:[!0,"Tool ID is required"]},type:{type:String,required:!0,enum:["launch_date_priority"],default:"launch_date_priority"},amount:{type:Number,required:[!0,"Amount is required"],min:[0,"Amount must be positive"]},currency:{type:String,required:!0,default:"CNY",enum:["CNY","USD"]},status:{type:String,required:!0,enum:["pending","completed","failed","cancelled","refunded"],default:"pending"},paymentMethod:{type:String,trim:!0},paymentIntentId:{type:String,trim:!0},paymentSessionId:{type:String,trim:!0},stripePaymentIntentId:{type:String,trim:!0},stripeCustomerId:{type:String,trim:!0},stripePaymentDetails:{paymentIntentId:String,amount:Number,currency:String,status:String,created:Date,failureReason:String},description:{type:String,required:[!0,"Description is required"],trim:!0,maxlength:[500,"Description cannot exceed 500 characters"]},selectedLaunchDate:{type:Date,required:[!0,"Selected launch date is required"]},paidAt:{type:Date},cancelledAt:{type:Date},refundedAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({userId:1,createdAt:-1}),o.index({toolId:1}),o.index({status:1}),o.index({paymentIntentId:1}),o.index({paymentSessionId:1}),o.index({stripePaymentIntentId:1}),o.index({stripeCustomerId:1}),o.virtual("user",{ref:"User",localField:"userId",foreignField:"_id",justOne:!0}),o.virtual("tool",{ref:"Tool",localField:"toolId",foreignField:"_id",justOne:!0}),o.methods.markAsPaid=function(){return this.status="completed",this.paidAt=new Date,this.save()},o.methods.markAsFailed=function(){return this.status="failed",this.save()},o.methods.cancel=function(){return this.status="cancelled",this.cancelledAt=new Date,this.save()},o.methods.refund=function(){return this.status="refunded",this.refundedAt=new Date,this.save()};let i=n().models.Order||n().model("Order",o)},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79171:(e,t,r)=>{r.d(t,{$g:()=>s,Ef:()=>o,S9:()=>d,kX:()=>a,mV:()=>i,mp:()=>u});let a={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}};a.FREE_LAUNCH.description,a.FREE_LAUNCH.displayPrice,a.FREE_LAUNCH.features,a.PRIORITY_LAUNCH.description,a.PRIORITY_LAUNCH.displayPrice,a.PRIORITY_LAUNCH.features;let n={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}};n.FREE.value,n.FREE.label,n.FREEMIUM.value,n.FREEMIUM.label,n.PAID.value,n.PAID.label,n.FREE.value,n.FREE.label,n.FREEMIUM.value,n.FREEMIUM.label,n.PAID.value,n.PAID.label;let o=e=>{switch(e){case n.FREE.value:return n.FREE.color;case n.FREEMIUM.value:return n.FREEMIUM.color;case n.PAID.value:return n.PAID.color;default:return"bg-gray-100 text-gray-800"}},i=e=>{switch(e){case n.FREE.value:return n.FREE.label;case n.FREEMIUM.value:return n.FREEMIUM.label;case n.PAID.value:return n.PAID.label;default:return e}},s=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,d=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=()=>{let e=a.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0}},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96706:(e,t,r)=>{r.d(t,{ZW:()=>u,bw:()=>d,f:()=>i,stripe:()=>o});var a=r(97877),n=r(79171);let o=new a.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0});async function i(e,t="cny",r={}){try{return await o.paymentIntents.create({amount:e,currency:t,metadata:r,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function s(e,t,r={}){try{return await o.customers.create({email:e,name:t,metadata:r})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function d(e,t,r={}){try{let a=await o.customers.list({email:e,limit:1});if(a.data.length>0)return a.data[0];return await s(e,t,r)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function u(e,t,r){try{return o.webhooks.constructEvent(e,t,r)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}n.kX.PRIORITY_LAUNCH.productName,n.kX.PRIORITY_LAUNCH.stripeAmount,n.kX.PRIORITY_LAUNCH.stripeCurrency,n.kX.PRIORITY_LAUNCH.description,n.kX.PRIORITY_LAUNCH.features}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,580,7877,2224],()=>r(9865));module.exports=a})();