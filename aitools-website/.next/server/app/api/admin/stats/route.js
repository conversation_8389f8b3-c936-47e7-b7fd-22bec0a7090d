"use strict";(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51639:(e,t,s)=>{s.r(t),s.d(t,{patchFetch:()=>w,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var a={};s.r(a),s.d(a,{GET:()=>c});var r=s(96559),i=s(48088),o=s(37719),n=s(32190),u=s(75745),d=s(30762);async function c(e){try{let t;await (0,u.A)();let{searchParams:s}=new URL(e.url),a=s.get("timeRange")||"7d",r=new Date;switch(a){case"1d":t=new Date(r.getTime()-864e5);break;case"7d":default:t=new Date(r.getTime()-6048e5);break;case"30d":t=new Date(r.getTime()-2592e6);break;case"90d":t=new Date(r.getTime()-7776e6)}let[i,o,c,p,m,g,l,w,$]=await Promise.all([d.A.countDocuments(),d.A.countDocuments({status:"pending"}),d.A.countDocuments({status:"approved"}),d.A.countDocuments({status:"rejected"}),d.A.aggregate([{$group:{_id:null,total:{$sum:"$views"}}}]),d.A.aggregate([{$group:{_id:null,total:{$sum:"$likes"}}}]),d.A.countDocuments({submittedAt:{$gte:t}}),d.A.countDocuments({status:"approved",reviewedAt:{$gte:t}}),d.A.countDocuments({status:"rejected",reviewedAt:{$gte:t}})]),v=await d.A.aggregate([{$match:{status:"approved"}},{$group:{_id:"$category",count:{$sum:1},totalViews:{$sum:"$views"},totalLikes:{$sum:"$likes"}}},{$sort:{count:-1}}]),A=await d.A.find({status:"approved"}).sort({views:-1}).limit(10).select("name category views likes").lean(),x=await d.A.find({$or:[{submittedAt:{$gte:t}},{reviewedAt:{$gte:t}}]}).sort({updatedAt:-1}).limit(20).select("name status submittedAt reviewedAt submittedBy reviewedBy").lean(),k=await d.A.aggregate([{$match:{submittedAt:{$gte:new Date(r.getTime()-6048e5)}}},{$group:{_id:{date:{$dateToString:{format:"%Y-%m-%d",date:"$submittedAt"}},status:"$status"},count:{$sum:1}}},{$sort:{"_id.date":1}}]),f=[];for(let e=6;e>=0;e--){let t=new Date(r.getTime()-24*e*36e5),s=t.toISOString().split("T")[0],a=t.toLocaleDateString("zh-CN",{weekday:"short"}),i=k.filter(e=>e._id.date===s),o=i.find(e=>"pending"===e._id.status)?.count||0,n=i.find(e=>"approved"===e._id.status)?.count||0,u=i.find(e=>"rejected"===e._id.status)?.count||0;f.push({date:s,day:a,submissions:o,approvals:n,rejections:u})}let h=await d.A.aggregate([{$match:{status:{$in:["approved","rejected"]},reviewedAt:{$exists:!0},submittedAt:{$exists:!0}}},{$project:{reviewTime:{$divide:[{$subtract:["$reviewedAt","$submittedAt"]},36e5]}}},{$group:{_id:null,avgReviewTime:{$avg:"$reviewTime"}}}]),b={totalTools:i,pendingTools:o,approvedTools:c,rejectedTools:p,totalViews:m[0]?.total||0,totalLikes:g[0]?.total||0,recentSubmissions:l,recentApprovals:w,recentRejections:$,avgReviewTime:h[0]?.avgReviewTime||0};return n.NextResponse.json({success:!0,data:{overview:b,categoryStats:v,topTools:A,recentActivity:x,dailyStats:f,timeRange:a}})}catch(e){return console.error("Error fetching admin stats:",e),n.NextResponse.json({success:!1,error:"获取统计数据失败"},{status:500})}}let p=new r.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:l}=p;function w(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,580,2224],()=>s(51639));module.exports=a})();