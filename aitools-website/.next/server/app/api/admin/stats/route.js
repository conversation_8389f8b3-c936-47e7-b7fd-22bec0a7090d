(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var a={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function i(e){if(!r.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],i=t[0];return r.e(t[1]).then(()=>r.t(i,19))}i.keys=()=>Object.keys(a),i.id=3845,e.exports=i},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>a,q:()=>i});let a=["en","zh"],i="en"},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(35471),i=r(12688);let n=(0,a.A)(async({locale:e})=>(e&&i.IB.find(t=>t.toString()===e?.toString())||(e=i.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29703:(e,t,r)=>{"use strict";r.d(t,{P5:()=>o,Tr:()=>n,Y5:()=>a,kx:()=>l,w8:()=>s});let a={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},i={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function n(e){let t=i.BASE_DIR,r=i.DIRECTORIES[e];return`${t}/${r}`}function s(e,t){let r=i.DIRECTORIES[e];return`/api/uploads/${r}/${t}`}function o(e,t){let r=a[e];if(!r)return{isValid:!0};let i=t.trim().length;return i<r.min?{isValid:!1,error:`${r.label} must be at least ${r.min} characters long`}:i>r.max?{isValid:!1,error:`${r.label} cannot exceed ${r.max} characters`}:{isValid:!0}}function l(e,t){var r;let a=function(e,t){let r=i.FILE_SIZE_LIMITS[e];if(t>r){let e=Math.round(r/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!a.isValid)return a;let n=i.ALLOWED_TYPES.IMAGES,s=(r=e.type,n.includes(r)?{isValid:!0}:{isValid:!1,error:`File type ${r} is not allowed. Allowed types: ${n.join(", ")}`});return s.isValid?{isValid:!0}:s}},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(56037),i=r.n(a),n=r(60366),s=r(29703);let o=new a.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,minlength:[s.Y5.TOOL_NAME.min,`Tool name must be at least ${s.Y5.TOOL_NAME.min} characters`],maxlength:[s.Y5.TOOL_NAME.max,`Tool name cannot exceed ${s.Y5.TOOL_NAME.max} characters`]},tagline:{type:String,trim:!0,maxlength:[s.Y5.TAGLINE.max,`Tagline cannot exceed ${s.Y5.TAGLINE.max} characters`]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,minlength:[s.Y5.DESCRIPTION.min,`Description must be at least ${s.Y5.DESCRIPTION.min} characters`],maxlength:[s.Y5.DESCRIPTION.max,`Description cannot exceed ${s.Y5.DESCRIPTION.max} characters`]},longDescription:{type:String,trim:!0,maxlength:[s.Y5.LONG_DESCRIPTION.max,`Long description cannot exceed ${s.Y5.LONG_DESCRIPTION.max} characters`]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,maxlength:[s.Y5.WEBSITE_URL.max,`Website URL cannot exceed ${s.Y5.WEBSITE_URL.max} characters`],validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:n.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[s.Y5.PRICING_DETAILS.max,`Pricing details cannot exceed ${s.Y5.PRICING_DETAILS.max} characters`]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({status:1,isActive:1}),o.index({category:1,status:1}),o.index({tags:1,status:1}),o.index({submittedBy:1}),o.index({launchDate:-1}),o.index({views:-1}),o.index({likes:-1}),o.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=i().models.Tool||i().model("Tool",o)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(78521),i=r(60687);function n({locale:e,...t}){if(!e)throw Error(void 0);return(0,i.jsx)(a.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},51639:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>p,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>u});var i=r(96559),n=r(48088),s=r(37719),o=r(32190),l=r(75745),c=r(30762);async function u(e){try{let t;await (0,l.A)();let{searchParams:r}=new URL(e.url),a=r.get("timeRange")||"7d",i=new Date;switch(a){case"1d":t=new Date(i.getTime()-864e5);break;case"7d":default:t=new Date(i.getTime()-6048e5);break;case"30d":t=new Date(i.getTime()-2592e6);break;case"90d":t=new Date(i.getTime()-7776e6)}let[n,s,u,d,m,g,p,x,v]=await Promise.all([c.A.countDocuments(),c.A.countDocuments({status:"pending"}),c.A.countDocuments({status:"approved"}),c.A.countDocuments({status:"rejected"}),c.A.aggregate([{$group:{_id:null,total:{$sum:"$views"}}}]),c.A.aggregate([{$group:{_id:null,total:{$sum:"$likes"}}}]),c.A.countDocuments({submittedAt:{$gte:t}}),c.A.countDocuments({status:"approved",reviewedAt:{$gte:t}}),c.A.countDocuments({status:"rejected",reviewedAt:{$gte:t}})]),A=await c.A.aggregate([{$match:{status:"approved"}},{$group:{_id:"$category",count:{$sum:1},totalViews:{$sum:"$views"},totalLikes:{$sum:"$likes"}}},{$sort:{count:-1}}]),f=await c.A.find({status:"approved"}).sort({views:-1}).limit(10).select("name category views likes").lean(),w=await c.A.find({$or:[{submittedAt:{$gte:t}},{reviewedAt:{$gte:t}}]}).sort({updatedAt:-1}).limit(20).select("name status submittedAt reviewedAt submittedBy reviewedBy").lean(),y=await c.A.aggregate([{$match:{submittedAt:{$gte:new Date(i.getTime()-6048e5)}}},{$group:{_id:{date:{$dateToString:{format:"%Y-%m-%d",date:"$submittedAt"}},status:"$status"},count:{$sum:1}}},{$sort:{"_id.date":1}}]),S=[];for(let e=6;e>=0;e--){let t=new Date(i.getTime()-24*e*36e5),r=t.toISOString().split("T")[0],a=t.toLocaleDateString("zh-CN",{weekday:"short"}),n=y.filter(e=>e._id.date===r),s=n.find(e=>"pending"===e._id.status)?.count||0,o=n.find(e=>"approved"===e._id.status)?.count||0,l=n.find(e=>"rejected"===e._id.status)?.count||0;S.push({date:r,day:a,submissions:s,approvals:o,rejections:l})}let h=await c.A.aggregate([{$match:{status:{$in:["approved","rejected"]},reviewedAt:{$exists:!0},submittedAt:{$exists:!0}}},{$project:{reviewTime:{$divide:[{$subtract:["$reviewedAt","$submittedAt"]},36e5]}}},{$group:{_id:null,avgReviewTime:{$avg:"$reviewTime"}}}]),E={totalTools:n,pendingTools:s,approvedTools:u,rejectedTools:d,totalViews:m[0]?.total||0,totalLikes:g[0]?.total||0,recentSubmissions:p,recentApprovals:x,recentRejections:v,avgReviewTime:h[0]?.avgReviewTime||0};return o.NextResponse.json({success:!0,data:{overview:E,categoryStats:A,topTools:f,recentActivity:w,dailyStats:S,timeRange:a}})}catch(e){return console.error("Error fetching admin stats:",e),o.NextResponse.json({success:!1,error:"获取统计数据失败"},{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:p}=d;function x(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>s,PZ:()=>o,RI:()=>c,ut:()=>l});var a=r(64348);let i=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function n(e){let t=await (0,a.A)({locale:e||"en",namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function s(e){return(await n(e)).map(e=>({value:e.slug,label:e.name}))}async function o(e,t){return(await n(t)).find(t=>t.slug===e)}let l=i.map(e=>e.slug),c=i.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(61120),i=r(92440),n=r(84604),s=(0,a.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:a,namespace:i,onError:s=n.g,...o}){return function({messages:e,namespace:t,...r},a){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...o,onError:s,cache:e,formatters:t,getMessageFallback:r,messages:{"!":a},namespace:i?`!.${i}`:"!"},"!")}({...e,namespace:t})}),o=(0,a.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),s(await (0,i.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(56037),i=r.n(a);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let s=global.mongoose;s||(s=global.mongoose={conn:null,promise:null});let o=async function(){if(s.conn)return s.conn;s.promise||(s.promise=i().connect(n,{bufferCommands:!1}));try{s.conn=await s.promise}catch(e){throw s.promise=null,e}return s.conn}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,580],()=>r(51639));module.exports=a})();