(()=>{var e={};e.id=9722,e.ids=[9722],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var a={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function i(e){if(!r.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],i=t[0];return r.e(t[1]).then(()=>r.t(i,19))}i.keys=()=>Object.keys(a),i.id=3845,e.exports=i},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>a,q:()=>i});let a=["en","zh"],i="en"},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(35471),i=r(12688);let s=(0,a.A)(async({locale:e})=>(e&&i.IB.find(t=>t.toString()===e?.toString())||(e=i.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29703:(e,t,r)=>{"use strict";r.d(t,{P5:()=>n,Tr:()=>s,Y5:()=>a,kx:()=>l,w8:()=>o});let a={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},i={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function s(e){let t=i.BASE_DIR,r=i.DIRECTORIES[e];return`${t}/${r}`}function o(e,t){let r=i.DIRECTORIES[e];return`/api/uploads/${r}/${t}`}function n(e,t){let r=a[e];if(!r)return{isValid:!0};let i=t.trim().length;return i<r.min?{isValid:!1,error:`${r.label} must be at least ${r.min} characters long`}:i>r.max?{isValid:!1,error:`${r.label} cannot exceed ${r.max} characters`}:{isValid:!0}}function l(e,t){var r;let a=function(e,t){let r=i.FILE_SIZE_LIMITS[e];if(t>r){let e=Math.round(r/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!a.isValid)return a;let s=i.ALLOWED_TYPES.IMAGES,o=(r=e.type,s.includes(r)?{isValid:!0}:{isValid:!1,error:`File type ${r} is not allowed. Allowed types: ${s.join(", ")}`});return o.isValid?{isValid:!0}:o}},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(56037),i=r.n(a),s=r(60366),o=r(29703);let n=new a.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,minlength:[o.Y5.TOOL_NAME.min,`Tool name must be at least ${o.Y5.TOOL_NAME.min} characters`],maxlength:[o.Y5.TOOL_NAME.max,`Tool name cannot exceed ${o.Y5.TOOL_NAME.max} characters`]},tagline:{type:String,trim:!0,maxlength:[o.Y5.TAGLINE.max,`Tagline cannot exceed ${o.Y5.TAGLINE.max} characters`]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,minlength:[o.Y5.DESCRIPTION.min,`Description must be at least ${o.Y5.DESCRIPTION.min} characters`],maxlength:[o.Y5.DESCRIPTION.max,`Description cannot exceed ${o.Y5.DESCRIPTION.max} characters`]},longDescription:{type:String,trim:!0,maxlength:[o.Y5.LONG_DESCRIPTION.max,`Long description cannot exceed ${o.Y5.LONG_DESCRIPTION.max} characters`]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,maxlength:[o.Y5.WEBSITE_URL.max,`Website URL cannot exceed ${o.Y5.WEBSITE_URL.max} characters`],validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:s.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[o.Y5.PRICING_DETAILS.max,`Pricing details cannot exceed ${o.Y5.PRICING_DETAILS.max} characters`]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});n.index({status:1,isActive:1}),n.index({category:1,status:1}),n.index({tags:1,status:1}),n.index({submittedBy:1}),n.index({launchDate:-1}),n.index({views:-1}),n.index({likes:-1}),n.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=i().models.Tool||i().model("Tool",n)},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var a=r(78521),i=r(60687);function s({locale:e,...t}){if(!e)throw Error(void 0);return(0,i.jsx)(a.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},52401:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>m,serverHooks:()=>g,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>f});var a={};r.r(a),r.d(a,{GET:()=>p});var i=r(96559),s=r(48088),o=r(37719),n=r(32190),l=r(75745),d=r(30762),c=r(60366),u=r(80972);async function p(e){try{await (0,l.A)();let e=await d.A.aggregate([{$match:{status:"approved",selectedLaunchDate:{$lte:new Date}}},{$group:{_id:"$category",count:{$sum:1},totalViews:{$sum:"$views"},totalLikes:{$sum:"$likes"}}},{$sort:{count:-1}}]),t=c.ut.map(t=>{let r=e.find(e=>e._id===t);return{id:t,name:t,count:r?.count||0,totalViews:r?.totalViews||0,totalLikes:r?.totalLikes||0}});t.sort((e,t)=>t.count-e.count);let r=(await d.A.aggregate([{$match:{status:"approved",selectedLaunchDate:{$lte:new Date}}},{$group:{_id:null,totalTools:{$sum:1},totalViews:{$sum:"$views"},totalLikes:{$sum:"$likes"}}}]))[0]||{totalTools:0,totalViews:0,totalLikes:0};return n.NextResponse.json({success:!0,data:{categories:t,overview:r}})}catch(r){console.error("Error fetching categories:",r);let t=(0,u.y3)(e);return n.NextResponse.json({success:!1,error:(0,u.Q$)(t,"errors.internal_error")},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:_,workUnitAsyncStorage:f,serverHooks:g}=m;function h(){return(0,o.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:f})}},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>o,PZ:()=>n,RI:()=>d,ut:()=>l});var a=r(64348);let i=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function s(e){let t=await (0,a.A)({locale:e||"en",namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function o(e){return(await s(e)).map(e=>({value:e.slug,label:e.name}))}async function n(e,t){return(await s(t)).find(t=>t.slug===e)}let l=i.map(e=>e.slug),d=i.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(61120),i=r(92440),s=r(84604),o=(0,a.cache)(function(e,t){return function({_cache:e=(0,s.d)(),_formatters:t=(0,s.b)(e),getMessageFallback:r=s.f,messages:a,namespace:i,onError:o=s.g,...n}){return function({messages:e,namespace:t,...r},a){return e=e["!"],t=(0,s.r)(t,"!"),(0,s.e)({...r,messages:e,namespace:t})}({...n,onError:o,cache:e,formatters:t,getMessageFallback:r,messages:{"!":a},namespace:i?`!.${i}`:"!"},"!")}({...e,namespace:t})}),n=(0,a.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),o(await (0,i.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(56037),i=r.n(a);let s=process.env.MONGODB_URI;if(!s)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let n=async function(){if(o.conn)return o.conn;o.promise||(o.promise=i().connect(s,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},80972:(e,t,r)=>{"use strict";r.d(t,{Q$:()=>s,y3:()=>o});let a={errors:{fetch_failed:"获取数据失败",network_error:"网络错误，请重试",validation_failed:"验证失败",unauthorized:"未授权访问",forbidden:"禁止访问",not_found:"资源未找到",internal_error:"服务器内部错误",invalid_request:"无效请求",missing_required_field:"缺少必需字段",duplicate_name:"名称已存在",create_failed:"创建失败",update_failed:"更新失败",delete_failed:"删除失败"},success:{created:"创建成功",updated:"更新成功",deleted:"删除成功",submitted:"提交成功",approved:"批准成功",rejected:"拒绝成功",published:"发布成功"},tools:{fetch_failed:"获取工具列表失败",create_failed:"创建工具失败",name_required:"name 是必需的",description_required:"description 是必需的",website_required:"website 是必需的",category_required:"category 是必需的",pricing_required:"pricing 是必需的",submitter_name_required:"submitterName 是必需的",submitter_email_required:"submitterEmail 是必需的",name_exists:"该工具名称已存在",website_exists:"该网站已存在",submit_success:"工具提交成功，等待审核",approve_success:"工具审核通过",reject_success:"工具已拒绝",approve_failed:"审核通过失败",reject_failed:"拒绝失败",not_found:"工具未找到",update_success:"工具更新成功",update_failed:"工具更新失败",launch_date_already_set:"此工具已经选择了发布日期",free_date_restriction:"免费选项只能选择一个月后的日期",paid_date_restriction:"付费选项最早只能选择明天的日期",launch_date_set_success:"发布日期设置成功，工具已进入审核队列",edit_not_allowed:"当前状态不允许修改发布日期",already_published:"工具已发布，无法修改发布日期",launch_date_updated:"发布日期修改成功",publish_success:"工具发布成功",publish_failed:"工具发布失败"},user:{not_found:"用户未找到",unauthorized:"用户未授权",profile_update_success:"个人资料更新成功",profile_update_failed:"个人资料更新失败"},auth:{invalid_credentials:"无效的登录凭据",code_sent:"验证码已发送",code_send_failed:"验证码发送失败",invalid_code:"无效的验证码",login_success:"登录成功",login_failed:"登录失败",logout_success:"退出成功"},payment:{create_intent_failed:"创建支付意图失败",payment_success:"支付成功",payment_failed:"支付失败",webhook_error:"Webhook 处理错误",order_created:"订单创建成功，请完成支付",upgrade_order_created:"升级订单创建成功，请完成支付"},upload:{no_file:"请选择要上传的文件",invalid_type:"只支持 JPEG、PNG、GIF、WebP 格式的图片",file_too_large:"文件大小不能超过 5MB",upload_failed:"文件上传失败",upload_success:"文件上传成功"}},i={errors:{fetch_failed:"Failed to fetch data",network_error:"Network error, please try again",validation_failed:"Validation failed",unauthorized:"Unauthorized access",forbidden:"Access forbidden",not_found:"Resource not found",internal_error:"Internal server error",invalid_request:"Invalid request",missing_required_field:"Missing required field",duplicate_name:"Name already exists",create_failed:"Creation failed",update_failed:"Update failed",delete_failed:"Deletion failed"},success:{created:"Created successfully",updated:"Updated successfully",deleted:"Deleted successfully",submitted:"Submitted successfully",approved:"Approved successfully",rejected:"Rejected successfully",published:"Published successfully"},tools:{fetch_failed:"Failed to fetch tools list",create_failed:"Failed to create tool",name_required:"name is required",description_required:"description is required",website_required:"website is required",category_required:"category is required",pricing_required:"pricing is required",submitter_name_required:"submitterName is required",submitter_email_required:"submitterEmail is required",name_exists:"Tool name already exists",website_exists:"Website already exists",submit_success:"Tool submitted successfully, awaiting review",approve_success:"Tool approved successfully",reject_success:"Tool rejected successfully",approve_failed:"Failed to approve tool",reject_failed:"Failed to reject tool",not_found:"Tool not found",update_success:"Tool updated successfully",update_failed:"Failed to update tool",launch_date_already_set:"This tool has already selected a launch date",free_date_restriction:"Free option can only select dates one month later",paid_date_restriction:"Paid option can only select dates from tomorrow",launch_date_set_success:"Launch date set successfully, tool entered review queue",edit_not_allowed:"Current status does not allow modifying launch date",already_published:"Tool already published, cannot modify launch date",launch_date_updated:"Launch date updated successfully",publish_success:"Tool published successfully",publish_failed:"Failed to publish tool"},user:{not_found:"User not found",unauthorized:"User unauthorized",profile_update_success:"Profile updated successfully",profile_update_failed:"Failed to update profile"},auth:{invalid_credentials:"Invalid credentials",code_sent:"Verification code sent",code_send_failed:"Failed to send verification code",invalid_code:"Invalid verification code",login_success:"Login successful",login_failed:"Login failed",logout_success:"Logout successful"},payment:{create_intent_failed:"Failed to create payment intent",payment_success:"Payment successful",payment_failed:"Payment failed",webhook_error:"Webhook processing error",order_created:"Order created successfully, please complete payment",upgrade_order_created:"Upgrade order created successfully, please complete payment"},upload:{no_file:"Please select a file to upload",invalid_type:"Only JPEG, PNG, GIF, WebP image formats are supported",file_too_large:"File size cannot exceed 5MB",upload_failed:"File upload failed",upload_success:"File uploaded successfully"}};function s(e,t){let r=t.split("."),s="zh"===e?a:i;for(let t of r)if(!s||"object"!=typeof s||!(t in s))return"zh"===e?"操作失败":"Operation failed";else s=s[t];return"string"==typeof s?s:"zh"===e?"操作失败":"Operation failed"}function o(e){let t=e.headers.get("x-locale");if("en"===t||"zh"===t)return t;let r=e.headers.get("accept-language")||"",a=new URL(e.url).pathname;return a.startsWith("/en/")?"en":a.startsWith("/zh/")?"zh":r.includes("en")?"en":"zh"}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,580],()=>r(52401));module.exports=a})();