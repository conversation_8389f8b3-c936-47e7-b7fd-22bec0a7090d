(()=>{var e={};e.id=5475,e.ids=[5475],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3845:(e,t,r)=>{var a={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function i(e){if(!r.o(a,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=a[e],i=t[0];return r.e(t[1]).then(()=>r.t(i,19))}i.keys=()=>Object.keys(a),i.id=3845,e.exports=i},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12688:(e,t,r)=>{"use strict";r.d(t,{IB:()=>a,q:()=>i});let a=["en","zh"],i="en"},17941:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var a=r(35471),i=r(12688);let n=(0,a.A)(async({locale:e})=>(e&&i.IB.find(t=>t.toString()===e?.toString())||(e=i.q),{locale:e,messages:(await r(3845)(`./${e}.json`)).default}))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29703:(e,t,r)=>{"use strict";r.d(t,{P5:()=>s,Tr:()=>n,Y5:()=>a,kx:()=>l,w8:()=>o});let a={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},i={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function n(e){let t=i.BASE_DIR,r=i.DIRECTORIES[e];return`${t}/${r}`}function o(e,t){let r=i.DIRECTORIES[e];return`/api/uploads/${r}/${t}`}function s(e,t){let r=a[e];if(!r)return{isValid:!0};let i=t.trim().length;return i<r.min?{isValid:!1,error:`${r.label} must be at least ${r.min} characters long`}:i>r.max?{isValid:!1,error:`${r.label} cannot exceed ${r.max} characters`}:{isValid:!0}}function l(e,t){var r;let a=function(e,t){let r=i.FILE_SIZE_LIMITS[e];if(t>r){let e=Math.round(r/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!a.isValid)return a;let n=i.ALLOWED_TYPES.IMAGES,o=(r=e.type,n.includes(r)?{isValid:!0}:{isValid:!1,error:`File type ${r} is not allowed. Allowed types: ${n.join(", ")}`});return o.isValid?{isValid:!0}:o}},30762:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(56037),i=r.n(a),n=r(60366),o=r(29703);let s=new a.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,minlength:[o.Y5.TOOL_NAME.min,`Tool name must be at least ${o.Y5.TOOL_NAME.min} characters`],maxlength:[o.Y5.TOOL_NAME.max,`Tool name cannot exceed ${o.Y5.TOOL_NAME.max} characters`]},tagline:{type:String,trim:!0,maxlength:[o.Y5.TAGLINE.max,`Tagline cannot exceed ${o.Y5.TAGLINE.max} characters`]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,minlength:[o.Y5.DESCRIPTION.min,`Description must be at least ${o.Y5.DESCRIPTION.min} characters`],maxlength:[o.Y5.DESCRIPTION.max,`Description cannot exceed ${o.Y5.DESCRIPTION.max} characters`]},longDescription:{type:String,trim:!0,maxlength:[o.Y5.LONG_DESCRIPTION.max,`Long description cannot exceed ${o.Y5.LONG_DESCRIPTION.max} characters`]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,maxlength:[o.Y5.WEBSITE_URL.max,`Website URL cannot exceed ${o.Y5.WEBSITE_URL.max} characters`],validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:n.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[o.Y5.PRICING_DETAILS.max,`Pricing details cannot exceed ${o.Y5.PRICING_DETAILS.max} characters`]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({status:1,isActive:1}),s.index({category:1,status:1}),s.index({tags:1,status:1}),s.index({submittedBy:1}),s.index({launchDate:-1}),s.index({views:-1}),s.index({likes:-1}),s.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=i().models.Tool||i().model("Tool",s)},34490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{GET:()=>u});var i=r(96559),n=r(48088),o=r(37719),s=r(32190),l=r(75745),c=r(30762),m=r(60366);async function u(){try{await (0,l.A)();let e=await c.A.find({status:"approved",selectedLaunchDate:{$lte:new Date}}).select("_id name selectedLaunchDate updatedAt").lean(),t=process.env.NEXT_PUBLIC_BASE_URL||"https://www.aitools.pub",r=new Date().toISOString(),a=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">

  <!-- 主页 -->
  <url>
    <loc>${t}</loc>
    <lastmod>${r}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- 工具目录页 -->
  <url>
    <loc>${t}/tools</loc>
    <lastmod>${r}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- 分类总览页 -->
  <url>
    <loc>${t}/categories</loc>
    <lastmod>${r}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- 各个分类页面 -->
  ${m.ut.map(e=>`
  <url>
    <loc>${t}/categories/${e}</loc>
    <lastmod>${r}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`).join("")}

  <!-- 静态页面 -->
  <url>
    <loc>${t}/about</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>

  <url>
    <loc>${t}/contact</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>

  <url>
    <loc>${t}/privacy</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>

  <url>
    <loc>${t}/terms</loc>
    <lastmod>${r}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>

  <!-- 工具详情页 -->
  ${e.map(e=>`
  <url>
    <loc>${t}/tools/${e._id}</loc>
    <lastmod>${e.updatedAt?new Date(e.updatedAt).toISOString():new Date(e.launchDate).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`).join("")}

</urlset>`;return new s.NextResponse(a,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(e){return console.error("Error generating sitemap:",e),new s.NextResponse("Internal Server Error",{status:500})}}let d=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"route",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:p,workUnitAsyncStorage:g,serverHooks:h}=d;function y(){return(0,o.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},45196:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(78521),i=r(60687);function n({locale:e,...t}){if(!e)throw Error(void 0);return(0,i.jsx)(a.Dk,{locale:e,...t})}},46930:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994))},56037:e=>{"use strict";e.exports=require("mongoose")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>o,PZ:()=>s,RI:()=>c,ut:()=>l});var a=r(64348);let i=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function n(e){let t=await (0,a.A)({locale:e||"en",namespace:"categories"});return i.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function o(e){return(await n(e)).map(e=>({value:e.slug,label:e.name}))}async function s(e,t){return(await n(t)).find(t=>t.slug===e)}let l=i.map(e=>e.slug),c=i.reduce((e,t)=>(e[t.slug]=t,e),{})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64348:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(61120),i=r(92440),n=r(84604),o=(0,a.cache)(function(e,t){return function({_cache:e=(0,n.d)(),_formatters:t=(0,n.b)(e),getMessageFallback:r=n.f,messages:a,namespace:i,onError:o=n.g,...s}){return function({messages:e,namespace:t,...r},a){return e=e["!"],t=(0,n.r)(t,"!"),(0,n.e)({...r,messages:e,namespace:t})}({...s,onError:o,cache:e,formatters:t,getMessageFallback:r,messages:{"!":a},namespace:i?`!.${i}`:"!"},"!")}({...e,namespace:t})}),s=(0,a.cache)(async function(e){let t,r;return"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace),o(await (0,i.A)(r),t)})},70490:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196))},75745:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(56037),i=r.n(a);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let s=async function(){if(o.conn)return o.conn;o.promise||(o.promise=i().connect(n,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},80994:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/node_modules/next-intl/dist/esm/production/shared/NextIntlClientProvider.js","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,4999,9658,580],()=>r(34490));module.exports=a})();