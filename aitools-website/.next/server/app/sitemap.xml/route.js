"use strict";(()=>{var e={};e.id=5475,e.ids=[5475],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34490:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var o={};t.r(o),t.d(o,{GET:()=>u});var a=t(96559),s=t(48088),l=t(37719),i=t(32190),n=t(75745),p=t(30762),c=t(60366);async function u(){try{await (0,n.A)();let e=await p.A.find({status:"approved",selectedLaunchDate:{$lte:new Date}}).select("_id name selectedLaunchDate updatedAt").lean(),r=process.env.NEXT_PUBLIC_BASE_URL||"https://www.aitools.pub",t=new Date().toISOString(),o=`<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">

  <!-- 主页 -->
  <url>
    <loc>${r}</loc>
    <lastmod>${t}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- 工具目录页 -->
  <url>
    <loc>${r}/tools</loc>
    <lastmod>${t}</lastmod>
    <changefreq>daily</changefreq>
    <priority>0.9</priority>
  </url>

  <!-- 分类总览页 -->
  <url>
    <loc>${r}/categories</loc>
    <lastmod>${t}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>

  <!-- 各个分类页面 -->
  ${c.ut.map(e=>`
  <url>
    <loc>${r}/categories/${e}</loc>
    <lastmod>${t}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`).join("")}

  <!-- 静态页面 -->
  <url>
    <loc>${r}/about</loc>
    <lastmod>${t}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>

  <url>
    <loc>${r}/contact</loc>
    <lastmod>${t}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.5</priority>
  </url>

  <url>
    <loc>${r}/privacy</loc>
    <lastmod>${t}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>

  <url>
    <loc>${r}/terms</loc>
    <lastmod>${t}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.4</priority>
  </url>

  <!-- 工具详情页 -->
  ${e.map(e=>`
  <url>
    <loc>${r}/tools/${e._id}</loc>
    <lastmod>${e.updatedAt?new Date(e.updatedAt).toISOString():new Date(e.launchDate).toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.6</priority>
  </url>`).join("")}

</urlset>`;return new i.NextResponse(o,{status:200,headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=3600, s-maxage=3600"}})}catch(e){return console.error("Error generating sitemap:",e),new i.NextResponse("Internal Server Error",{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"route",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=d;function y(){return(0,l.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56037:e=>{e.exports=require("mongoose")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[4243,4999,9658,580,2224],()=>t(34490));module.exports=o})();