{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_256bb82d._.js", "server/edge/chunks/node_modules_@formatjs_intl-localematcher_lib_f4580406._.js", "server/edge/chunks/node_modules_4ac6bddb._.js", "server/edge/chunks/[root-of-the-server]__abf7a29f._.js", "server/edge/chunks/edge-wrapper_80fb5899.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml|uploads|logo.png).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "rObpEGTIrWNDN3M2HS9TlYsp9mfi7mRTcx2zGrtDXSw=", "__NEXT_PREVIEW_MODE_ID": "edd574d6976150d9e9b4808d5c41ae1f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "14be22e0030b54751c244afd60cd7aae46af2816c3cadb41c1cd4ad8098bff9d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "6016308046c2f6d61d8d553abda6dfc41da85d121dea40dc252c86b300983ac7"}}}, "instrumentation": null, "functions": {}}