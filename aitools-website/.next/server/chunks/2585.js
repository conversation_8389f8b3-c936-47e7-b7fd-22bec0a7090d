exports.id=2585,exports.ids=[2585],exports.modules={2328:(e,t,s)=>{"use strict";s.d(t,{LikeProvider:()=>n,X:()=>c});var r=s(60687),o=s(43210),l=s(82136);let a={liked:!1,likes:0,loading:!1},i=(0,o.createContext)(null);function n({children:e}){let{data:t}=(0,l.useSession)(),[s,n]=(0,o.useState)({}),c=(0,o.useCallback)(e=>s[e]||a,[s]),d=(0,o.useCallback)((e,t,s=!1)=>{n(r=>r[e]?r:{...r,[e]:{liked:s,likes:t,loading:!1}})},[]),m=(0,o.useCallback)(async e=>{if(t)try{let t=await fetch(`/api/tools/${e}/like`);if(t.ok){let s=await t.json();s.success&&n(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[t]),x=(0,o.useCallback)(async(e,s=!1)=>{if(!t)return!1;n(t=>({...t,[e]:{...t[e]||a,loading:!0}}));try{let t=await fetch(`/api/tools/${e}/like`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s?{forceUnlike:!0}:{})});if(t.ok){let s=await t.json();if(s.success)return n(t=>({...t,[e]:{liked:s.data.liked,likes:s.data.likes,loading:!1}})),!0}return n(t=>({...t,[e]:{...t[e]||a,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),n(t=>({...t,[e]:{...t[e]||a,loading:!1}})),!1}},[t]);return(0,r.jsx)(i.Provider,{value:{toolStates:s,toggleLike:x,getToolState:c,initializeToolState:d,refreshToolState:m},children:e})}function c(){let e=(0,o.useContext)(i);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},3845:(e,t,s)=>{var r={"./en.json":[31960,1960],"./zh.json":[67601,7601]};function o(e){if(!s.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],o=t[0];return s.e(t[1]).then(()=>s.t(o,19))}o.keys=()=>Object.keys(r),o.id=3845,e.exports=o},10456:()=>{},12340:(e,t,s)=>{"use strict";s.d(t,{N_:()=>a,a8:()=>n,rd:()=>c});var r=s(85484),o=s(48022);let l=(0,r.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:a,redirect:i,usePathname:n,useRouter:c}=(0,o.A)(l)},12623:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx","default")},12688:(e,t,s)=>{"use strict";s.d(t,{IB:()=>r,q:()=>o});let r=["en","zh"],o="en"},17941:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(35471),o=s(12688);let l=(0,r.A)(async({locale:e})=>(e&&o.IB.find(t=>t.toString()===e?.toString())||(e=o.q),{locale:e,messages:(await s(3845)(`./${e}.json`)).default}))},22017:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687),o=s(43210),l=s(12340),a=s(23877),i=s(51188);let n=({children:e,href:t,locale:s})=>(0,r.jsx)(l.N_,{href:t,locale:s,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:e});function c({links:e,locale:t}){let[s,l]=(0,o.useState)(!1);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{className:"md:hidden ml-2 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100",onClick:()=>l(!s),"aria-label":"Open Menu",children:s?(0,r.jsx)(a.QCr,{}):(0,r.jsx)(a.OXb,{})}),s&&(0,r.jsx)("div",{className:"md:hidden pb-4",children:(0,r.jsxs)("nav",{className:"space-y-4",children:[e.map(e=>(0,r.jsx)(n,{href:e.href,locale:t,children:e.name},e.name)),(0,r.jsx)("div",{className:"pt-4",children:(0,r.jsx)(i.default,{locale:t})})]})})]})}},23399:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx","default")},23440:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx","default")},25536:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},32410:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C,generateMetadata:()=>k,generateStaticParams:()=>w});var r=s(37413),o=s(49521),l=s.n(o),a=s(7905),i=s.n(a),n=s(88946),c=s(83930),d=s(39916);s(61135);var m=s(23440),x=s(78878),h=s(64348),u=s(80408),p=s(23399),g=s(12623),f=s(37062);let b=({children:e,href:t,locale:s})=>(0,r.jsx)(x.N_,{href:t,locale:s,className:"px-2 py-1 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-200 transition-colors",children:e});async function v({locale:e}){let t=await (0,h.A)({locale:e,namespace:"navigation"}),s=[{name:t("home"),href:"/"},{name:t("tools"),href:"/tools"},{name:t("categories"),href:"/categories"},{name:t("submit"),href:"/submit"}];return(0,r.jsx)("header",{className:"bg-white px-4 shadow-sm border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,r.jsxs)(x.N_,{href:"/",locale:e,className:"flex items-center space-x-2 hover:no-underline",children:[(0,r.jsx)("img",{src:"/logo.png",alt:"AI Tools Logo",className:"w-8 h-8 rounded-lg object-cover"}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"zh"===e?"AI工具导航":"AI Tools"})]}),(0,r.jsx)("nav",{className:"hidden md:flex space-x-4",children:s.map(t=>(0,r.jsx)(b,{href:t.href,locale:e,children:t.name},t.name))})]}),(0,r.jsx)("div",{className:"flex-1 max-w-md mx-8 hidden md:block",children:(0,r.jsx)(f.default,{locale:e})}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(p.default,{currentLocale:e}),(0,r.jsx)(u.default,{locale:e}),(0,r.jsx)(g.default,{links:s,locale:e})]})]})})}async function y({locale:e}){let t=await (0,h.A)({locale:e,namespace:"layout"});return(0,r.jsx)("footer",{className:"bg-white border-t border-gray-200 mt-16",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 md:col-span-2",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("img",{src:"/logo.png",alt:"AI Tools Logo",className:"w-12 h-12 rounded-lg object-cover"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"zh"===e?"AI工具导航":"AI Tools"})]}),(0,r.jsx)("p",{className:"text-gray-600 mb-4",children:t("footer.description")})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:t("footer.quick_links")}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/tools",locale:e,className:"text-gray-600 hover:text-blue-600",children:t("footer.tools_directory")})}),(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/categories",locale:e,className:"text-gray-600 hover:text-blue-600",children:t("footer.browse_categories")})}),(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/submit",locale:e,className:"text-gray-600 hover:text-blue-600",children:t("footer.submit_tool")})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4",children:t("footer.support")}),(0,r.jsxs)("ul",{className:"space-y-2",children:[(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/terms",locale:e,className:"text-gray-600 hover:text-blue-600",children:t("footer.service_terms")})}),(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"mailto:<EMAIL>",locale:e,className:"text-gray-600 hover:text-blue-600",children:t("footer.contact_us")})}),(0,r.jsx)("li",{children:(0,r.jsx)(x.N_,{href:"/privacy",locale:e,className:"text-gray-600 hover:text-blue-600",children:t("footer.privacy_policy")})})]})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 mt-8 pt-8",children:(0,r.jsx)("p",{className:"text-center text-gray-600",children:t("footer.copyright")})})]})})}var j=s(50290),N=s(12688);async function w(){return N.IB.map(e=>({locale:e}))}async function k({params:e}){let{locale:t}=await e;return"zh"===t?{title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",keywords:"AI工具,人工智能,AI导航,机器学习工具,深度学习,自动化工具,AI应用",authors:[{name:"AI工具导航团队"}],creator:"AI工具导航",publisher:"AI工具导航",icons:{icon:"/logo.png",shortcut:"/logo.png",apple:"/logo.png"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"zh_CN",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:"AI工具导航",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。从文本生成到图像创作，找到适合您需求的完美AI工具。",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AI工具导航 - 发现最好的人工智能工具"}]},twitter:{card:"summary_large_image",title:"AI工具导航 - 发现最好的人工智能工具",description:"探索精选的人工智能工具集合，提升您的工作效率和创造力。",images:["/og-image.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",languages:{zh:"/",en:"/en"}},verification:{google:"your-google-verification-code"}}:{title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.",keywords:"AI tools,artificial intelligence,AI directory,machine learning tools,deep learning,automation tools,AI applications",authors:[{name:"AI Tools Directory Team"}],creator:"AI Tools Directory",publisher:"AI Tools Directory",icons:{icon:"/logo.png",shortcut:"/logo.png",apple:"/logo.png"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},openGraph:{type:"website",locale:"en_US",url:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",siteName:"AI Tools Directory",title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity. From text generation to image creation, find the perfect AI tool for your needs.",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"AI Tools Directory - Discover the Best AI Tools"}]},twitter:{card:"summary_large_image",title:"AI Tools Directory - Discover the Best AI Tools",description:"Explore curated collection of AI tools to boost your productivity and creativity.",images:["/og-image.jpg"]},alternates:{canonical:process.env.NEXT_PUBLIC_BASE_URL||"https://aitools.example.com",languages:{zh:"/",en:"/en"}},verification:{google:"your-google-verification-code"}}}async function C({children:e,params:t}){let{locale:s}=await t;N.IB.includes(s)||(0,d.notFound)();let o=await (0,c.A)({locale:s});return(0,r.jsx)(n.A,{messages:o,locale:s,children:(0,r.jsx)(m.default,{children:(0,r.jsx)(j.LikeProvider,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 flex flex-col",children:[(0,r.jsx)(v,{locale:s}),(0,r.jsx)("main",{className:`${l().variable} ${i().variable} antialiased flex-1`,children:e}),(0,r.jsx)(y,{locale:s})]})})})})}},37062:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx","default")},39254:(e,t,s)=>{"use strict";s.d(t,{default:()=>m});var r=s(60687),o=s(43210),l=s(82136),a=s(23877),i=s(12340),n=s(77618),c=s(48577);let d=({children:e,href:t,locale:s})=>(0,r.jsx)(i.N_,{href:t,locale:s,className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:e});function m({locale:e}){let{data:t,status:s}=(0,l.useSession)();(0,i.rd)();let[m,x]=(0,o.useState)(!1),[h,u]=(0,o.useState)(!1),p=(0,n.c3)("common"),g=async()=>{await (0,l.signOut)({callbackUrl:"/"})};return"loading"===s?(0,r.jsx)("button",{className:"px-3 py-2 text-sm text-gray-600 bg-gray-100 rounded-lg animate-pulse",disabled:!0,children:p("loading")}):t?(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 p-1 rounded-lg hover:bg-gray-100 transition-colors",onClick:()=>u(!h),children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:t.user?.image?(0,r.jsx)("img",{src:t.user.image,alt:t.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:t.user?.name?.charAt(0)||"U"})}),(0,r.jsx)("span",{className:"text-sm hidden md:block",children:t.user?.name}),(0,r.jsx)(a.Vr3,{className:`text-xs transition-transform ${h?"rotate-180":""}`})]}),h&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>u(!1)}),(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border z-20",onClick:()=>u(!h),children:[(0,r.jsx)("div",{className:"p-4 border-b",children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-300 flex items-center justify-center overflow-hidden",children:t.user?.image?(0,r.jsx)("img",{src:t.user.image,alt:t.user.name||"",className:"w-full h-full object-cover"}):(0,r.jsx)("span",{className:"text-lg font-medium text-gray-600",children:t.user?.name?.charAt(0)||"U"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-sm",children:t.user?.name}),(0,r.jsx)("p",{className:"text-gray-500 text-xs",children:t.user?.email}),t.user?.role==="admin"&&(0,r.jsx)("span",{className:"inline-block mt-1 px-2 py-1 text-xs bg-red-100 text-red-800 rounded",children:p("admin")})]})]})}),(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsxs)(d,{href:"/profile",locale:e,children:[(0,r.jsx)(a.x$1,{}),p("profile")]}),(0,r.jsxs)(d,{href:"/profile/submitted",locale:e,children:[(0,r.jsx)(a.svy,{}),p("my_submissions")]}),(0,r.jsxs)(d,{href:"/profile/liked",locale:e,children:[(0,r.jsx)(a.Mbv,{}),p("my_favorites")]}),(0,r.jsxs)(d,{href:"/submit",locale:e,children:[(0,r.jsx)(a.OiG,{}),p("submit_tool")]})]}),t.user?.role==="admin"&&(0,r.jsx)("div",{className:"border-t py-2",children:(0,r.jsxs)(d,{href:"/admin",locale:e,children:[(0,r.jsx)(a.Pcn,{}),p("admin_panel")]})}),(0,r.jsxs)("div",{className:"border-t py-2",children:[(0,r.jsxs)(d,{href:"/settings",locale:e,children:[(0,r.jsx)(a.Pcn,{}),p("settings")]}),(0,r.jsxs)("button",{onClick:g,className:"w-full flex items-center gap-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,r.jsx)(a.axc,{}),p("logout")]})]})]})]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{className:"flex items-center gap-2 px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors",onClick:()=>x(!0),children:[(0,r.jsx)(a.Zu,{}),p("login")]}),(0,r.jsx)(c.A,{isOpen:m,onClose:()=>x(!1)})]})}},45304:()=>{},48577:(e,t,s)=>{"use strict";s.d(t,{A:()=>d});var r=s(60687),o=s(43210),l=s(82136),a=s(12340),i=s(77618),n=s(78521),c=s(23877);function d({isOpen:e,onClose:t}){let[s,d]=(0,o.useState)("method"),[m,x]=(0,o.useState)(""),[h,u]=(0,o.useState)(""),[p,g]=(0,o.useState)(!1),[f,b]=(0,o.useState)("");(0,a.a8)();let v=(0,i.c3)("auth");(0,n.Ym)();let y=(e,t="success")=>{let s=document.createElement("div");s.className=`fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${"success"===t?"bg-green-500":"bg-red-500"}`,s.textContent=e,document.body.appendChild(s),setTimeout(()=>document.body.removeChild(s),3e3)},j=()=>{d("method"),x(""),u(""),b(""),t()},N=async e=>{try{g(!0),await (0,l.signIn)(e,{callbackUrl:"/"})}catch(e){y(v("login_failed"),"error")}finally{g(!1)}},w=async()=>{if(!m)return void b(v("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(m))return void b(v("email_invalid"));b(""),g(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:m})}),t=await e.json();t.success?(u(t.token),d("code"),y(v("verification_sent"))):y(t.error||v("send_failed"),"error")}catch(e){y(v("network_error"),"error")}finally{g(!1)}},k=async e=>{if(6===e.length){g(!0);try{let t=await (0,l.signIn)("email-code",{email:m,code:e,token:h,redirect:!1});t?.ok?(y(v("login_success")),j()):y(t?.error||v("verification_error"),"error")}catch(e){y(v("network_error"),"error")}finally{g(!1)}}},C=(e,t)=>{if(t.length>1)return;let s=document.querySelectorAll(".code-input");s[e].value=t,t&&e<5&&s[e+1]?.focus();let r=Array.from(s).map(e=>e.value).join("");6===r.length&&k(r)};return e?(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:j}),(0,r.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===s&&v("login_title"),"email"===s&&v("email_login_title"),"code"===s&&v("verification_title")]}),(0,r.jsx)("button",{onClick:j,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,r.jsx)(c.QCr,{})})]}),(0,r.jsxs)("div",{className:"p-6",children:["method"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:v("choose_method")}),(0,r.jsx)("div",{className:"space-y-3",children:(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors disabled:opacity-50",onClick:()=>N("google"),disabled:p,children:[(0,r.jsx)(c.DSS,{}),v("google_login")]})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:v("or")})})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-black-300 rounded-lg text-black-700 hover:bg-black-50 transition-colors",onClick:()=>d("email"),children:[(0,r.jsx)(c.maD,{}),v("email_login")]})]}),"email"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:v("email_instruction")}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("email_address")}),(0,r.jsx)("input",{type:"email",value:m,onChange:e=>x(e.target.value),placeholder:v("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&w(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),f&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:w,disabled:p,children:p?v("sending"):v("send_code")}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>d("method"),children:v("back")})]})]}),"code"===s&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-600 text-center",children:v("verification_instruction",{email:m})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:v("verification_code")}),(0,r.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,r.jsx)("input",{type:"text",maxLength:1,onChange:t=>C(e,t.target.value),disabled:p,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>d("email"),children:v("resend_code")}),(0,r.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>d("method"),children:v("back")})]})]})]})]})]}):null}},50290:(e,t,s)=>{"use strict";s.d(t,{LikeProvider:()=>o});var r=s(12907);let o=(0,r.registerClientReference)(function(){throw Error("Attempted to call LikeProvider() from the server but LikeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","LikeProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useLike() from the server but useLike is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx","useLike")},51188:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),o=s(12340),l=s(77618),a=s(23877);function i({locale:e,className:t=""}){let s=(0,o.rd)(),i=(0,l.c3)("navigation");return(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault();let t=new FormData(e.currentTarget).get("search");t.trim()&&s.push(`/search?q=${encodeURIComponent(t.trim())}`)},className:t,children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(a.KSO,{className:"text-gray-400"})}),(0,r.jsx)("input",{name:"search",type:"text",placeholder:i("search_placeholder"),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})})}},61135:()=>{},61984:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},70440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(31658);let o=async e=>[{type:"image/x-icon",sizes:"200x200",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},72785:(e,t,s)=>{Promise.resolve().then(s.bind(s,39130)),Promise.resolve().then(s.bind(s,45196)),Promise.resolve().then(s.bind(s,39254)),Promise.resolve().then(s.bind(s,84541)),Promise.resolve().then(s.bind(s,22017)),Promise.resolve().then(s.bind(s,51188)),Promise.resolve().then(s.bind(s,76242)),Promise.resolve().then(s.bind(s,2328))},76242:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var r=s(60687),o=s(82136);function l({children:e}){return(0,r.jsx)(o.SessionProvider,{children:e})}},78878:(e,t,s)=>{"use strict";s.d(t,{N_:()=>a,V2:()=>i});var r=s(55946),o=s(92118);let l=(0,r.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:a,redirect:i,usePathname:n,useRouter:c}=(0,o.A)(l)},80408:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx","default")},82513:(e,t,s)=>{Promise.resolve().then(s.bind(s,75788)),Promise.resolve().then(s.bind(s,80994)),Promise.resolve().then(s.bind(s,80408)),Promise.resolve().then(s.bind(s,23399)),Promise.resolve().then(s.bind(s,12623)),Promise.resolve().then(s.bind(s,37062)),Promise.resolve().then(s.bind(s,23440)),Promise.resolve().then(s.bind(s,50290))},84541:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var r=s(60687),o=s(43210),l=s(16189),a=s(23877);let i=["en","zh"],n={zh:"中文",en:"English"};function c({currentLocale:e}){let[t,s]=(0,o.useState)(!1),c=(0,l.useRouter)(),d=(0,l.usePathname)(),m=e=>{let t=d?.split("/")?.filter(Boolean)||[];t.length>0&&i.includes(t[0])&&t.shift();let r=`/${e}${t.length>0?"/"+t.join("/"):""}`;c.replace(r),s(!1)};return(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>s(!t),className:"flex items-center space-x-1 px-3 py-2 text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors",children:[(0,r.jsx)(a.f35,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"text-sm",children:n[e]})]}),t&&(0,r.jsx)("div",{className:"absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-50",children:i.map(t=>(0,r.jsx)("button",{onClick:()=>m(t),className:`block w-full text-left px-4 py-2 text-sm hover:bg-gray-100 transition-colors ${t===e?"bg-blue-50 text-blue-600":"text-gray-700"}`,children:n[t]},t))})]})}},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413),o=s(7339),l=s.n(o);function a({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:l().className,children:e})})}}};