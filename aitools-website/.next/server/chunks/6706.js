"use strict";exports.id=6706,exports.ids=[6706],exports.modules={79171:(e,r,t)=>{t.d(r,{$g:()=>c,Ef:()=>i,S9:()=>l,kX:()=>a,mV:()=>n,mp:()=>s});let a={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}};a.FREE_LAUNCH.description,a.FREE_LAUNCH.displayPrice,a.FREE_LAUNCH.features,a.PRIORITY_LAUNCH.description,a.PRIORITY_LAUNCH.displayPrice,a.PRIORITY_LAUNCH.features;let o={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}};o.FREE.value,o.FREE.label,o.FREEMIUM.value,o.FREEMIUM.label,o.PAID.value,o.PAID.label,o.FREE.value,o.FREE.label,o.FREEMIUM.value,o.FREEMIUM.label,o.PAID.value,o.PAID.label;let i=e=>{switch(e){case o.FREE.value:return o.FREE.color;case o.FREEMIUM.value:return o.FREEMIUM.color;case o.PAID.value:return o.PAID.color;default:return"bg-gray-100 text-gray-800"}},n=e=>{switch(e){case o.FREE.value:return o.FREE.label;case o.FREEMIUM.value:return o.FREEMIUM.label;case o.PAID.value:return o.PAID.label;default:return e}},c=(e,r)=>0===e?"zh"===r?"免费":"Free":`\xa5${e}`,l=(e,r)=>0===e?"zh"===r?"免费":"Free":`\xa5${e}`,s=()=>{let e=a.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0}},96706:(e,r,t)=>{t.d(r,{ZW:()=>s,bw:()=>l,f:()=>n,stripe:()=>i});var a=t(97877),o=t(79171);let i=new a.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-05-28.basil",typescript:!0});async function n(e,r="cny",t={}){try{return await i.paymentIntents.create({amount:e,currency:r,metadata:t,automatic_payment_methods:{enabled:!0}})}catch(e){throw console.error("Error creating payment intent:",e),Error("Failed to create payment intent")}}async function c(e,r,t={}){try{return await i.customers.create({email:e,name:r,metadata:t})}catch(e){throw console.error("Error creating Stripe customer:",e),Error("Failed to create customer")}}async function l(e,r,t={}){try{let a=await i.customers.list({email:e,limit:1});if(a.data.length>0)return a.data[0];return await c(e,r,t)}catch(e){throw console.error("Error getting or creating Stripe customer:",e),Error("Failed to get or create customer")}}function s(e,r,t){try{return i.webhooks.constructEvent(e,r,t)}catch(e){throw console.error("Error constructing webhook event:",e),Error("Invalid webhook signature")}}o.kX.PRIORITY_LAUNCH.productName,o.kX.PRIORITY_LAUNCH.stripeAmount,o.kX.PRIORITY_LAUNCH.stripeCurrency,o.kX.PRIORITY_LAUNCH.description,o.kX.PRIORITY_LAUNCH.features}};