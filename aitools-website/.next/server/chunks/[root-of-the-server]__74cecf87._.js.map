{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/verify-badge/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { toolId, website, expectedText } = await request.json();\n\n    if (!website || !expectedText) {\n      return NextResponse.json(\n        { success: false, error: 'Missing required parameters' },\n        { status: 400 }\n      );\n    }\n\n    // 验证网站URL格式\n    let websiteUrl: URL;\n    try {\n      websiteUrl = new URL(website.startsWith('http') ? website : `https://${website}`);\n    } catch (error) {\n      return NextResponse.json(\n        { success: false, error: 'Invalid website URL' },\n        { status: 400 }\n      );\n    }\n\n    // 获取网站内容并检查是否包含badge\n    try {\n      const response = await fetch(`http://api.scraperapi.com/?api_key=********************************&url=${websiteUrl.toString()}`, {\n        // headers: {\n        //   'User-Agent': 'AITools Badge Verification Bot/1.0',\n        // },\n        // 设置超时时间\n        signal: AbortSignal.timeout(10000), // 10秒超时\n      });\n\n      if (!response.ok) {\n        return NextResponse.json(\n          { success: false, error: `Website returned ${response.status} status` },\n          { status: 400 }\n        );\n      }\n\n      const html = await response.text();\n\n      console.log('html.................', expectedText, html)\n      \n      // 检查是否包含预期的文本\n      const containsBadge = html.includes(expectedText) && html.includes('aitools.pub');\n      \n      if (!containsBadge) {\n        return NextResponse.json(\n          { success: false, error: 'Badge not found on the website. Please make sure you have added the badge code to your homepage.' },\n          { status: 400 }\n        );\n      }\n\n      // 验证成功，不更新数据库，直接返回成功状态\n      return NextResponse.json({\n        success: true,\n        message: 'Badge verification successful'\n      });\n\n    } catch (fetchError) {\n      console.error('Failed to fetch website:', fetchError);\n      \n      if (fetchError instanceof Error && fetchError.name === 'TimeoutError') {\n        return NextResponse.json(\n          { success: false, error: 'Website request timed out. Please try again later.' },\n          { status: 408 }\n        );\n      }\n      \n      return NextResponse.json(\n        { success: false, error: 'Failed to access the website. Please check if the URL is correct and the website is accessible.' },\n        { status: 400 }\n      );\n    }\n\n  } catch (error) {\n    console.error('Badge verification error:', error);\n    return NextResponse.json(\n      { success: false, error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE5D,IAAI,CAAC,WAAW,CAAC,cAAc;YAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA8B,GACvD;gBAAE,QAAQ;YAAI;QAElB;QAEA,YAAY;QACZ,IAAI;QACJ,IAAI;YACF,aAAa,IAAI,IAAI,QAAQ,UAAU,CAAC,UAAU,UAAU,CAAC,QAAQ,EAAE,SAAS;QAClF,EAAE,OAAO,OAAO;YACd,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsB,GAC/C;gBAAE,QAAQ;YAAI;QAElB;QAEA,qBAAqB;QACrB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,wEAAwE,EAAE,WAAW,QAAQ,IAAI,EAAE;gBAC/H,aAAa;gBACb,wDAAwD;gBACxD,KAAK;gBACL,SAAS;gBACT,QAAQ,YAAY,OAAO,CAAC;YAC9B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO,CAAC,iBAAiB,EAAE,SAAS,MAAM,CAAC,OAAO,CAAC;gBAAC,GACtE;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,QAAQ,GAAG,CAAC,yBAAyB,cAAc;YAEnD,cAAc;YACd,MAAM,gBAAgB,KAAK,QAAQ,CAAC,iBAAiB,KAAK,QAAQ,CAAC;YAEnE,IAAI,CAAC,eAAe;gBAClB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAmG,GAC5H;oBAAE,QAAQ;gBAAI;YAElB;YAEA,uBAAuB;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QAEF,EAAE,OAAO,YAAY;YACnB,QAAQ,KAAK,CAAC,4BAA4B;YAE1C,IAAI,sBAAsB,SAAS,WAAW,IAAI,KAAK,gBAAgB;gBACrE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAAqD,GAC9E;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkG,GAC3H;gBAAE,QAAQ;YAAI;QAElB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAwB,GACjD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}