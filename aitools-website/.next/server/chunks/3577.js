"use strict";exports.id=3577,exports.ids=[3577],exports.modules={277:(e,t,n)=>{n.d(t,{I:()=>s});var r=n(92373),i=n(17405),l=n(11855);let o=/[A-Z]/g,a=/-[a-z]/g,u=/^data[-\w.:]+$/i;function s(e,t){let n=(0,l.S)(t),s=t,p=i.R;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&u.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(a,f);s="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!a.test(e)){let n=e.replace(o,c);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}p=r.E}return new p(s,t)}function c(e){return"-"+e.toLowerCase()}function f(e){return e.charAt(1).toUpperCase()}},2557:(e,t,n)=>{function r(e){if(e)throw e}n.d(t,{l:()=>f});var i=n(26556),l=n(33849);function o(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}var a=n(89316);let u=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},s={}.hasOwnProperty;class c extends u{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(l,...o){let a=e[++n],u=-1;if(l)return void r(l);for(;++u<t.length;)(null===o[u]||void 0===o[u])&&(o[u]=t[u]);t=o,a?(function(e,t){let n;return function(...t){let l,o=e.length>t.length;o&&t.push(r);try{l=e.apply(this,t)}catch(e){if(o&&n)throw e;return r(e)}o||(l&&l.then&&"function"==typeof l.then?l.then(i,r):l instanceof Error?r(l):i(l))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(a,i)(...o):r(null,...o)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new c,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(i(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(h("data",this.frozen),this.namespace[e]=t,this):s.call(this.namespace,e)&&this.namespace[e]||void 0:e?(h("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=b(e),n=this.parser||this.Parser;return p("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),p("process",this.parser||this.Parser),d("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,i){let o=b(e),a=n.parse(o);function u(e,n){e||!n?i(e):r?r(n):((0,l.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(a,o,function(e,t,r){var i,l;if(e||!t||!r)return u(e);let o=n.stringify(t,r);"string"==typeof(i=o)||(l=i)&&"object"==typeof l&&"byteLength"in l&&"byteOffset"in l?r.value=o:r.result=o,u(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),p("processSync",this.parser||this.Parser),d("processSync",this.compiler||this.Compiler),this.process(e,function(e,i){n=!0,r(e),t=i}),g("processSync","process",n),(0,l.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){m(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?i(void 0,n):new Promise(i);function i(i,o){(0,l.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let a=b(t);r.run(e,a,function(t,r,a){let u=r||e;t?o(t):i?i(u):((0,l.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,u,a))})}}runSync(e,t){let n,i=!1;return this.run(e,t,function(e,t){r(e),n=t,i=!0}),g("runSync","run",i),(0,l.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=b(t),r=this.compiler||this.Compiler;return d("stringify",r),m(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(h("use",this.frozen),null==e);else if("function"==typeof e)u(e,t);else if("object"==typeof e)Array.isArray(e)?a(e):l(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function l(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");a(e.plugins),e.settings&&(r.settings=i(!0,r.settings,e.settings))}function a(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)u(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;u(e,t)}else l(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function u(e,t){let r=-1,l=-1;for(;++r<n.length;)if(n[r][0]===e){l=r;break}if(-1===l)n.push([e,...t]);else if(t.length>0){let[r,...a]=t,u=n[l][1];o(u)&&o(r)&&(r=i(!0,u,r)),n[l]=[e,r,...a]}}}}let f=new c().freeze();function p(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function d(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function h(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function m(e){if(!o(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function g(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function b(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new a.T(e)}},4597:(e,t,n)=>{n.d(t,{W:()=>r});function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}},4698:(e,t,n)=>{n.d(t,{G1:()=>o,PW:()=>i,Y:()=>r});let r=l("end"),i=l("start");function l(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function o(e){let t=i(e),n=r(e);if(t&&n)return{start:t,end:n}}},6815:(e,t,n)=>{n.d(t,{y:()=>l});var r=n(11287);let i={}.hasOwnProperty;function l(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let l,o=(i.call(e,n)?e[n]:void 0)||(e[n]={}),a=t[n];if(a)for(l in a){i.call(o,l)||(o[l]=[]);let e=a[l];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.m)(e,0,0,i)}(o[l],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},11287:(e,t,n)=>{function r(e,t,n,r){let i,l=e.length,o=0;if(t=t<0?-t>l?0:l+t:t>l?l:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);o<r.length;)(i=r.slice(o,o+1e4)).unshift(t,0),e.splice(...i),o+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:()=>i,m:()=>r})},11715:(e,t,n)=>{n.d(t,{A:()=>r});function r(e){return e.join(" ").trim()}},11855:(e,t,n)=>{n.d(t,{S:()=>r});function r(e){return e.toLowerCase()}},14165:(e,t,n)=>{n.d(t,{S:()=>i});var r=n(71164);function i(e){return null===e||(0,r.Ee)(e)||(0,r.Ny)(e)?1:(0,r.es)(e)?2:void 0}},17405:(e,t,n)=>{n.d(t,{R:()=>r});class r{constructor(e,t){this.attribute=t,this.property=e}}r.prototype.attribute="",r.prototype.booleanish=!1,r.prototype.boolean=!1,r.prototype.commaOrSpaceSeparated=!1,r.prototype.commaSeparated=!1,r.prototype.defined=!1,r.prototype.mustUseProperty=!1,r.prototype.number=!1,r.prototype.overloadedBoolean=!1,r.prototype.property="",r.prototype.spaceSeparated=!1,r.prototype.space=void 0},18732:(e,t,n)=>{n.d(t,{f:()=>o});var r=n(28836),i=n(92072),l=n(53728);function o(e,t){let n=!1;return(0,r.YR)(e,function(e){if("value"in e&&/\r?\n|\r/.test(e.value)||"break"===e.type)return n=!0,i.dc}),!!((!e.depth||e.depth<3)&&(0,l.d)(e)&&(t.options.setext||n))}},19143:(e,t,n)=>{n.d(t,{m:()=>r});function r(e,t){return!!(!1===t.options.fences&&e.value&&!e.lang&&/[^ \r\n]/.test(e.value)&&!/^[\t ]*(?:[\r\n]|$)|(?:^|[\r\n])[\t ]*$/.test(e.value))}},20610:(e,t,n)=>{function r(e,t){return i(e,t.inConstruct,!0)&&!i(e,t.notInConstruct,!1)}function i(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}n.d(t,{q:()=>r})},25464:(e,t,n)=>{n.r(t),n.d(t,{boolean:()=>i,booleanish:()=>l,commaOrSpaceSeparated:()=>c,commaSeparated:()=>s,number:()=>a,overloadedBoolean:()=>o,spaceSeparated:()=>u});let r=0,i=f(),l=f(),o=f(),a=f(),u=f(),s=f(),c=f();function f(){return 2**++r}},26373:(e,t,n)=>{n.d(t,{A:()=>f});var r=n(61120);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase()),o=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},a=(...e)=>e.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim(),u=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:i,className:l="",children:o,iconNode:c,...f},p)=>(0,r.createElement)("svg",{ref:p,...s,width:t,height:t,stroke:e,strokeWidth:i?24*Number(n)/Number(t):n,className:a("lucide",l),...!o&&!u(f)&&{"aria-hidden":"true"},...f},[...c.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]])),f=(e,t)=>{let n=(0,r.forwardRef)(({className:n,...l},u)=>(0,r.createElement)(c,{ref:u,iconNode:t,className:a(`lucide-${i(o(e))}`,`lucide-${e}`,n),...l}));return n.displayName=o(e),n}},26556:e=>{var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,l=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},o=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),l=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!l)return!1;for(r in e);return void 0===r||t.call(e,r)},a=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},u=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,s,c,f=arguments[0],p=1,d=arguments.length,h=!1;for("boolean"==typeof f&&(h=f,f=arguments[1]||{},p=2),(null==f||"object"!=typeof f&&"function"!=typeof f)&&(f={});p<d;++p)if(t=arguments[p],null!=t)for(n in t)r=u(f,n),f!==(i=u(t,n))&&(h&&i&&(o(i)||(s=l(i)))?(s?(s=!1,c=r&&l(r)?r:[]):c=r&&o(r)?r:{},a(f,{name:n,newValue:e(h,c,i)})):void 0!==i&&a(f,{name:n,newValue:i}));return f}},28836:(e,t,n)=>{n.d(t,{YR:()=>i});var r=n(92072);function i(e,t,n,i){let l,o,a;"function"==typeof t&&"function"!=typeof n?(o=void 0,a=t,l=n):(o=t,a=n,l=i),(0,r.VG)(e,o,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return a(e,r,n)},l)}},33444:(e,t,n)=>{n.d(t,{o:()=>i});var r=n(42626);class i extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let i="",l={},o=!1;if(t&&(l="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?i=e:!l.cause&&e&&(o=!0,i=e.message,l.cause=e),!l.ruleId&&!l.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?l.ruleId=n:(l.source=n.slice(0,e),l.ruleId=n.slice(e+1))}if(!l.place&&l.ancestors&&l.ancestors){let e=l.ancestors[l.ancestors.length-1];e&&(l.place=e.position)}let a=l.place&&"start"in l.place?l.place.start:l.place;this.ancestors=l.ancestors||void 0,this.cause=l.cause||void 0,this.column=a?a.column:void 0,this.fatal=void 0,this.file,this.message=i,this.line=a?a.line:void 0,this.name=(0,r.L)(l.place)||"1:1",this.place=l.place||void 0,this.reason=this.message,this.ruleId=l.ruleId||void 0,this.source=l.source||void 0,this.stack=o&&l.cause&&"string"==typeof l.cause.stack?l.cause.stack:"",this.actual,this.expected,this.note,this.url}}i.prototype.file="",i.prototype.name="",i.prototype.reason="",i.prototype.message="",i.prototype.stack="",i.prototype.column=void 0,i.prototype.line=void 0,i.prototype.ancestors=void 0,i.prototype.cause=void 0,i.prototype.fatal=void 0,i.prototype.place=void 0,i.prototype.ruleId=void 0,i.prototype.source=void 0},33488:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(26373).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},33788:(e,t,n)=>{n.d(t,{A:()=>r});function r(e,t){let n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}},33849:(e,t,n)=>{function r(){}function i(){}n.d(t,{HB:()=>i,ok:()=>r})},34967:(e,t,n)=>{n.d(t,{Ay:()=>p});let r="object"==typeof self?self:globalThis,i=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),i=l=>{if(e.has(l))return e.get(l);let[o,a]=t[l];switch(o){case 0:case -1:return n(a,l);case 1:{let e=n([],l);for(let t of a)e.push(i(t));return e}case 2:{let e=n({},l);for(let[t,n]of a)e[i(t)]=i(n);return e}case 3:return n(new Date(a),l);case 4:{let{source:e,flags:t}=a;return n(new RegExp(e,t),l)}case 5:{let e=n(new Map,l);for(let[t,n]of a)e.set(i(t),i(n));return e}case 6:{let e=n(new Set,l);for(let t of a)e.add(i(t));return e}case 7:{let{name:e,message:t}=a;return n(new r[e](t),l)}case 8:return n(BigInt(a),l);case"BigInt":return n(Object(BigInt(a)),l);case"ArrayBuffer":return n(new Uint8Array(a).buffer,a);case"DataView":{let{buffer:e}=new Uint8Array(a);return n(new DataView(e),a)}}return n(new r[o](a),l)};return i},l=e=>i(new Map,e)(0),{toString:o}={},{keys:a}=Object,u=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=o.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},s=([e,t])=>0===e&&("function"===t||"symbol"===t),c=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},l=r=>{if(n.has(r))return n.get(r);let[o,c]=u(r);switch(o){case 0:{let t=r;switch(c){case"bigint":o=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+c);t=null;break;case"undefined":return i([-1],r)}return i([o,t],r)}case 1:{if(c){let e=r;return"DataView"===c?e=new Uint8Array(r.buffer):"ArrayBuffer"===c&&(e=new Uint8Array(r)),i([c,[...e]],r)}let e=[],t=i([o,e],r);for(let t of r)e.push(l(t));return t}case 2:{if(c)switch(c){case"BigInt":return i([c,r.toString()],r);case"Boolean":case"Number":case"String":return i([c,r.valueOf()],r)}if(t&&"toJSON"in r)return l(r.toJSON());let n=[],f=i([o,n],r);for(let t of a(r))(e||!s(u(r[t])))&&n.push([l(t),l(r[t])]);return f}case 3:return i([o,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([o,{source:e,flags:t}],r)}case 5:{let t=[],n=i([o,t],r);for(let[n,i]of r)(e||!(s(u(n))||s(u(i))))&&t.push([l(n),l(i)]);return n}case 6:{let t=[],n=i([o,t],r);for(let n of r)(e||!s(u(n)))&&t.push(l(n));return n}}let{message:f}=r;return i([o,{name:c,message:f}],r)};return l},f=(e,{json:t,lossy:n}={})=>{let r=[];return c(!(t||n),!!t,new Map,r)(e),r},p="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?l(f(e,t)):structuredClone(e):(e,t)=>l(f(e,t))},38251:(e,t,n)=>{n.d(t,{s:()=>o});var r=n(70785),i=n(89011);let l=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function o(e){return e.replace(l,a)}function a(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return(0,i.C)(n.slice(t?2:1),t?16:10)}return(0,r.s)(n)||e}},42626:(e,t,n)=>{function r(e){return e&&"object"==typeof e?"position"in e||"type"in e?l(e.position):"start"in e||"end"in e?l(e):"line"in e||"column"in e?i(e):"":""}function i(e){return o(e&&e.line)+":"+o(e&&e.column)}function l(e){return i(e&&e.start)+"-"+i(e&&e.end)}function o(e){return e&&"number"==typeof e?e:1}n.d(t,{L:()=>r})},45717:(e,t,n)=>{n.d(t,{m:()=>i});let r=/[ \t\n\f\r]/g;function i(e){return"object"==typeof e?"text"===e.type&&l(e.value):l(e)}function l(e){return""===e.replace(r,"")}},49209:(e,t,n)=>{n.d(t,{C:()=>r});let r=function(e){var t,n;if(null==e)return l;if("function"==typeof e)return i(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,i(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,i(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function l(){return!0}},51104:(e,t,n)=>{n.d(t,{_:()=>w});var r=n(33849),i=n(34967),l=n(71164);function o(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),a="";if(37===o&&(0,l.lV)(e.charCodeAt(n+1))&&(0,l.lV)(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(a=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(a=String.fromCharCode(o,t),i=1):a="�"}else a=String.fromCharCode(o);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+i+1,a=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function a(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function u(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var s=n(28836),c=n(4698);function f(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),l=i[0];l&&"text"===l.type?l.value="["+l.value:i.unshift({type:"text",value:"["});let o=i[i.length-1];return o&&"text"===o.type?o.value+=r:i.push({type:"text",value:r}),i}function p(e){let t=e.spread;return null==t?e.children.length>1:t}function d(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let h={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),l=o(i.toLowerCase()),a=e.footnoteOrder.indexOf(i),u=e.footnoteCounts.get(i);void 0===u?(u=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=a+1,u+=1,e.footnoteCounts.set(i,u);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+l,id:r+"fnref-"+l+(u>1?"-"+u:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let c={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return f(e,t);let i={src:o(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,l),e.applyData(t,l)},image:function(e,t){let n={src:o(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return f(e,t);let i={href:o(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let l={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,l),e.applyData(t,l)},link:function(e,t){let n={href:o(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=p(n[r])}return t}(n):p(t),l={},o=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){let e=r[a];(i||0!==a||"element"!==e.type||"p"!==e.tagName)&&o.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?o.push(e):o.push(...e.children)}let u=r[r.length-1];u&&(i||"element"!==u.type||"p"!==u.tagName)&&o.push({type:"text",value:"\n"});let s={type:"element",tagName:"li",properties:l,children:o};return e.patch(t,s),e.applyData(t,s)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},l=(0,c.PW)(t.children[1]),o=(0,c.Y)(t.children[t.children.length-1]);l&&o&&(r.position={start:l,end:o}),i.push(r)}let l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",l=n&&"table"===n.type?n.align:void 0,o=l?l.length:t.children.length,a=-1,u=[];for(;++a<o;){let n=t.children[a],r={},o=l?l[a]:void 0;o&&(r.align=o);let s={type:"element",tagName:i,properties:r,children:[]};n&&(s.children=e.all(n),e.patch(n,s),s=e.applyData(n,s)),u.push(s)}let s={type:"element",tagName:"tr",properties:{},children:e.wrap(u,!0)};return e.patch(t,s),e.applyData(t,s)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,l=[];for(;r;)l.push(d(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(d(t.slice(i),i>0,!1)),l.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:m,yaml:m,definition:m,footnoteDefinition:m};function m(){}let g={}.hasOwnProperty,b={};function y(e,t){e.position&&(t.position=(0,c.G1)(e))}function k(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,l=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&l&&Object.assign(n.properties,(0,i.Ay)(l)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function v(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function x(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function w(e,t){let n=function(e,t){let n=t||b,r=new Map,l=new Map,o={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=o.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=x(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=x(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:k,definitionById:r,footnoteById:l,footnoteCounts:new Map,footnoteOrder:[],handlers:{...h,...n.handlers},one:function(e,t){let n=e.type,r=o.handlers[n];if(g.call(o.handlers,n)&&r)return r(o,e,t);if(o.options.passThrough&&o.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=(0,i.Ay)(n);return r.children=o.all(e),r}return(0,i.Ay)(e)}return(o.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(g.call(n,"hProperties")||g.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(o,e,t)},options:n,patch:y,wrap:v};return(0,s.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:l,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),o}(e,t),l=n.one(e,void 0),c=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||a,r=e.options.footnoteBackLabel||u,l=e.options.footnoteLabel||"Footnotes",s=e.options.footnoteLabelTagName||"h2",c=e.options.footnoteLabelProperties||{className:["sr-only"]},f=[],p=-1;for(;++p<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[p]);if(!i)continue;let l=e.all(i),a=String(i.identifier).toUpperCase(),u=o(a.toLowerCase()),s=0,c=[],d=e.footnoteCounts.get(a);for(;void 0!==d&&++s<=d;){c.length>0&&c.push({type:"text",value:" "});let e="string"==typeof n?n:n(p,s);"string"==typeof e&&(e={type:"text",value:e}),c.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+u+(s>1?"-"+s:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(p,s),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let h=l[l.length-1];if(h&&"element"===h.type&&"p"===h.tagName){let e=h.children[h.children.length-1];e&&"text"===e.type?e.value+=" ":h.children.push({type:"text",value:" "}),h.children.push(...c)}else l.push(...c);let m={type:"element",tagName:"li",properties:{id:t+"fn-"+u},children:e.wrap(l,!0)};e.patch(i,m),f.push(m)}if(0!==f.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:s,properties:{...(0,i.Ay)(c),id:"footnote-label"},children:[{type:"text",value:l}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(f,!0)},{type:"text",value:"\n"}]}}(n),f=Array.isArray(l)?{type:"root",children:l}:l||{type:"root",children:[]};return c&&((0,r.ok)("children"in f),f.children.push({type:"text",value:"\n"},c)),f}},53728:(e,t,n)=>{n.d(t,{d:()=>i});let r={};function i(e,t){let n=t||r;return l(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function l(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return o(e.children,t,n)}return Array.isArray(e)?o(e,t,n):""}function o(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=l(e[i],t,n);return r.join("")}},60730:(e,t,n)=>{function r(e,t,n){return">"+(n?"":" ")+e}n.d(t,{p:()=>D});var i=n(20610);function l(e,t,n,r){let l=-1;for(;++l<n.unsafe.length;)if("\n"===n.unsafe[l].character&&(0,i.q)(n.stack,n.unsafe[l]))return/[ \t]/.test(r.before)?"":" ";return"\\\n"}var o=n(19143);function a(e,t,n){return(n?"":"    ")+e}function u(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}var s=n(82629),c=n(14165);function f(e,t,n){let r=(0,c.S)(e),i=(0,c.S)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}function p(e,t,n,r){let i=function(e){let t=e.options.emphasis||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize emphasis with `"+t+"` for `options.emphasis`, expected `*`, or `_`");return t}(n),l=n.enter("emphasis"),o=n.createTracker(r),a=o.move(i),u=o.move(n.containerPhrasing(e,{after:i,before:a,...o.current()})),c=u.charCodeAt(0),p=f(r.before.charCodeAt(r.before.length-1),c,i);p.inside&&(u=(0,s.T)(c)+u.slice(1));let d=u.charCodeAt(u.length-1),h=f(r.after.charCodeAt(0),d,i);h.inside&&(u=u.slice(0,-1)+(0,s.T)(d));let m=o.move(i);return l(),n.attentionEncodeSurroundingInfo={after:h.outside,before:p.outside},a+u+m}p.peek=function(e,t,n){return n.options.emphasis||"*"};var d=n(18732);function h(e){return e.value||""}function m(e,t,n,r){let i=u(n),l='"'===i?"Quote":"Apostrophe",o=n.enter("image"),a=n.enter("label"),s=n.createTracker(r),c=s.move("![");return c+=s.move(n.safe(e.alt,{before:c,after:"]",...s.current()})),c+=s.move("]("),a(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":")",...s.current()}))),a(),e.title&&(a=n.enter(`title${l}`),c+=s.move(" "+i),c+=s.move(n.safe(e.title,{before:c,after:i,...s.current()})),c+=s.move(i),a()),c+=s.move(")"),o(),c}function g(e,t,n,r){let i=e.referenceType,l=n.enter("imageReference"),o=n.enter("label"),a=n.createTracker(r),u=a.move("!["),s=n.safe(e.alt,{before:u,after:"]",...a.current()});u+=a.move(s+"]["),o();let c=n.stack;n.stack=[],o=n.enter("reference");let f=n.safe(n.associationId(e),{before:u,after:"]",...a.current()});return o(),n.stack=c,l(),"full"!==i&&s&&s===f?"shortcut"===i?u=u.slice(0,-1):u+=a.move("]"):u+=a.move(f+"]"),u}function b(e,t,n){let r=e.value||"",i="`",l=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++l<n.unsafe.length;){let e,t=n.unsafe[l],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}h.peek=function(){return"<"},m.peek=function(){return"!"},g.peek=function(){return"!"},b.peek=function(){return"`"};var y=n(53728);function k(e,t){let n=(0,y.d)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}function v(e,t,n,r){let i,l,o=u(n),a='"'===o?"Quote":"Apostrophe",s=n.createTracker(r);if(k(e,n)){let t=n.stack;n.stack=[],i=n.enter("autolink");let r=s.move("<");return r+=s.move(n.containerPhrasing(e,{before:r,after:">",...s.current()})),r+=s.move(">"),i(),n.stack=t,r}i=n.enter("link"),l=n.enter("label");let c=s.move("[");return c+=s.move(n.containerPhrasing(e,{before:c,after:"](",...s.current()})),c+=s.move("]("),l(),!e.url&&e.title||/[\0- \u007F]/.test(e.url)?(l=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(l=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":")",...s.current()}))),l(),e.title&&(l=n.enter(`title${a}`),c+=s.move(" "+o),c+=s.move(n.safe(e.title,{before:c,after:o,...s.current()})),c+=s.move(o),l()),c+=s.move(")"),i(),c}function x(e,t,n,r){let i=e.referenceType,l=n.enter("linkReference"),o=n.enter("label"),a=n.createTracker(r),u=a.move("["),s=n.containerPhrasing(e,{before:u,after:"]",...a.current()});u+=a.move(s+"]["),o();let c=n.stack;n.stack=[],o=n.enter("reference");let f=n.safe(n.associationId(e),{before:u,after:"]",...a.current()});return o(),n.stack=c,l(),"full"!==i&&s&&s===f?"shortcut"===i?u=u.slice(0,-1):u+=a.move("]"):u+=a.move(f+"]"),u}function w(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}function S(e){let t=e.options.rule||"*";if("*"!==t&&"-"!==t&&"_"!==t)throw Error("Cannot serialize rules with `"+t+"` for `options.rule`, expected `*`, `-`, or `_`");return t}v.peek=function(e,t,n){return k(e,n)?"<":"["},x.peek=function(){return"["};let C=(0,n(49209).C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);function E(e,t,n,r){let i=function(e){let t=e.options.strong||"*";if("*"!==t&&"_"!==t)throw Error("Cannot serialize strong with `"+t+"` for `options.strong`, expected `*`, or `_`");return t}(n),l=n.enter("strong"),o=n.createTracker(r),a=o.move(i+i),u=o.move(n.containerPhrasing(e,{after:i,before:a,...o.current()})),c=u.charCodeAt(0),p=f(r.before.charCodeAt(r.before.length-1),c,i);p.inside&&(u=(0,s.T)(c)+u.slice(1));let d=u.charCodeAt(u.length-1),h=f(r.after.charCodeAt(0),d,i);h.inside&&(u=u.slice(0,-1)+(0,s.T)(d));let m=o.move(i+i);return l(),n.attentionEncodeSurroundingInfo={after:h.outside,before:p.outside},a+u+m}E.peek=function(e,t,n){return n.options.strong||"*"};let D={blockquote:function(e,t,n,i){let l=n.enter("blockquote"),o=n.createTracker(i);o.move("> "),o.shift(2);let a=n.indentLines(n.containerFlow(e,o.current()),r);return l(),a},break:l,code:function(e,t,n,r){let i=function(e){let t=e.options.fence||"`";if("`"!==t&&"~"!==t)throw Error("Cannot serialize code with `"+t+"` for `options.fence`, expected `` ` `` or `~`");return t}(n),l=e.value||"",u="`"===i?"GraveAccent":"Tilde";if((0,o.m)(e,n)){let e=n.enter("codeIndented"),t=n.indentLines(l,a);return e(),t}let s=n.createTracker(r),c=i.repeat(Math.max(function(e,t){let n=String(e),r=n.indexOf(t),i=r,l=0,o=0;if("string"!=typeof t)throw TypeError("Expected substring");for(;-1!==r;)r===i?++l>o&&(o=l):l=1,i=r+t.length,r=n.indexOf(t,i);return o}(l,i)+1,3)),f=n.enter("codeFenced"),p=s.move(c);if(e.lang){let t=n.enter(`codeFencedLang${u}`);p+=s.move(n.safe(e.lang,{before:p,after:" ",encode:["`"],...s.current()})),t()}if(e.lang&&e.meta){let t=n.enter(`codeFencedMeta${u}`);p+=s.move(" "),p+=s.move(n.safe(e.meta,{before:p,after:"\n",encode:["`"],...s.current()})),t()}return p+=s.move("\n"),l&&(p+=s.move(l+"\n")),p+=s.move(c),f(),p},definition:function(e,t,n,r){let i=u(n),l='"'===i?"Quote":"Apostrophe",o=n.enter("definition"),a=n.enter("label"),s=n.createTracker(r),c=s.move("[");return c+=s.move(n.safe(n.associationId(e),{before:c,after:"]",...s.current()})),c+=s.move("]: "),a(),!e.url||/[\0- \u007F]/.test(e.url)?(a=n.enter("destinationLiteral"),c+=s.move("<"),c+=s.move(n.safe(e.url,{before:c,after:">",...s.current()})),c+=s.move(">")):(a=n.enter("destinationRaw"),c+=s.move(n.safe(e.url,{before:c,after:e.title?" ":"\n",...s.current()}))),a(),e.title&&(a=n.enter(`title${l}`),c+=s.move(" "+i),c+=s.move(n.safe(e.title,{before:c,after:i,...s.current()})),c+=s.move(i),a()),o(),c},emphasis:p,hardBreak:l,heading:function(e,t,n,r){let i=Math.max(Math.min(6,e.depth||1),1),l=n.createTracker(r);if((0,d.f)(e,n)){let t=n.enter("headingSetext"),r=n.enter("phrasing"),o=n.containerPhrasing(e,{...l.current(),before:"\n",after:"\n"});return r(),t(),o+"\n"+(1===i?"=":"-").repeat(o.length-(Math.max(o.lastIndexOf("\r"),o.lastIndexOf("\n"))+1))}let o="#".repeat(i),a=n.enter("headingAtx"),u=n.enter("phrasing");l.move(o+" ");let c=n.containerPhrasing(e,{before:"# ",after:"\n",...l.current()});return/^[\t ]/.test(c)&&(c=(0,s.T)(c.charCodeAt(0))+c.slice(1)),c=c?o+" "+c:o,n.options.closeAtx&&(c+=" "+o),u(),a(),c},html:h,image:m,imageReference:g,inlineCode:b,link:v,linkReference:x,list:function(e,t,n,r){let i=n.enter("list"),l=n.bulletCurrent,o=e.ordered?function(e){let t=e.options.bulletOrdered||".";if("."!==t&&")"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bulletOrdered`, expected `.` or `)`");return t}(n):w(n),a=e.ordered?"."===o?")":".":function(e){let t=w(e),n=e.options.bulletOther;if(!n)return"*"===t?"-":"*";if("*"!==n&&"+"!==n&&"-"!==n)throw Error("Cannot serialize items with `"+n+"` for `options.bulletOther`, expected `*`, `+`, or `-`");if(n===t)throw Error("Expected `bullet` (`"+t+"`) and `bulletOther` (`"+n+"`) to be different");return n}(n),u=!!t&&!!n.bulletLastUsed&&o===n.bulletLastUsed;if(!e.ordered){let t=e.children?e.children[0]:void 0;if("*"!==o&&"-"!==o||!t||t.children&&t.children[0]||"list"!==n.stack[n.stack.length-1]||"listItem"!==n.stack[n.stack.length-2]||"list"!==n.stack[n.stack.length-3]||"listItem"!==n.stack[n.stack.length-4]||0!==n.indexStack[n.indexStack.length-1]||0!==n.indexStack[n.indexStack.length-2]||0!==n.indexStack[n.indexStack.length-3]||(u=!0),S(n)===o&&t){let t=-1;for(;++t<e.children.length;){let n=e.children[t];if(n&&"listItem"===n.type&&n.children&&n.children[0]&&"thematicBreak"===n.children[0].type){u=!0;break}}}}u&&(o=a),n.bulletCurrent=o;let s=n.containerFlow(e,r);return n.bulletLastUsed=o,n.bulletCurrent=l,i(),s},listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),l=n.bulletCurrent||w(n);t&&"list"===t.type&&t.ordered&&(l=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+l);let o=l.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(o=4*Math.ceil(o/4));let a=n.createTracker(r);a.move(l+" ".repeat(o-l.length)),a.shift(o);let u=n.enter("listItem"),s=n.indentLines(n.containerFlow(e,a.current()),function(e,t,n){return t?(n?"":" ".repeat(o))+e:(n?l:l+" ".repeat(o-l.length))+e});return u(),s},paragraph:function(e,t,n,r){let i=n.enter("paragraph"),l=n.enter("phrasing"),o=n.containerPhrasing(e,r);return l(),i(),o},root:function(e,t,n,r){return(e.children.some(function(e){return C(e)})?n.containerPhrasing:n.containerFlow).call(n,e,r)},strong:E,text:function(e,t,n,r){return n.safe(e.value,r)},thematicBreak:function(e,t,n){let r=(S(n)+(n.options.ruleSpaces?" ":"")).repeat(function(e){let t=e.options.ruleRepetition||3;if(t<3)throw Error("Cannot serialize rules with repetition `"+t+"` for `options.ruleRepetition`, expected `3` or more");return t}(n));return n.options.ruleSpaces?r.slice(0,-1):r}}},68798:(e,t,n)=>{n.d(t,{A:()=>eI});var r=n(98270),i=n(33849),l=n(71164),o=n(92072),a=n(49209);let u="phrasing",s=["autolink","link","image","label"];function c(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function f(e){this.config.enter.autolinkProtocol.call(this,e)}function p(e){this.config.exit.autolinkProtocol.call(this,e)}function d(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function h(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,a.C)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),l=-1;for(;++l<i.length;)(0,o.VG)(e,"text",u);function u(e,t){let n,o=-1;for(;++o<t.length;){let e=t[o],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[l][0],o=i[l][1],a=0,u=n.children.indexOf(e),s=!1,c=[];r.lastIndex=0;let f=r.exec(e.value);for(;f;){let n=f.index,i={index:f.index,input:f.input,stack:[...t,e]},l=o(...f,i);if("string"==typeof l&&(l=l.length>0?{type:"text",value:l}:void 0),!1===l?r.lastIndex=n+1:(a!==n&&c.push({type:"text",value:e.value.slice(a,n)}),Array.isArray(l)?c.push(...l):l&&c.push(l),a=n+f[0].length,s=!0),!r.global)break;f=r.exec(e.value)}return s?(a<e.value.length&&c.push({type:"text",value:e.value.slice(a)}),n.children.splice(u,1,...c)):c=[e],u+c.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,b],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,y]],{ignore:["link","linkReference"]})}function b(e,t,n,i,l){let o="";if(!k(l)||(/^w/i.test(t)&&(n=t+n,t="",o="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let a=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),l=(0,r.D)(e,"("),o=(0,r.D)(e,")");for(;-1!==i&&l>o;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),o++;return[e,n]}(n+i);if(!a[0])return!1;let u={type:"link",title:null,url:o+t+a[0],children:[{type:"text",value:t+a[0]}]};return a[1]?[u,{type:"text",value:a[1]}]:u}function y(e,t,n,r){return!(!k(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function k(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,l.Ny)(n)||(0,l.es)(n))&&(!t||47!==n)}var v=n(90148);function x(){this.buffer()}function w(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function S(){this.buffer()}function C(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function E(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,v.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function D(e){this.exit(e)}function T(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,v.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function A(e){this.exit(e)}function L(e,t,n,r){let i=n.createTracker(r),l=i.move("[^"),o=n.enter("footnoteReference"),a=n.enter("reference");return l+=i.move(n.safe(n.associationId(e),{after:"]",before:l})),a(),o(),l+=i.move("]")}function P(e,t,n){return 0===t?e:q(e,t,n)}function q(e,t,n){return(n?"":"    ")+e}L.peek=function(){return"["};let I=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function O(e){this.enter({type:"delete",children:[]},e)}function N(e){this.exit(e)}function R(e,t,n,r){let i=n.createTracker(r),l=n.enter("strikethrough"),o=i.move("~~");return o+=n.containerPhrasing(e,{...i.current(),before:o,after:"~"}),o+=i.move("~~"),l(),o}function z(e){return e.length}function F(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}R.peek=function(){return"~"};var M=n(60730);function B(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function H(e){this.exit(e),this.data.inTable=void 0}function V(e){this.enter({type:"tableRow",children:[]},e)}function U(e){this.exit(e)}function _(e){this.enter({type:"tableCell",children:[]},e)}function j(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,W));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function W(e,t){return"|"===t?t:e}function G(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function Y(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r,i=t.children,l=-1;for(;++l<i.length;){let e=i[l];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function Q(e,t,n,r){let i=e.children[0],l="boolean"==typeof e.checked&&i&&"paragraph"===i.type,o="["+(e.checked?"x":" ")+"] ",a=n.createTracker(r);l&&a.move(o);let u=M.p.listItem(e,t,n,{...r,...a.current()});return l&&(u=u.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+o})),u}var $=n(6815);let J={tokenize:function(e,t,n){let r=0;return function t(l){return(87===l||119===l)&&r<3?(r++,e.consume(l),t):46===l&&3===r?(e.consume(l),i):n(l)};function i(e){return null===e?n(e):t(e)}},partial:!0},Z={tokenize:function(e,t,n){let r,i,o;return a;function a(t){return 46===t||95===t?e.check(X,s,u)(t):null===t||(0,l.Ee)(t)||(0,l.Ny)(t)||45!==t&&(0,l.es)(t)?s(t):(o=!0,e.consume(t),a)}function u(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),a}function s(e){return i||r||!o?n(e):t(e)}},partial:!0},K={tokenize:function(e,t){let n=0,r=0;return i;function i(a){return 40===a?(n++,e.consume(a),i):41===a&&r<n?o(a):33===a||34===a||38===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||60===a||63===a||93===a||95===a||126===a?e.check(X,t,o)(a):null===a||(0,l.Ee)(a)||(0,l.Ny)(a)?t(a):(e.consume(a),i)}function o(t){return 41===t&&r++,e.consume(t),i}},partial:!0},X={tokenize:function(e,t,n){return r;function r(a){return 33===a||34===a||39===a||41===a||42===a||44===a||46===a||58===a||59===a||63===a||95===a||126===a?(e.consume(a),r):38===a?(e.consume(a),o):93===a?(e.consume(a),i):60===a||null===a||(0,l.Ee)(a)||(0,l.Ny)(a)?t(a):n(a)}function i(e){return null===e||40===e||91===e||(0,l.Ee)(e)||(0,l.Ny)(e)?t(e):r(e)}function o(t){return(0,l.CW)(t)?function t(i){return 59===i?(e.consume(i),r):(0,l.CW)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},ee={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,l.lV)(e)?n(e):t(e)}},partial:!0},et={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!eo.call(r,r.previous)||ec(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(J,e.attempt(Z,e.attempt(K,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:eo},en={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",o=!1;return function(t){return(72===t||104===t)&&ea.call(r,r.previous)&&!ec(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),a):n(t)};function a(t){if((0,l.CW)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),a;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),u}return n(t)}function u(t){return 47===t?(e.consume(t),o)?s:(o=!0,u):n(t)}function s(t){return null===t||(0,l.JQ)(t)||(0,l.Ee)(t)||(0,l.Ny)(t)||(0,l.es)(t)?n(t):e.attempt(Z,e.attempt(K,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:ea},er={name:"emailAutolink",tokenize:function(e,t,n){let r,i,o=this;return function(t){return!es(t)||!eu.call(o,o.previous)||ec(o.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return es(r)?(e.consume(r),t):64===r?(e.consume(r),a):n(r)}(t))};function a(t){return 46===t?e.check(ee,s,u)(t):45===t||95===t||(0,l.lV)(t)?(i=!0,e.consume(t),a):s(t)}function u(t){return e.consume(t),r=!0,a}function s(a){return i&&r&&(0,l.CW)(o.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(a)):n(a)}},previous:eu},ei={},el=48;for(;el<123;)ei[el]=er,58==++el?el=65:91===el&&(el=97);function eo(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,l.Ee)(e)}function ea(e){return!(0,l.CW)(e)}function eu(e){return!(47===e||es(e))}function es(e){return 43===e||45===e||46===e||95===e||(0,l.lV)(e)}function ec(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}ei[43]=er,ei[45]=er,ei[46]=er,ei[95]=er,ei[72]=[er,en],ei[104]=[er,en],ei[87]=[er,et],ei[119]=[er,et];var ef=n(87833),ep=n(85303);let ed={tokenize:function(e,t,n){let r=this;return(0,ep.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function eh(e,t,n){let r,i=this,l=i.events.length,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;l--;){let e=i.events[l][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(l){if(!r||!r._balanced)return n(l);let a=(0,v.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===a.codePointAt(0)&&o.includes(a.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(l),e.exit("gfmFootnoteCallLabelMarker"),t(l)):n(l)}}function em(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let l={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},o={type:"chunkString",contentType:"string",start:Object.assign({},l.start),end:Object.assign({},l.end)},a=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",l,t],["enter",o,t],["exit",o,t],["exit",l,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...a),e}function eg(e,t,n){let r,i=this,o=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),a=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),u};function u(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",s)}function s(u){if(a>999||93===u&&!r||null===u||91===u||(0,l.Ee)(u))return n(u);if(93===u){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return o.includes((0,v.B)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(u),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(u)}return(0,l.Ee)(u)||(r=!0),a++,e.consume(u),92===u?c:s}function c(t){return 91===t||92===t||93===t?(e.consume(t),a++,s):s(t)}}function eb(e,t,n){let r,i,o=this,a=o.parser.gfmFootnotes||(o.parser.gfmFootnotes=[]),u=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),s};function s(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(u>999||93===t&&!i||null===t||91===t||(0,l.Ee)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,v.B)(o.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return(0,l.Ee)(t)||(i=!0),u++,e.consume(t),92===t?f:c}function f(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}function p(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a.includes(r)||a.push(r),(0,ep.N)(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function ey(e,t,n){return e.check(ef.B,t,e.attempt(ed,t,n))}function ek(e){e.exit("gfmFootnoteDefinition")}var ev=n(11287),ex=n(14165),ew=n(4597);class eS{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function eC(e,t,n){let r,i=this,o=0,a=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,l="tableHead"===r||"tableRow"===r?k:u;return l===k&&i.parser.lazy[i.now().line]?n(e):l(e)};function u(t){var n;return e.enter("tableHead"),e.enter("tableRow"),124===(n=t)||(r=!0,a+=1),s(n)}function s(t){return null===t?n(t):(0,l.HP)(t)?a>1?(a=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p):n(t):(0,l.On)(t)?(0,ep.N)(e,s,"whitespace")(t):(a+=1,r&&(r=!1,o+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,s):(e.enter("data"),c(t))}function c(t){return null===t||124===t||(0,l.Ee)(t)?(e.exit("data"),s(t)):(e.consume(t),92===t?f:c)}function f(t){return 92===t||124===t?(e.consume(t),c):c(t)}function p(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,l.On)(t))?(0,ep.N)(e,d,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t)}function d(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),h):n(t)}function h(t){return(0,l.On)(t)?(0,ep.N)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(a+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(a+=1,g(t)):null===t||(0,l.HP)(t)?y(t):n(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),b):(e.exit("tableDelimiterFiller"),b(n))}(t)):n(t)}function b(t){return(0,l.On)(t)?(0,ep.N)(e,y,"whitespace")(t):y(t)}function y(i){if(124===i)return d(i);if(null===i||(0,l.HP)(i))return r&&o===a?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i);return n(i)}function k(t){return e.enter("tableRow"),v(t)}function v(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),v):null===n||(0,l.HP)(n)?(e.exit("tableRow"),t(n)):(0,l.On)(n)?(0,ep.N)(e,v,"whitespace")(n):(e.enter("data"),x(n))}function x(t){return null===t||124===t||(0,l.Ee)(t)?(e.exit("data"),v(t)):(e.consume(t),92===t?w:x)}function w(t){return 92===t||124===t?(e.consume(t),x):x(t)}}function eE(e,t){let n,r,i,l=-1,o=!0,a=0,u=[0,0,0,0],s=[0,0,0,0],c=!1,f=0,p=new eS;for(;++l<e.length;){let d=e[l],h=d[1];"enter"===d[0]?"tableHead"===h.type?(c=!1,0!==f&&(eT(p,t,f,n,r),r=void 0,f=0),n={type:"table",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(l,0,[["enter",n,t]])):"tableRow"===h.type||"tableDelimiterRow"===h.type?(o=!0,i=void 0,u=[0,0,0,0],s=[0,l+1,0,0],c&&(c=!1,r={type:"tableBody",start:Object.assign({},h.start),end:Object.assign({},h.end)},p.add(l,0,[["enter",r,t]])),a="tableDelimiterRow"===h.type?2:r?3:1):a&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)?(o=!1,0===s[2]&&(0!==u[1]&&(s[0]=s[1],i=eD(p,t,u,a,void 0,i),u=[0,0,0,0]),s[2]=l)):"tableCellDivider"===h.type&&(o?o=!1:(0!==u[1]&&(s[0]=s[1],i=eD(p,t,u,a,void 0,i)),s=[(u=s)[1],l,0,0])):"tableHead"===h.type?(c=!0,f=l):"tableRow"===h.type||"tableDelimiterRow"===h.type?(f=l,0!==u[1]?(s[0]=s[1],i=eD(p,t,u,a,l,i)):0!==s[1]&&(i=eD(p,t,s,a,l,i)),a=0):a&&("data"===h.type||"tableDelimiterMarker"===h.type||"tableDelimiterFiller"===h.type)&&(s[3]=l)}for(0!==f&&eT(p,t,f,n,r),p.consume(t.events),l=-1;++l<t.events.length;){let e=t.events[l];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,l))}return e}function eD(e,t,n,r,i,l){0!==n[0]&&(l.end=Object.assign({},eA(t.events,n[0])),e.add(n[0],0,[["exit",l,t]]));let o=eA(t.events,n[1]);if(l={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},o),end:Object.assign({},o)},e.add(n[1],0,[["enter",l,t]]),0!==n[2]){let i=eA(t.events,n[2]),l=eA(t.events,n[3]),o={type:"tableContent",start:Object.assign({},i),end:Object.assign({},l)};if(e.add(n[2],0,[["enter",o,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",o,t]])}return void 0!==i&&(l.end=Object.assign({},eA(t.events,i)),e.add(i,0,[["exit",l,t]]),l=void 0),l}function eT(e,t,n,r,i){let l=[],o=eA(t.events,n);i&&(i.end=Object.assign({},o),l.push(["exit",i,t])),r.end=Object.assign({},o),l.push(["exit",r,t]),e.add(n+1,0,l)}function eA(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let eL={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,l.Ee)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),o):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),o):n(t)}function o(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),a):n(t)}function a(r){return(0,l.HP)(r)?t(r):(0,l.On)(r)?e.check({tokenize:eP},t,n)(r):n(r)}}};function eP(e,t,n){return(0,ep.N)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eq={};function eI(e){let t,n=e||eq,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),l=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),o=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,$.y)([{text:ei},{document:{91:{name:"gfmFootnoteDefinition",tokenize:eb,continuation:{tokenize:ey},exit:ek}},text:{91:{name:"gfmFootnoteCall",tokenize:eg},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:eh,resolveTo:em}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,l=this.events,o=0;return function(a){return 126===i&&"characterEscape"!==l[l.length-1][1].type?r(a):(e.enter("strikethroughSequenceTemporary"),function l(a){let u=(0,ex.S)(i);if(126===a)return o>1?r(a):(e.consume(a),o++,l);if(o<2&&!t)return r(a);let s=e.exit("strikethroughSequenceTemporary"),c=(0,ex.S)(a);return s._open=!c||2===c&&!!u,s._close=!u||2===u&&!!c,n(a)}(a))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},l={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},o=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",l,t]],a=t.parser.constructs.insideSpan.null;a&&(0,ev.m)(o,o.length,0,(0,ew.W)(a,e.slice(r+1,n),t)),(0,ev.m)(o,o.length,0,[["exit",l,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,ev.m)(e,r-1,n-r+3,o),n=r+o.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:eC,resolveAll:eE}}},{text:{91:eL}}])),l.push([{transforms:[g],enter:{literalAutolink:c,literalAutolinkEmail:f,literalAutolinkHttp:f,literalAutolinkWww:f},exit:{literalAutolink:m,literalAutolinkEmail:h,literalAutolinkHttp:p,literalAutolinkWww:d}},{enter:{gfmFootnoteCallString:x,gfmFootnoteCall:w,gfmFootnoteDefinitionLabelString:S,gfmFootnoteDefinition:C},exit:{gfmFootnoteCallString:E,gfmFootnoteCall:D,gfmFootnoteDefinitionLabelString:T,gfmFootnoteDefinition:A}},{canContainEols:["delete"],enter:{strikethrough:O},exit:{strikethrough:N}},{enter:{table:B,tableData:_,tableHeader:_,tableRow:V},exit:{codeText:j,table:H,tableData:U,tableHeader:U,tableRow:U}},{exit:{taskListCheckValueChecked:G,taskListCheckValueUnchecked:G,paragraph:Y}}]),o.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:u,notInConstruct:s},{character:":",before:"[ps]",after:"\\/",inConstruct:u,notInConstruct:s}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let l=r.createTracker(i),o=l.move("[^"),a=r.enter("footnoteDefinition"),u=r.enter("label");return o+=l.move(r.safe(r.associationId(e),{before:o,after:"]"})),u(),o+=l.move("]:"),e.children&&e.children.length>0&&(l.shift(4),o+=l.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,l.current()),t?q:P))),a(),o},footnoteReference:L},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:I}],handlers:{delete:R}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,l=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=M.p.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return a(function(e,t,n){let r=e.children,i=-1,l=[],o=t.enter("table");for(;++i<r.length;)l[i]=u(r[i],t,n);return o(),l}(e,n,r),e.align)},tableCell:o,tableRow:function(e,t,n,r){let i=a([u(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function o(e,t,n,r){let i=n.enter("tableCell"),o=n.enter("phrasing"),a=n.containerPhrasing(e,{...r,before:l,after:l});return o(),i(),a}function a(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||z,l=[],o=[],a=[],u=[],s=0,c=-1;for(;++c<e.length;){let t=[],r=[],l=-1;for(e[c].length>s&&(s=e[c].length);++l<e[c].length;){var f;let o=null==(f=e[c][l])?"":String(f);if(!1!==n.alignDelimiters){let e=i(o);r[l]=e,(void 0===u[l]||e>u[l])&&(u[l]=e)}t.push(o)}o[c]=t,a[c]=r}let p=-1;if("object"==typeof r&&"length"in r)for(;++p<s;)l[p]=F(r[p]);else{let e=F(r);for(;++p<s;)l[p]=e}p=-1;let d=[],h=[];for(;++p<s;){let e=l[p],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,u[p]-t.length-r.length),o=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>u[p]&&(u[p]=i),h[p]=i),d[p]=o}o.splice(1,0,d),a.splice(1,0,h),c=-1;let m=[];for(;++c<o.length;){let e=o[c],t=a[c];p=-1;let r=[];for(;++p<s;){let i=e[p]||"",o="",a="";if(!1!==n.alignDelimiters){let e=u[p]-(t[p]||0),n=l[p];114===n?o=" ".repeat(e):99===n?e%2?(o=" ".repeat(e/2+.5),a=" ".repeat(e/2-.5)):a=o=" ".repeat(e/2):a=" ".repeat(e)}!1===n.delimiterStart||p||r.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==i)&&(!1!==n.delimiterStart||p)&&r.push(" "),!1!==n.alignDelimiters&&r.push(o),r.push(i),!1!==n.alignDelimiters&&r.push(a),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||p!==s-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function u(e,t,n){let r=e.children,i=-1,l=[],a=t.enter("tableRow");for(;++i<r.length;)l[i]=o(r[i],e,t,n);return a(),l}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:Q}}]})}},70785:(e,t,n)=>{n.d(t,{s:()=>l});let r={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},i={}.hasOwnProperty;function l(e){return!!i.call(r,e)&&r[e]}},71164:(e,t,n)=>{n.d(t,{BM:()=>a,CW:()=>r,Ee:()=>f,HP:()=>c,JQ:()=>o,Ny:()=>h,On:()=>p,cx:()=>l,es:()=>d,lV:()=>i,ok:()=>u,ol:()=>s});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),l=m(/[#-'*+\--9=?A-Z^-~]/);function o(e){return null!==e&&(e<32||127===e)}let a=m(/\d/),u=m(/[\dA-Fa-f]/),s=m(/[!-/:-@[-`{-~]/);function c(e){return null!==e&&e<-2}function f(e){return null!==e&&(e<0||32===e)}function p(e){return -2===e||-1===e||32===e}let d=m(/\p{P}|\p{S}/u),h=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},82629:(e,t,n)=>{n.d(t,{T:()=>r});function r(e){return"&#x"+e.toString(16).toUpperCase()+";"}},85303:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(71164);function i(e,t,n,i){let l=i?i-1:Number.POSITIVE_INFINITY,o=0;return function(i){return(0,r.On)(i)?(e.enter(n),function i(a){return(0,r.On)(a)&&o++<l?(e.consume(a),i):(e.exit(n),t(a))}(i)):t(i)}}},87833:(e,t,n)=>{n.d(t,{B:()=>l});var r=n(85303),i=n(71164);let l={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.On)(t)?(0,r.N)(e,l,"linePrefix")(t):l(t)};function l(e){return null===e||(0,i.HP)(e)?t(e):n(e)}}}},89011:(e,t,n)=>{n.d(t,{C:()=>r});function r(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}},89203:(e,t,n)=>{n.d(t,{A:()=>ex});var r={};n.r(r),n.d(r,{attentionMarkers:()=>ep,contentInitial:()=>eo,disable:()=>ed,document:()=>el,flow:()=>eu,flowInitial:()=>ea,insideSpan:()=>ef,string:()=>es,text:()=>ec});var i=n(53728),l=n(11287);class o{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&a(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),a(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),a(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);a(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);a(this.left,t.reverse())}}}function a(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function u(e){let t,n,r,i,a,u,s,c={},f=-1,p=new o(e);for(;++f<p.length;){for(;f in c;)f=c[f];if(t=p.get(f),f&&"chunkFlow"===t[1].type&&"listItemPrefix"===p.get(f-1)[1].type&&((r=0)<(u=t[1]._tokenizer.events).length&&"lineEndingBlank"===u[r][1].type&&(r+=2),r<u.length&&"content"===u[r][1].type))for(;++r<u.length&&"content"!==u[r][1].type;)"chunkText"===u[r][1].type&&(u[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(c,function(e,t){let n,r,i=e.get(t)[1],l=e.get(t)[2],o=t-1,a=[],u=i._tokenizer;!u&&(u=l.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(u._contentTypeTextTrailing=!0));let s=u.events,c=[],f={},p=-1,d=i,h=0,m=0,g=[0];for(;d;){for(;e.get(++o)[1]!==d;);a.push(o),!d._tokenizer&&(n=l.sliceStream(d),d.next||n.push(null),r&&u.defineSkip(d.start),d._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=!0),u.write(n),d._isInFirstContentOfListItem&&(u._gfmTasklistFirstContentOfListItem=void 0)),r=d,d=d.next}for(d=i;++p<s.length;)"exit"===s[p][0]&&"enter"===s[p-1][0]&&s[p][1].type===s[p-1][1].type&&s[p][1].start.line!==s[p][1].end.line&&(m=p+1,g.push(m),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(u.events=[],d?(d._tokenizer=void 0,d.previous=void 0):g.pop(),p=g.length;p--;){let t=s.slice(g[p],g[p+1]),n=a.pop();c.push([n,n+t.length-1]),e.splice(n,2,t)}for(c.reverse(),p=-1;++p<c.length;)f[h+c[p][0]]=h+c[p][1],h+=c[p][1]-c[p][0]-1;return f}(p,f)),f=c[f],s=!0);else if(t[1]._container){for(r=f,n=void 0;r--;)if("lineEnding"===(i=p.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(p.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...p.get(n)[1].start},(a=p.slice(n,f)).unshift(t),p.splice(n,f-n+1,a))}}return(0,l.m)(e,0,Number.POSITIVE_INFINITY,p.slice(0)),!s}var s=n(6815),c=n(85303),f=n(71164);let p={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,f.HP)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},d={tokenize:function(e){let t,n,r,i=this,o=[],a=0;return u;function u(t){if(a<o.length){let n=o[a];return i.containerState=n[1],e.attempt(n[0].continuation,s,c)(t)}return c(t)}function s(e){if(a++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&v();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}k(a);let u=r;for(;u<i.events.length;)i.events[u][1].end={...n},u++;return(0,l.m)(i.events,o+1,0,i.events.slice(r)),i.events.length=u,c(e)}return u(e)}function c(n){if(a===o.length){if(!t)return m(n);if(t.currentConstruct&&t.currentConstruct.concrete)return b(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(h,p,d)(n)}function p(e){return t&&v(),k(a),m(e)}function d(e){return i.parser.lazy[i.now().line]=a!==o.length,r=i.now().offset,b(e)}function m(t){return i.containerState={},e.attempt(h,g,b)(t)}function g(e){return a++,o.push([i.currentConstruct,i.containerState]),m(e)}function b(r){if(null===r){t&&v(),k(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){y(e.exit("chunkFlow"),!0),k(0),e.consume(n);return}return(0,f.HP)(n)?(e.consume(n),y(e.exit("chunkFlow")),a=0,i.interrupt=void 0,u):(e.consume(n),t)}(r)}function y(e,o){let u=i.sliceStream(e);if(o&&u.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(u),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let u=i.events.length,s=u;for(;s--;)if("exit"===i.events[s][0]&&"chunkFlow"===i.events[s][1].type){if(e){n=i.events[s][1].end;break}e=!0}for(k(a),o=u;o<i.events.length;)i.events[o][1].end={...n},o++;(0,l.m)(i.events,s+1,0,i.events.slice(u)),i.events.length=o}}function k(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function v(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},h={tokenize:function(e,t,n){return(0,c.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var m=n(87833);let g={resolve:function(e){return u(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,f.HP)(t)?e.check(b,l,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function l(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},b={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,f.HP)(i))return n(i);let l=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},y={tokenize:function(e){let t=this,n=e.attempt(m.B,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,(0,c.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(g,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},k={resolveAll:S()},v=w("string"),x=w("text");function w(e){return{resolveAll:S("text"===e?C:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,l,o);return l;function l(e){return u(e)?i(e):o(e)}function o(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),a)}function a(e){return u(e)?(t.exit("data"),i(e)):(t.consume(e),a)}function u(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function S(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function C(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],l=t.sliceStream(i),o=l.length,a=-1,u=0;for(;o--;){let e=l[o];if("string"==typeof e){for(a=e.length;32===e.charCodeAt(a-1);)u++,a--;if(a)break;a=-1}else if(-2===e)r=!0,u++;else if(-1===e);else{o++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(u=0),u){let l={type:n===e.length||r||u<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:o?a:i.start._bufferIndex+a,_index:i.start._index+o,line:i.end.line,column:i.end.column-u,offset:i.end.offset-u},end:{...i.end}};i.end={...l.start},i.start.offset===i.end.offset?Object.assign(i,l):(e.splice(n,0,["enter",l,t],["exit",l,t]),n+=2)}n++}return e}let E={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(l){var o;return e.enter("thematicBreak"),r=o=l,function l(o){return o===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,f.On)(n)?(0,c.N)(e,l,"whitespace")(n):l(n))}(o)):i>=3&&(null===o||(0,f.HP)(o))?(e.exit("thematicBreak"),t(o)):n(o)}(o)}}},D={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(m.B,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,c.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,f.On)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(A,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,c.N)(e,e.attempt(D,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],l=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,o=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,f.BM)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(E,n,a)(t):a(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,f.BM)(i)&&++o<10?(e.consume(i),t):(!r.interrupt||o<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),a(i)):n(i)}(t)}return n(t)};function a(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(m.B,r.interrupt?n:u,e.attempt(T,c,s))}function u(e){return r.containerState.initialBlankLine=!0,l++,c(e)}function s(t){return(0,f.On)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),c):n(t)}function c(n){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},T={partial:!0,tokenize:function(e,t,n){let r=this;return(0,c.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,f.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},A={partial:!0,tokenize:function(e,t,n){let r=this;return(0,c.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},L={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,f.On)(t)?(0,c.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(L,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,f.On)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function P(e,t,n,r,i,l,o,a,u){let s=u||Number.POSITIVE_INFINITY,c=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(l),e.consume(t),e.exit(l),p):null===t||32===t||41===t||(0,f.JQ)(t)?n(t):(e.enter(r),e.enter(o),e.enter(a),e.enter("chunkString",{contentType:"string"}),m(t))};function p(n){return 62===n?(e.enter(l),e.consume(n),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(a),p(t)):null===t||60===t||(0,f.HP)(t)?n(t):(e.consume(t),92===t?h:d)}function h(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function m(i){return!c&&(null===i||41===i||(0,f.Ee)(i))?(e.exit("chunkString"),e.exit(a),e.exit(o),e.exit(r),t(i)):c<s&&40===i?(e.consume(i),c++,m):41===i?(e.consume(i),c--,m):null===i||32===i||40===i||(0,f.JQ)(i)?n(i):(e.consume(i),92===i?g:m)}function g(t){return 40===t||41===t||92===t?(e.consume(t),m):m(t)}}function q(e,t,n,r,i,l){let o,a=this,u=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(l),s};function s(p){return u>999||null===p||91===p||93===p&&!o||94===p&&!u&&"_hiddenFootnoteSupport"in a.parser.constructs?n(p):93===p?(e.exit(l),e.enter(i),e.consume(p),e.exit(i),e.exit(r),t):(0,f.HP)(p)?(e.enter("lineEnding"),e.consume(p),e.exit("lineEnding"),s):(e.enter("chunkString",{contentType:"string"}),c(p))}function c(t){return null===t||91===t||93===t||(0,f.HP)(t)||u++>999?(e.exit("chunkString"),s(t)):(e.consume(t),o||(o=!(0,f.On)(t)),92===t?p:c)}function p(t){return 91===t||92===t||93===t?(e.consume(t),u++,c):c(t)}}function I(e,t,n,r,i,l){let o;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),o=40===t?41:t,a):n(t)};function a(n){return n===o?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(l),u(n))}function u(t){return t===o?(e.exit(l),a(o)):null===t?n(t):(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,c.N)(e,u,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),s(t))}function s(t){return t===o||null===t||(0,f.HP)(t)?(e.exit("chunkString"),u(t)):(e.consume(t),92===t?p:s)}function p(t){return t===o||92===t?(e.consume(t),s):s(t)}}function O(e,t){let n;return function r(i){return(0,f.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,f.On)(i)?(0,c.N)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var N=n(90148);let R={partial:!0,tokenize:function(e,t,n){return function(t){return(0,f.Ee)(t)?O(e,r)(t):n(t)};function r(t){return I(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,f.On)(t)?(0,c.N)(e,l,"whitespace")(t):l(t)}function l(e){return null===e||(0,f.HP)(e)?t(e):n(e)}}},z={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,c.N)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?l(n):(0,f.HP)(n)?e.attempt(F,t,l)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,f.HP)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function l(n){return e.exit("codeIndented"),t(n)}}},F={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,c.N)(e,l,"linePrefix",5)(t)}function l(e){let l=r.events[r.events.length-1];return l&&"linePrefix"===l[1].type&&l[2].sliceSerialize(l[1],!0).length>=4?t(e):(0,f.HP)(e)?i(e):n(e)}}},M={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,l=e.length;for(;l--;)if("enter"===e[l][0]){if("content"===e[l][1].type){n=l;break}"paragraph"===e[l][1].type&&(r=l)}else"content"===e[l][1].type&&e.splice(l,1),i||"definition"!==e[l][1].type||(i=l);let o={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",o,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=o,e.push(["exit",o,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var o;let a,u=i.events.length;for(;u--;)if("lineEnding"!==i.events[u][1].type&&"linePrefix"!==i.events[u][1].type&&"content"!==i.events[u][1].type){a="paragraph"===i.events[u][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||a)?(e.enter("setextHeadingLine"),r=t,o=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,f.On)(n)?(0,c.N)(e,l,"lineSuffix")(n):l(n))}(o)):n(t)};function l(r){return null===r||(0,f.HP)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},B=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],H=["pre","script","style","textarea"],V={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(m.B,t,n)}}},U={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,f.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},_={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},j={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,l={partial:!0,tokenize:function(e,t,n){let l=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),o};function o(t){return e.enter("codeFencedFence"),(0,f.On)(t)?(0,c.N)(e,u,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):u(t)}function u(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(l++,e.consume(i),t):l>=a?(e.exit("codeFencedFenceSequence"),(0,f.On)(i)?(0,c.N)(e,s,"whitespace")(i):s(i)):n(i)}(t)):n(t)}function s(r){return null===r||(0,f.HP)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},o=0,a=0;return function(t){var l=t;let s=i.events[i.events.length-1];return o=s&&"linePrefix"===s[1].type?s[2].sliceSerialize(s[1],!0).length:0,r=l,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(a++,e.consume(i),t):a<3?n(i):(e.exit("codeFencedFenceSequence"),(0,f.On)(i)?(0,c.N)(e,u,"whitespace")(i):u(i))}(l)};function u(l){return null===l||(0,f.HP)(l)?(e.exit("codeFencedFence"),i.interrupt?t(l):e.check(_,p,g)(l)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,f.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),u(i)):(0,f.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,c.N)(e,s,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(l))}function s(t){return null===t||(0,f.HP)(t)?u(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,f.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),u(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function p(t){return e.attempt(l,g,d)(t)}function d(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),h}function h(t){return o>0&&(0,f.On)(t)?(0,c.N)(e,m,"linePrefix",o+1)(t):m(t)}function m(t){return null===t||(0,f.HP)(t)?e.check(_,p,g)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,f.HP)(n)?(e.exit("codeFlowValue"),m(n)):(e.consume(n),t)}(t))}function g(n){return e.exit("codeFenced"),t(n)}}};var W=n(70785);let G={name:"characterReference",tokenize:function(e,t,n){let r,i,l=this,o=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),a};function a(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),u):(e.enter("characterReferenceValue"),r=31,i=f.lV,s(t))}function u(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=f.ok,s):(e.enter("characterReferenceValue"),r=7,i=f.BM,s(t))}function s(a){if(59===a&&o){let r=e.exit("characterReferenceValue");return i!==f.lV||(0,W.s)(l.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(a),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(a)}return i(a)&&o++<r?(e.consume(a),s):n(a)}}},Y={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,f.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},Q={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,c.N)(e,t,"linePrefix")}}};var $=n(4597);let J={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,l.m)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,o,a=e.length,u=0;for(;a--;)if(n=e[a][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[a][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[a][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=a,"labelLink"!==n.type)){u=2;break}}else"labelEnd"===n.type&&(i=a);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},f={type:"labelText",start:{...e[r+u+2][1].end},end:{...e[i-2][1].start}};return o=[["enter",s,t],["enter",c,t]],o=(0,l.V)(o,e.slice(r+1,r+u+3)),o=(0,l.V)(o,[["enter",f,t]]),o=(0,l.V)(o,(0,$.W)(t.parser.constructs.insideSpan.null,e.slice(r+u+4,i-3),t)),o=(0,l.V)(o,[["exit",f,t],e[i-2],e[i-1],["exit",c,t]]),o=(0,l.V)(o,e.slice(i+1)),o=(0,l.V)(o,[["exit",s,t]]),(0,l.m)(e,r,e.length,o),e},tokenize:function(e,t,n){let r,i,l=this,o=l.events.length;for(;o--;)if(("labelImage"===l.events[o][1].type||"labelLink"===l.events[o][1].type)&&!l.events[o][1]._balanced){r=l.events[o][1];break}return function(t){return r?r._inactive?c(t):(i=l.parser.defined.includes((0,N.B)(l.sliceSerialize({start:r.end,end:l.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),a):n(t)};function a(t){return 40===t?e.attempt(Z,s,i?s:c)(t):91===t?e.attempt(K,s,i?u:c)(t):i?s(t):c(t)}function u(t){return e.attempt(X,s,c)(t)}function s(e){return t(e)}function c(e){return r._balanced=!0,n(e)}}},Z={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,f.Ee)(t)?O(e,i)(t):i(t)}function i(t){return 41===t?s(t):P(e,l,o,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function l(t){return(0,f.Ee)(t)?O(e,a)(t):s(t)}function o(e){return n(e)}function a(t){return 34===t||39===t||40===t?I(e,u,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):s(t)}function u(t){return(0,f.Ee)(t)?O(e,s)(t):s(t)}function s(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},K={tokenize:function(e,t,n){let r=this;return function(t){return q.call(r,e,i,l,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,N.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function l(e){return n(e)}}},X={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},ee={name:"labelStartImage",resolveAll:J.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),l):n(t)}function l(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var et=n(14165);let en={name:"attention",resolveAll:function(e,t){let n,r,i,o,a,u,s,c,f=-1;for(;++f<e.length;)if("enter"===e[f][0]&&"attentionSequence"===e[f][1].type&&e[f][1]._close){for(n=f;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[f][1]).charCodeAt(0)){if((e[n][1]._close||e[f][1]._open)&&(e[f][1].end.offset-e[f][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[f][1].end.offset-e[f][1].start.offset)%3))continue;u=e[n][1].end.offset-e[n][1].start.offset>1&&e[f][1].end.offset-e[f][1].start.offset>1?2:1;let p={...e[n][1].end},d={...e[f][1].start};er(p,-u),er(d,u),o={type:u>1?"strongSequence":"emphasisSequence",start:p,end:{...e[n][1].end}},a={type:u>1?"strongSequence":"emphasisSequence",start:{...e[f][1].start},end:d},i={type:u>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[f][1].start}},r={type:u>1?"strong":"emphasis",start:{...o.start},end:{...a.end}},e[n][1].end={...o.start},e[f][1].start={...a.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=(0,l.V)(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=(0,l.V)(s,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),s=(0,l.V)(s,(0,$.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,f),t)),s=(0,l.V)(s,[["exit",i,t],["enter",a,t],["exit",a,t],["exit",r,t]]),e[f][1].end.offset-e[f][1].start.offset?(c=2,s=(0,l.V)(s,[["enter",e[f][1],t],["exit",e[f][1],t]])):c=0,(0,l.m)(e,n-1,f-n+3,s),f=n+s.length-c-2;break}}for(f=-1;++f<e.length;)"attentionSequence"===e[f][1].type&&(e[f][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,l=(0,et.S)(i);return function(o){return n=o,e.enter("attentionSequence"),function o(a){if(a===n)return e.consume(a),o;let u=e.exit("attentionSequence"),s=(0,et.S)(a),c=!s||2===s&&l||r.includes(a),f=!l||2===l&&s||r.includes(i);return u._open=!!(42===n?c:c&&(l||!f)),u._close=!!(42===n?f:f&&(s||!c)),t(a)}(o)}}};function er(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let ei={name:"labelStartLink",resolveAll:J.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},el={42:D,43:D,45:D,48:D,49:D,50:D,51:D,52:D,53:D,54:D,55:D,56:D,57:D,62:L},eo={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,q.call(i,e,l,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function l(t){return(r=(0,N.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),o):n(t)}function o(t){return(0,f.Ee)(t)?O(e,a)(t):a(t)}function a(t){return P(e,u,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function u(t){return e.attempt(R,s,s)(t)}function s(t){return(0,f.On)(t)?(0,c.N)(e,p,"whitespace")(t):p(t)}function p(l){return null===l||(0,f.HP)(l)?(e.exit("definition"),i.parser.defined.push(r),t(l)):n(l)}}}},ea={[-2]:z,[-1]:z,32:z},eu={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},(0,l.m)(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var l;return e.enter("atxHeading"),l=i,e.enter("atxHeadingSequence"),function i(l){return 35===l&&r++<6?(e.consume(l),i):null===l||(0,f.Ee)(l)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,f.HP)(r)?(e.exit("atxHeading"),t(r)):(0,f.On)(r)?(0,c.N)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,f.Ee)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(l)):n(l)}(l)}}},42:E,45:[M,E],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,l,o,a,u=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),s};function s(o){return 33===o?(e.consume(o),c):47===o?(e.consume(o),i=!0,h):63===o?(e.consume(o),r=3,u.interrupt?t:O):(0,f.CW)(o)?(e.consume(o),l=String.fromCharCode(o),m):n(o)}function c(i){return 45===i?(e.consume(i),r=2,p):91===i?(e.consume(i),r=5,o=0,d):(0,f.CW)(i)?(e.consume(i),r=4,u.interrupt?t:O):n(i)}function p(r){return 45===r?(e.consume(r),u.interrupt?t:O):n(r)}function d(r){let i="CDATA[";return r===i.charCodeAt(o++)?(e.consume(r),o===i.length)?u.interrupt?t:E:d:n(r)}function h(t){return(0,f.CW)(t)?(e.consume(t),l=String.fromCharCode(t),m):n(t)}function m(o){if(null===o||47===o||62===o||(0,f.Ee)(o)){let a=47===o,s=l.toLowerCase();return!a&&!i&&H.includes(s)?(r=1,u.interrupt?t(o):E(o)):B.includes(l.toLowerCase())?(r=6,a)?(e.consume(o),g):u.interrupt?t(o):E(o):(r=7,u.interrupt&&!u.parser.lazy[u.now().line]?n(o):i?function t(n){return(0,f.On)(n)?(e.consume(n),t):S(n)}(o):b(o))}return 45===o||(0,f.lV)(o)?(e.consume(o),l+=String.fromCharCode(o),m):n(o)}function g(r){return 62===r?(e.consume(r),u.interrupt?t:E):n(r)}function b(t){return 47===t?(e.consume(t),S):58===t||95===t||(0,f.CW)(t)?(e.consume(t),y):(0,f.On)(t)?(e.consume(t),b):S(t)}function y(t){return 45===t||46===t||58===t||95===t||(0,f.lV)(t)?(e.consume(t),y):k(t)}function k(t){return 61===t?(e.consume(t),v):(0,f.On)(t)?(e.consume(t),k):b(t)}function v(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),a=t,x):(0,f.On)(t)?(e.consume(t),v):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,f.Ee)(n)?k(n):(e.consume(n),t)}(t)}function x(t){return t===a?(e.consume(t),a=null,w):null===t||(0,f.HP)(t)?n(t):(e.consume(t),x)}function w(e){return 47===e||62===e||(0,f.On)(e)?b(e):n(e)}function S(t){return 62===t?(e.consume(t),C):n(t)}function C(t){return null===t||(0,f.HP)(t)?E(t):(0,f.On)(t)?(e.consume(t),C):n(t)}function E(t){return 45===t&&2===r?(e.consume(t),L):60===t&&1===r?(e.consume(t),P):62===t&&4===r?(e.consume(t),N):63===t&&3===r?(e.consume(t),O):93===t&&5===r?(e.consume(t),I):(0,f.HP)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(V,R,D)(t)):null===t||(0,f.HP)(t)?(e.exit("htmlFlowData"),D(t)):(e.consume(t),E)}function D(t){return e.check(U,T,R)(t)}function T(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),A}function A(t){return null===t||(0,f.HP)(t)?D(t):(e.enter("htmlFlowData"),E(t))}function L(t){return 45===t?(e.consume(t),O):E(t)}function P(t){return 47===t?(e.consume(t),l="",q):E(t)}function q(t){if(62===t){let n=l.toLowerCase();return H.includes(n)?(e.consume(t),N):E(t)}return(0,f.CW)(t)&&l.length<8?(e.consume(t),l+=String.fromCharCode(t),q):E(t)}function I(t){return 93===t?(e.consume(t),O):E(t)}function O(t){return 62===t?(e.consume(t),N):45===t&&2===r?(e.consume(t),O):E(t)}function N(t){return null===t||(0,f.HP)(t)?(e.exit("htmlFlowData"),R(t)):(e.consume(t),N)}function R(n){return e.exit("htmlFlow"),t(n)}}},61:M,95:E,96:j,126:j},es={38:G,92:Y},ec={[-5]:Q,[-4]:Q,[-3]:Q,33:ee,38:G,42:en,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,f.CW)(t)?(e.consume(t),l):64===t?n(t):a(t)}function l(t){return 43===t||45===t||46===t||(0,f.lV)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,o):(43===n||45===n||46===n||(0,f.lV)(n))&&r++<32?(e.consume(n),t):(r=0,a(n))}(t)):a(t)}function o(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,f.JQ)(r)?n(r):(e.consume(r),o)}function a(t){return 64===t?(e.consume(t),u):(0,f.cx)(t)?(e.consume(t),a):n(t)}function u(i){return(0,f.lV)(i)?function i(l){return 46===l?(e.consume(l),r=0,u):62===l?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(l),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(l){if((45===l||(0,f.lV)(l))&&r++<63){let n=45===l?t:i;return e.consume(l),n}return n(l)}(l)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,l,o=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),a};function a(t){return 33===t?(e.consume(t),u):47===t?(e.consume(t),w):63===t?(e.consume(t),v):(0,f.CW)(t)?(e.consume(t),C):n(t)}function u(t){return 45===t?(e.consume(t),s):91===t?(e.consume(t),i=0,m):(0,f.CW)(t)?(e.consume(t),k):n(t)}function s(t){return 45===t?(e.consume(t),h):n(t)}function p(t){return null===t?n(t):45===t?(e.consume(t),d):(0,f.HP)(t)?(l=p,I(t)):(e.consume(t),p)}function d(t){return 45===t?(e.consume(t),h):p(t)}function h(e){return 62===e?q(e):45===e?d(e):p(e)}function m(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?g:m):n(t)}function g(t){return null===t?n(t):93===t?(e.consume(t),b):(0,f.HP)(t)?(l=g,I(t)):(e.consume(t),g)}function b(t){return 93===t?(e.consume(t),y):g(t)}function y(t){return 62===t?q(t):93===t?(e.consume(t),y):g(t)}function k(t){return null===t||62===t?q(t):(0,f.HP)(t)?(l=k,I(t)):(e.consume(t),k)}function v(t){return null===t?n(t):63===t?(e.consume(t),x):(0,f.HP)(t)?(l=v,I(t)):(e.consume(t),v)}function x(e){return 62===e?q(e):v(e)}function w(t){return(0,f.CW)(t)?(e.consume(t),S):n(t)}function S(t){return 45===t||(0,f.lV)(t)?(e.consume(t),S):function t(n){return(0,f.HP)(n)?(l=t,I(n)):(0,f.On)(n)?(e.consume(n),t):q(n)}(t)}function C(t){return 45===t||(0,f.lV)(t)?(e.consume(t),C):47===t||62===t||(0,f.Ee)(t)?E(t):n(t)}function E(t){return 47===t?(e.consume(t),q):58===t||95===t||(0,f.CW)(t)?(e.consume(t),D):(0,f.HP)(t)?(l=E,I(t)):(0,f.On)(t)?(e.consume(t),E):q(t)}function D(t){return 45===t||46===t||58===t||95===t||(0,f.lV)(t)?(e.consume(t),D):function t(n){return 61===n?(e.consume(n),T):(0,f.HP)(n)?(l=t,I(n)):(0,f.On)(n)?(e.consume(n),t):E(n)}(t)}function T(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,A):(0,f.HP)(t)?(l=T,I(t)):(0,f.On)(t)?(e.consume(t),T):(e.consume(t),L)}function A(t){return t===r?(e.consume(t),r=void 0,P):null===t?n(t):(0,f.HP)(t)?(l=A,I(t)):(e.consume(t),A)}function L(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,f.Ee)(t)?E(t):(e.consume(t),L)}function P(e){return 47===e||62===e||(0,f.Ee)(e)?E(e):n(e)}function q(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function I(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),O}function O(t){return(0,f.On)(t)?(0,c.N)(e,N,"linePrefix",o.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):N(t)}function N(t){return e.enter("htmlTextData"),l(t)}}}],91:ei,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,f.HP)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},Y],93:J,95:en,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,l=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),l++,t):(e.exit("codeTextSequence"),o(n))}(t)};function o(u){return null===u?n(u):32===u?(e.enter("space"),e.consume(u),e.exit("space"),o):96===u?(i=e.enter("codeTextSequence"),r=0,function n(o){return 96===o?(e.consume(o),r++,n):r===l?(e.exit("codeTextSequence"),e.exit("codeText"),t(o)):(i.type="codeTextData",a(o))}(u)):(0,f.HP)(u)?(e.enter("lineEnding"),e.consume(u),e.exit("lineEnding"),o):(e.enter("codeTextData"),a(u))}function a(t){return null===t||32===t||96===t||(0,f.HP)(t)?(e.exit("codeTextData"),o(t)):(e.consume(t),a)}}}},ef={null:[en,k]},ep={null:[42,95]},ed={null:[]},eh=/[\0\t\n\r]/g;var em=n(89011),eg=n(38251),eb=n(42626);let ey={}.hasOwnProperty;function ek(e){return{line:e.line,column:e.column,offset:e.offset}}function ev(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+(0,eb.L)({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+(0,eb.L)({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+(0,eb.L)({start:t.start,end:t.end})+") is still open")}function ex(e){let t=this;t.parser=function(n){var o,a;let c,h,m,g;return"string"!=typeof(o={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(a=o,o=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:c,autolinkEmail:c,atxHeading:r(m),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:c,characterReference:c,codeFenced:r(h),codeFencedFenceInfo:l,codeFencedFenceMeta:l,codeIndented:r(h,l),codeText:r(function(){return{type:"inlineCode",value:""}},l),codeTextData:c,data:c,codeFlowValue:c,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:l,definitionLabelString:l,definitionTitleString:l,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(g),hardBreakTrailing:r(g),htmlFlow:r(b,l),htmlFlowData:c,htmlText:r(b,l),htmlTextData:c,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:l,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(k,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(k),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:l,resourceDestinationString:l,resourceTitleString:l,setextHeading:r(m),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:a(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:a(),autolinkEmail:function(e){f.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){f.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:a(),characterEscapeValue:f,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=(0,em.C)(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=(0,W.s)(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=ek(e.end)},codeFenced:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:f,codeIndented:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:f,data:f,definition:a(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,N.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:a(),hardBreakEscape:a(p),hardBreakTrailing:a(p),htmlFlow:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:f,htmlText:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:f,image:a(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=(0,eg.s)(t),n.identifier=(0,N.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=ek(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),f.call(this,e))},link:a(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,N.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:a(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:a(),thematicBreak:a()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(ey.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},i={stack:[r],tokenStack:[],config:t,enter:o,exit:u,buffer:l,resume:s,data:n},a=[],c=-1;for(;++c<e.length;)("listOrdered"===e[c][1].type||"listUnordered"===e[c][1].type)&&("enter"===e[c][0]?a.push(c):c=function(e,t,n){let r,i,l,o,a=t-1,u=-1,s=!1;for(;++a<=n;){let t=e[a];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?u++:u--,o=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||o||u||l||(l=a),o=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:o=void 0}if(!u&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===u&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let o=a;for(i=void 0;o--;){let t=e[o];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",s=!0),t[1].type="lineEnding",i=o}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}l&&(!i||l<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||a,0,["exit",r,t[2]]),a++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(a,0,["enter",i,t[2]]),a++,n++,l=void 0,o=!0}}}return e[t][1]._spread=s,n}(e,a.pop(),c));for(c=-1;++c<e.length;){let n=t[e[c][0]];ey.call(n,e[c][1].type)&&n[e[c][1].type].call(Object.assign({sliceSerialize:e[c][2].sliceSerialize},i),e[c][1])}if(i.tokenStack.length>0){let e=i.tokenStack[i.tokenStack.length-1];(e[1]||ev).call(i,void 0,e[0])}for(r.position={start:ek(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:ek(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},c=-1;++c<t.transforms.length;)r=t.transforms[c](r)||r;return r};function r(e,t){return function(n){o.call(this,e(n),n),t&&t.call(this,n)}}function l(){this.stack.push({type:"fragment",children:[]})}function o(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:ek(t.start),end:void 0}}function a(e){return function(t){e&&e.call(this,t),u.call(this,t)}}function u(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||ev).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+(0,eb.L)({start:e.start,end:e.end})+"): it’s not open");n.position.end=ek(e.end)}function s(){return(0,i.d)(this.stack.pop())}function c(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:ek(e.start),end:void 0},t.push(n)),this.stack.push(n)}function f(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=ek(e.end)}function p(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function h(){return{type:"code",lang:null,meta:null,value:""}}function m(){return{type:"heading",depth:0,children:[]}}function g(){return{type:"break"}}function b(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function k(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(a)(function(e){for(;!u(e););return e}((function(e){let t={constructs:(0,s.y)([r,...(e||{}).extensions||[]]),content:n(p),defined:[],document:n(d),flow:n(y),lazy:{},string:n(v),text:n(x)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},o=[],a=[],u=[],s={attempt:g(function(e,t){b(e,t.from)}),check:g(m),consume:function(e){(0,f.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,y()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=h(),c.events.push(["enter",n,c]),u.push(n),n},exit:function(e){let t=u.pop();return t.end=h(),c.events.push(["exit",t,c]),t},interrupt:g(m,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,y()},events:[],now:h,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let l,o=e[r];if("string"==typeof o)l=o;else switch(o){case -5:l="\r";break;case -4:l="\n";break;case -3:l="\r\n";break;case -2:l=t?" ":"	";break;case -1:if(!t&&n)continue;l=" ";break;default:l=String.fromCharCode(o)}n=-2===o,i.push(l)}return i.join("")}(d(e),t)},sliceStream:d,write:function(e){return(a=(0,l.V)(a,e),function(){let e;for(;r._index<a.length;){let n=a[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),p=p(t)}else p=p(n)}}(),null!==a[a.length-1])?[]:(b(t,0),c.events=(0,$.W)(o,c.events,c),c.events)}},p=t.tokenize.call(c,s);return t.resolveAll&&o.push(t),c;function d(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,l=t.end._index,o=t.end._bufferIndex;if(r===l)n=[e[r].slice(i,o)];else{if(n=e.slice(r,l),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}o>0&&n.push(e[l].slice(0,o))}return n}(a,e)}function h(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:l}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:l}}function m(e,t){t.restore()}function g(e,t){return function(n,i,l){var o;let a,f,p,d;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(o=n,function(e){let t=null!==e&&o[e],n=null!==e&&o.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(a=e,f=0,0===e.length)?l:g(e[f])}function g(e){return function(n){return(d=function(){let e=h(),t=c.previous,n=c.currentConstruct,i=c.events.length,l=Array.from(u);return{from:i,restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,u=l,y()}}}(),p=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?k(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,s,b,k)(n)}}function b(t){return e(p,d),i}function k(e){return(d.restore(),++f<a.length)?g(a[f]):l}}}function b(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&(0,l.m)(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function y(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(a).document().write((h=1,m="",g=!0,function(e,t,n){let r,i,l,o,a,u=[];for(e=m+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),l=0,m="",g&&(65279===e.charCodeAt(0)&&l++,g=void 0);l<e.length;){if(eh.lastIndex=l,o=(r=eh.exec(e))&&void 0!==r.index?r.index:e.length,a=e.charCodeAt(o),!r){m=e.slice(l);break}if(10===a&&l===o&&c)u.push(-3),c=void 0;else switch(c&&(u.push(-5),c=void 0),l<o&&(u.push(e.slice(l,o)),h+=o-l),a){case 0:u.push(65533),h++;break;case 9:for(i=4*Math.ceil(h/4),u.push(-2);h++<i;)u.push(-1);break;case 10:u.push(-4),h=1;break;default:c=!0,h=1}l=o+1}return n&&(c&&u.push(-5),m&&u.push(m),u.push(null)),u})(n,o,!0))))}}},89316:(e,t,n)=>{n.d(t,{T:()=>s});var r=n(33444),i=n(76760),l=n(1708);function o(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}var a=n(73136);let u=["history","path","basename","stem","extname","dirname"];class s{constructor(e){let t,n;t=e?o(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":l.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<u.length;){let e=u[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)u.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?i.basename(this.path):void 0}set basename(e){f(e,"basename"),c(e,"basename"),this.path=i.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?i.dirname(this.path):void 0}set dirname(e){p(this.basename,"dirname"),this.path=i.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?i.extname(this.path):void 0}set extname(e){if(c(e,"extname"),p(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=i.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){o(e)&&(e=(0,a.fileURLToPath)(e)),f(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?i.basename(this.path,this.extname):void 0}set stem(e){f(e,"stem"),c(e,"stem"),this.path=i.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let i=new r.o(e,t,n);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function c(e,t){if(e&&e.includes(i.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+i.sep+"`")}function f(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function p(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}},90148:(e,t,n)=>{n.d(t,{B:()=>r});function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}},91256:(e,t,n)=>{n.d(t,{qy:()=>b,JW:()=>y});class r{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function i(e,t){let n={},i={};for(let t of e)Object.assign(n,t.property),Object.assign(i,t.normal);return new r(n,i,t)}r.prototype.normal={},r.prototype.property={},r.prototype.space=void 0;var l=n(11855),o=n(92373);function a(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let a=new o.E(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(a.mustUseProperty=!0),t[r]=a,n[(0,l.S)(r)]=r,n[(0,l.S)(a.attribute)]=r}return new r(t,n,e.space)}var u=n(25464);let s=a({properties:{ariaActiveDescendant:null,ariaAtomic:u.booleanish,ariaAutoComplete:null,ariaBusy:u.booleanish,ariaChecked:u.booleanish,ariaColCount:u.number,ariaColIndex:u.number,ariaColSpan:u.number,ariaControls:u.spaceSeparated,ariaCurrent:null,ariaDescribedBy:u.spaceSeparated,ariaDetails:null,ariaDisabled:u.booleanish,ariaDropEffect:u.spaceSeparated,ariaErrorMessage:null,ariaExpanded:u.booleanish,ariaFlowTo:u.spaceSeparated,ariaGrabbed:u.booleanish,ariaHasPopup:null,ariaHidden:u.booleanish,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:u.spaceSeparated,ariaLevel:u.number,ariaLive:null,ariaModal:u.booleanish,ariaMultiLine:u.booleanish,ariaMultiSelectable:u.booleanish,ariaOrientation:null,ariaOwns:u.spaceSeparated,ariaPlaceholder:null,ariaPosInSet:u.number,ariaPressed:u.booleanish,ariaReadOnly:u.booleanish,ariaRelevant:null,ariaRequired:u.booleanish,ariaRoleDescription:u.spaceSeparated,ariaRowCount:u.number,ariaRowIndex:u.number,ariaRowSpan:u.number,ariaSelected:u.booleanish,ariaSetSize:u.number,ariaSort:null,ariaValueMax:u.number,ariaValueMin:u.number,ariaValueNow:u.number,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function c(e,t){return t in e?e[t]:t}function f(e,t){return c(e,t.toLowerCase())}let p=a({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:u.commaSeparated,acceptCharset:u.spaceSeparated,accessKey:u.spaceSeparated,action:null,allow:null,allowFullScreen:u.boolean,allowPaymentRequest:u.boolean,allowUserMedia:u.boolean,alt:null,as:null,async:u.boolean,autoCapitalize:null,autoComplete:u.spaceSeparated,autoFocus:u.boolean,autoPlay:u.boolean,blocking:u.spaceSeparated,capture:null,charSet:null,checked:u.boolean,cite:null,className:u.spaceSeparated,cols:u.number,colSpan:null,content:null,contentEditable:u.booleanish,controls:u.boolean,controlsList:u.spaceSeparated,coords:u.number|u.commaSeparated,crossOrigin:null,data:null,dateTime:null,decoding:null,default:u.boolean,defer:u.boolean,dir:null,dirName:null,disabled:u.boolean,download:u.overloadedBoolean,draggable:u.booleanish,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:u.boolean,formTarget:null,headers:u.spaceSeparated,height:u.number,hidden:u.overloadedBoolean,high:u.number,href:null,hrefLang:null,htmlFor:u.spaceSeparated,httpEquiv:u.spaceSeparated,id:null,imageSizes:null,imageSrcSet:null,inert:u.boolean,inputMode:null,integrity:null,is:null,isMap:u.boolean,itemId:null,itemProp:u.spaceSeparated,itemRef:u.spaceSeparated,itemScope:u.boolean,itemType:u.spaceSeparated,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:u.boolean,low:u.number,manifest:null,max:null,maxLength:u.number,media:null,method:null,min:null,minLength:u.number,multiple:u.boolean,muted:u.boolean,name:null,nonce:null,noModule:u.boolean,noValidate:u.boolean,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:u.boolean,optimum:u.number,pattern:null,ping:u.spaceSeparated,placeholder:null,playsInline:u.boolean,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:u.boolean,referrerPolicy:null,rel:u.spaceSeparated,required:u.boolean,reversed:u.boolean,rows:u.number,rowSpan:u.number,sandbox:u.spaceSeparated,scope:null,scoped:u.boolean,seamless:u.boolean,selected:u.boolean,shadowRootClonable:u.boolean,shadowRootDelegatesFocus:u.boolean,shadowRootMode:null,shape:null,size:u.number,sizes:null,slot:null,span:u.number,spellCheck:u.booleanish,src:null,srcDoc:null,srcLang:null,srcSet:null,start:u.number,step:null,style:null,tabIndex:u.number,target:null,title:null,translate:null,type:null,typeMustMatch:u.boolean,useMap:null,value:u.booleanish,width:u.number,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:u.spaceSeparated,axis:null,background:null,bgColor:null,border:u.number,borderColor:null,bottomMargin:u.number,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:u.boolean,declare:u.boolean,event:null,face:null,frame:null,frameBorder:null,hSpace:u.number,leftMargin:u.number,link:null,longDesc:null,lowSrc:null,marginHeight:u.number,marginWidth:u.number,noResize:u.boolean,noHref:u.boolean,noShade:u.boolean,noWrap:u.boolean,object:null,profile:null,prompt:null,rev:null,rightMargin:u.number,rules:null,scheme:null,scrolling:u.booleanish,standby:null,summary:null,text:null,topMargin:u.number,valueType:null,version:null,vAlign:null,vLink:null,vSpace:u.number,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:u.boolean,disableRemotePlayback:u.boolean,prefix:null,property:null,results:u.number,security:null,unselectable:null},space:"html",transform:f}),d=a({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:u.commaOrSpaceSeparated,accentHeight:u.number,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:u.number,amplitude:u.number,arabicForm:null,ascent:u.number,attributeName:null,attributeType:null,azimuth:u.number,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:u.number,by:null,calcMode:null,capHeight:u.number,className:u.spaceSeparated,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:u.number,diffuseConstant:u.number,direction:null,display:null,dur:null,divisor:u.number,dominantBaseline:null,download:u.boolean,dx:null,dy:null,edgeMode:null,editable:null,elevation:u.number,enableBackground:null,end:null,event:null,exponent:u.number,externalResourcesRequired:null,fill:null,fillOpacity:u.number,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:u.commaSeparated,g2:u.commaSeparated,glyphName:u.commaSeparated,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:u.number,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:u.number,horizOriginX:u.number,horizOriginY:u.number,id:null,ideographic:u.number,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:u.number,k:u.number,k1:u.number,k2:u.number,k3:u.number,k4:u.number,kernelMatrix:u.commaOrSpaceSeparated,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:u.number,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:u.number,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:u.number,overlineThickness:u.number,paintOrder:null,panose1:null,path:null,pathLength:u.number,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:u.spaceSeparated,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:u.number,pointsAtY:u.number,pointsAtZ:u.number,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:u.commaOrSpaceSeparated,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:u.commaOrSpaceSeparated,rev:u.commaOrSpaceSeparated,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:u.commaOrSpaceSeparated,requiredFeatures:u.commaOrSpaceSeparated,requiredFonts:u.commaOrSpaceSeparated,requiredFormats:u.commaOrSpaceSeparated,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:u.number,specularExponent:u.number,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:u.number,strikethroughThickness:u.number,string:null,stroke:null,strokeDashArray:u.commaOrSpaceSeparated,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:u.number,strokeOpacity:u.number,strokeWidth:null,style:null,surfaceScale:u.number,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:u.commaOrSpaceSeparated,tabIndex:u.number,tableValues:null,target:null,targetX:u.number,targetY:u.number,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:u.commaOrSpaceSeparated,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:u.number,underlineThickness:u.number,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:u.number,values:null,vAlphabetic:u.number,vMathematical:u.number,vectorEffect:null,vHanging:u.number,vIdeographic:u.number,version:null,vertAdvY:u.number,vertOriginX:u.number,vertOriginY:u.number,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:u.number,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:c}),h=a({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),m=a({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:f}),g=a({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),b=i([s,p,h,m,g],"html"),y=i([s,d,h,m,g],"svg")},92072:(e,t,n)=>{n.d(t,{dc:()=>l,VG:()=>o});var r=n(49209);let i=[],l=!1;function o(e,t,n,o){let a;"function"==typeof t&&"function"!=typeof n?(o=n,n=t):a=t;let u=(0,r.C)(a),s=o?-1:1;(function e(r,a,c){let f=r&&"object"==typeof r?r:{};if("string"==typeof f.type){let e="string"==typeof f.tagName?f.tagName:"string"==typeof f.name?f.name:void 0;Object.defineProperty(p,"name",{value:"node (\x1b[33m"+r.type+(e?"<"+e+">":"")+"\x1b[39m)"})}return p;function p(){var f;let p,d,h,m=i;if((!t||u(r,a,c[c.length-1]||void 0))&&(m=Array.isArray(f=n(r,c))?f:"number"==typeof f?[!0,f]:null==f?i:[f])[0]===l)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(d=(o?r.children.length:-1)+s,h=c.concat(r);d>-1&&d<r.children.length;){if((p=e(r.children[d],d,h)())[0]===l)return p;d="number"==typeof p[1]?p[1]:d+s}return m}})(e,void 0,[])()}},92373:(e,t,n)=>{n.d(t,{E:()=>o});var r=n(17405),i=n(25464);let l=Object.keys(i);class o extends r.R{constructor(e,t,n,r){let o=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++o<l.length;){let e=l[o];!function(e,t,n){n&&(e[t]=n)}(this,l[o],(n&i[e])===i[e])}}}o.prototype.defined=!0},94014:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(26373).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},98270:(e,t,n)=>{n.d(t,{D:()=>r});function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}}};