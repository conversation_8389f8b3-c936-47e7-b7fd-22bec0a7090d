"use strict";exports.id=3489,exports.ids=[3489],exports.modules={5336:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25334:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},33489:(e,t,r)=>{r.d(t,{A:()=>I});var s=r(60687),a=r(43210),l=r(77618),i=r(78521),n=r(40228),d=r(85778),c=r(37360),o=r(5336),m=r(62688);let x=(0,m.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var h=r(48730),u=r(94865);let p=(0,m.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),g=(0,m.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),b=(0,m.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var y=r(25334),f=r(93613),v=r(41862);let j={light:{container:"bg-white border-2 border-purple-500 text-purple-600",icon:"text-purple-500",text:"text-purple-600",accent:"bg-purple-500"},dark:{container:"bg-gray-900 border-2 border-yellow-400 text-yellow-400",icon:"text-yellow-400",text:"text-yellow-400",accent:"bg-yellow-400"},neutral:{container:"bg-gray-50 border-2 border-gray-400 text-gray-700",icon:"text-gray-600",text:"text-gray-700",accent:"bg-gray-400"}},N={sm:{container:"px-3 py-2 text-xs",icon:"h-3 w-3",spacing:"gap-1.5"},md:{container:"px-4 py-2.5 text-sm",icon:"h-4 w-4",spacing:"gap-2"},lg:{container:"px-6 py-3 text-base",icon:"h-5 w-5",spacing:"gap-2.5"}};function w({theme:e="light",size:t="md",showCopyButton:r=!1,toolName:l="Your Tool",className:i=""}){let[n,d]=(0,a.useState)(!1),c=j[e],o=N[t],m=async()=>{try{await navigator.clipboard.writeText(A(e,t)),d(!0),setTimeout(()=>d(!1),2e3)}catch(e){console.error("Failed to copy:",e)}};return(0,s.jsxs)("div",{className:`inline-flex items-center ${i}`,children:[(0,s.jsxs)("a",{href:"https://aitools.pub",target:"_blank",rel:"noopener noreferrer",className:`
          inline-flex items-center rounded-lg font-semibold transition-all duration-200 hover:scale-105 hover:shadow-lg
          ${c.container} ${o.container} ${o.spacing}
        `,children:[(0,s.jsx)(x,{className:`${c.icon} ${o.icon}`}),(0,s.jsx)("span",{className:c.text,children:"Featured on AITools"})]}),r&&(0,s.jsx)("button",{onClick:m,className:"ml-2 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",title:"Copy embed code",children:n?(0,s.jsx)(p,{className:"h-4 w-4 text-green-500"}):(0,s.jsx)(g,{className:"h-4 w-4"})})]})}function A(e="light",t="md"){return`<a href="https://aitools.pub" target="_blank" rel="noopener noreferrer" style="display: inline-flex; align-items: center; padding: ${"sm"===t?"8px 12px":"md"===t?"10px 16px":"12px 24px"}; border-radius: 8px; text-decoration: none; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-weight: 600; font-size: ${"sm"===t?"12px":"md"===t?"14px":"16px"}; gap: ${"sm"===t?"6px":"8px"}; ${"light"===e?"background-color: #ffffff; border: 2px solid #9333ea; color: #9333ea;":"dark"===e?"background-color: #111827; border: 2px solid #fbbf24; color: #fbbf24;":"background-color: #f9fafb; border: 2px solid #6b7280; color: #374151;"} transition: all 0.2s ease;">
  <svg width="${"sm"===t?"12":"md"===t?"16":"20"}" height="${"sm"===t?"12":"md"===t?"16":"20"}" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"/>
    <circle cx="12" cy="8" r="6"/>
  </svg>
  Featured on AITools
</a>`}function k({toolId:e,toolWebsite:t,onVerificationSuccess:r,className:i=""}){let[n,d]=(0,a.useState)("light"),[c,m]=(0,a.useState)(!1),[h,u]=(0,a.useState)(!1),[j,N]=(0,a.useState)("idle"),[k,_]=(0,a.useState)(""),I=(0,l.c3)("badge_verification"),R=[{value:"light",label:I("themes.light.label"),description:I("themes.light.description")},{value:"dark",label:I("themes.dark.label"),description:I("themes.dark.description")},{value:"neutral",label:I("themes.neutral.label"),description:I("themes.neutral.description")}],E=async()=>{try{let e=A(n,"lg");await navigator.clipboard.writeText(e),m(!0),setTimeout(()=>m(!1),2e3)}catch(e){console.error("Failed to copy:",e)}},F=async()=>{if(!t)return void _(I("errors.no_website"));u(!0),_(""),N("idle");try{let s=await fetch("/api/tools/verify-badge",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({toolId:e,website:t,expectedText:"Featured on AITools"})}),a=await s.json();a.success?(N("success"),setTimeout(()=>{r()},1500)):(N("error"),_(a.error||I("errors.verification_failed")))}catch(e){N("error"),_(I("errors.network_error"))}finally{u(!1)}};return(0,s.jsxs)("div",{className:`bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200 ${i}`,children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsx)(x,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:I("title")}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:I("subtitle")})]})]}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3",children:"1"}),(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"选择Badge主题"})]}),(0,s.jsx)("div",{className:"mb-4 ml-11",children:(0,s.jsx)("div",{className:"flex border-b border-gray-200",children:R.map(e=>(0,s.jsx)("button",{onClick:()=>d(e.value),className:`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${n===e.value?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:e.label},e.value))})}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-200 mb-4 ml-11",children:[(0,s.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"预览："}),(0,s.jsx)(w,{theme:n,size:"lg"})]}),(0,s.jsx)("div",{className:"ml-11",children:(0,s.jsx)("button",{onClick:E,className:"w-full bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p,{className:"h-4 w-4"}),"已复制!"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g,{className:"h-4 w-4"}),"复制嵌入代码"]})})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3",children:"2"}),(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"添加到您的网站"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-600 ml-11",children:"将复制的代码添加到您网站的首页，通常放在页脚或侧边栏位置。"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3",children:"3"}),(0,s.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"验证Badge安装"})]}),t&&(0,s.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-200 mb-4 ml-11",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-2",children:[(0,s.jsx)(b,{className:"h-4 w-4"}),"您的网站："]}),(0,s.jsxs)("a",{href:t,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center gap-1",children:[t,(0,s.jsx)(y.A,{className:"h-3 w-3"})]})]}),"success"===j&&(0,s.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4 ml-11",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-green-800",children:[(0,s.jsx)(o.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"font-medium",children:"Badge验证成功！"})]}),(0,s.jsx)("p",{className:"text-sm text-green-600 mt-1",children:"您现在可以选择任意发布日期，无需等待一个月。"})]}),"error"===j&&(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4 ml-11",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-red-800",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"font-medium",children:"验证失败"})]}),(0,s.jsx)("p",{className:"text-sm text-red-600 mt-1",children:k})]}),(0,s.jsx)("div",{className:"ml-11",children:(0,s.jsx)("button",{onClick:F,disabled:h||"success"===j,className:"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors",children:h?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"h-4 w-4 animate-spin"}),"验证中..."]}):"success"===j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"已验证！"]}):"验证Badge"})})]})]})]})}let _=u.vS;function I({toolId:e,toolWebsite:t,currentOption:r="paid",currentDate:m,isEditing:p=!1,onSubmit:g,isSubmitting:b,error:y,hasPaidOrder:f=!1,badgeVerified:v=!1}){let[j,N]=(0,a.useState)(f?"paid":r),[w,A]=(0,a.useState)(""),[I,R]=(0,a.useState)(!1),[E,F]=(0,a.useState)(v),P=(0,l.c3)("launch"),C=(0,i.Ym)(),S=()=>{if(!(0,u.mp)())return 0;let e=u.kX.PRIORITY_LAUNCH.originalPrice;return Math.round((e-u.kX.PRIORITY_LAUNCH.displayPrice)/e*100)},M=()=>{if(E){let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]}let e=new Date;return e.setMonth(e.getMonth()+1),e.toISOString().split("T")[0]},$=()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]},U=e=>{f||(N(e),"free"===e?A(M()):A($()))},T=async()=>{w&&await g(j,w)};return(0,s.jsxs)("div",{className:"space-y-8",children:[!f&&(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:p?P("select_plan"):P("select_option")}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:_.map(e=>(0,s.jsxs)("div",{className:`relative border-2 rounded-lg p-6 cursor-pointer transition-all flex flex-col ${j===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"} ${"recommended"in e&&e.recommended?"ring-2 ring-blue-200":""}`,onClick:()=>U(e.id),children:["recommended"in e&&e.recommended&&(0,s.jsx)("div",{className:"absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:P("recommended")}),(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4 flex-1",children:[(0,s.jsxs)("div",{className:"flex items-center",children:["free"===e.id?(0,s.jsx)(n.A,{className:"h-6 w-6 text-gray-600 mr-3"}):(0,s.jsx)(d.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:P(`plans.${e.id}.title`)}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:P(`plans.${e.id}.description`)})]})]}),(0,s.jsx)("div",{className:"text-right",children:"paid"===e.id&&(0,u.mp)()?(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-semibold mb-3 shadow-lg",children:[(0,s.jsx)(c.A,{className:"h-3 w-3 mr-1"}),P("promotion.early_bird")]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[P("promotion.original_price")," ",(0,s.jsx)("span",{className:"line-through font-medium text-gray-400",children:(0,u.S9)(u.kX.PRIORITY_LAUNCH.originalPrice,C)})]}),(0,s.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:(0,u.$g)(e.price,C)}),(0,s.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full bg-red-100 text-red-600 text-xs font-bold",children:["Save ",S(),"%"]})]})]}):(0,s.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,u.$g)(e.price,C)})})]}),(0,s.jsx)("ul",{className:"space-y-2",children:e.features.map((t,r)=>(0,s.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 text-green-500 mr-2 flex-shrink-0"}),P(`plans.${e.id}.features.${r}`)]},r))}),(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("input",{type:"radio",name:"launchOption",value:e.id,checked:j===e.id,onChange:()=>U(e.id),className:"sr-only"}),(0,s.jsx)("div",{className:`w-4 h-4 rounded-full border-2 ${j===e.id?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:j===e.id&&(0,s.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})})]})]},e.id))})]}),!f&&"free"===j&&!E&&t&&(0,s.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(x,{className:"h-6 w-6 text-blue-600 mr-3 mt-0.5"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h4",{className:"text-lg font-semibold text-blue-900 mb-2",children:P("badge_option_title")}),(0,s.jsx)("p",{className:"text-blue-700 mb-4",children:P("badge_option_description")}),(0,s.jsxs)("button",{onClick:()=>R(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,s.jsx)(x,{className:"h-4 w-4"}),P("badge_option_button")]})]})]})}),E&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-500 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-green-800",children:"Badge验证成功！"}),(0,s.jsx)("p",{className:"text-sm text-green-600 mt-1",children:"您现在可以选择任意发布日期，无需等待一个月。"})]})]})}),I&&(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)(k,{toolId:e,toolWebsite:t,onVerificationSuccess:()=>{if(F(!0),R(!1),"free"===j){let e=new Date;e.setDate(e.getDate()+1),A(e.toISOString().split("T")[0])}}})}),f&&(0,s.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(o.A,{className:"h-5 w-5 text-green-500 mr-2"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-green-800",children:P("priority_service_activated_title")}),(0,s.jsx)("p",{className:"text-sm text-green-600 mt-1",children:P("priority_service_activated_description")})]})]})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 mr-2"}),P("select_date")]}),(0,s.jsxs)("div",{className:"max-w-md",children:[(0,s.jsx)("input",{type:"date",value:w,onChange:e=>A(e.target.value),min:f||"paid"===j?$():M(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:f?P("paid_date_info"):"free"===j?E?P("paid_date_info")+" (Badge已验证)":P("free_date_info"):P("paid_date_info")})]})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("button",{onClick:T,disabled:b||!w,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto",children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"paid"===j?P("processing"):P("saving")]}):(0,s.jsx)(s.Fragment,{children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),P("save_changes")]}):"paid"===j?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"h-4 w-4 mr-2"}),(0,u.mp)()?(0,s.jsxs)("span",{className:"flex items-center",children:[p?"zh"===C?"升级并支付 ":"Upgrade and Pay ":"zh"===C?"立即支付 ":"Pay Now ",(0,s.jsx)("span",{className:"line-through text-blue-200 mx-1 text-sm",children:(0,u.S9)(u.kX.PRIORITY_LAUNCH.originalPrice,C)}),(0,s.jsx)("span",{className:"font-bold",children:(0,u.$g)(u.kX.PRIORITY_LAUNCH.displayPrice,C)}),(0,s.jsx)("span",{className:"ml-2 px-2 py-0.5 bg-amber-400 text-amber-900 text-xs rounded-full font-medium",children:P("promotion.early_bird")})]}):(0,s.jsx)("span",{children:p?P("upgrade_and_pay",{price:(0,u.$g)(u.kX.PRIORITY_LAUNCH.displayPrice,C)}):P("pay_amount",{price:(0,u.$g)(u.kX.PRIORITY_LAUNCH.displayPrice,C)})})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),p?P("save_changes"):P("confirm_date")]})})}),y&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-4",children:y}),(0,s.jsx)("p",{className:"text-gray-500 text-sm mt-4",children:f?P("changes_effective"):"paid"===j?P("payment_redirect"):p?P("changes_effective"):P("review_queue")})]})]})}},37360:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},40228:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41862:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},48730:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},62688:(e,t,r)=>{r.d(t,{A:()=>m});var s=r(43210);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim(),d=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,s.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:l="",children:i,iconNode:o,...m},x)=>(0,s.createElement)("svg",{ref:x,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:n("lucide",l),...!i&&!d(m)&&{"aria-hidden":"true"},...m},[...o.map(([e,t])=>(0,s.createElement)(e,t)),...Array.isArray(i)?i:[i]])),m=(e,t)=>{let r=(0,s.forwardRef)(({className:r,...l},d)=>(0,s.createElement)(o,{ref:d,iconNode:t,className:n(`lucide-${a(i(e))}`,`lucide-${e}`,r),...l}));return r.displayName=i(e),r}},85778:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},93613:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(62688).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94865:(e,t,r)=>{r.d(t,{$g:()=>o,Ef:()=>d,S9:()=>m,Y$:()=>n,kX:()=>s,mV:()=>c,mp:()=>h,sT:()=>x,tF:()=>u,v4:()=>i,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"All Prices"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],n=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],d=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},c=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},o=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,m=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,x=()=>s.PRIORITY_LAUNCH.promotion,h=()=>{let e=s.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},u=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};