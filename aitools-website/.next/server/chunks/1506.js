exports.id=1506,exports.ids=[1506],exports.modules={595:(e,t,r)=>{Promise.resolve().then(r.bind(r,45196)),Promise.resolve().then(r.bind(r,25441))},18549:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>a,iu:()=>i,ng:()=>l});var s=r(60687);function a({src:e,alt:t,width:r,height:a,className:i="",priority:l=!1,fill:o=!1,sizes:n,placeholder:d="empty",blurDataURL:c,fallbackSrc:m="/images/placeholder.svg"}){return o?(0,s.jsx)("div",{className:"relative overflow-hidden",children:(0,s.jsx)("img",{src:e,alt:t,className:i,style:{objectFit:"contain",padding:2,width:"100%",height:"100%"}})}):(0,s.jsx)("img",{src:e,alt:t,width:r,height:a,className:i,style:{objectFit:"contain",padding:2}})}r(43210);let i={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:84,height:84},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},l={avatar:"40px",toolLogo:"52px",toolLogoLarge:"84px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}},25441:(e,t,r)=>{"use strict";r.d(t,{default:()=>I});var s=r(60687),a=r(43210),i=r(82136),l=r(12340),o=r(77618),n=r(33823),d=r(78890),c=r(48577),m=r(94865),u=r(28559),x=r(16023),g=r(96882),p=r(47342),h=r(56085);let b=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"];var f=r(11860),w=r(99270),j=r(37360);function y({selectedTags:e,onTagsChange:t,maxTags:r=3,placeholder:i}){let[n,d]=(0,a.useState)(""),[c,m]=(0,a.useState)(!1),u=(0,l.a8)(),x=(0,o.c3)("common"),g=function(){let e=(0,o.c3)("tags");return b.map(t=>({key:t,label:e(t)}))}();u?.startsWith("/en");let p=s=>{e.includes(s)?t(e.filter(e=>e!==s)):e.length<r&&t([...e,s])},h=r=>{t(e.filter(e=>e!==r))},y=g.filter(t=>t.label.toLowerCase().includes(n.toLowerCase())&&!e.includes(t.key)),v=e=>{let t=g.find(t=>t.key===e);return t?t.label:e};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:x("select_tags")}),(0,s.jsx)("span",{className:"text-sm text-gray-500",children:x("selected_count",{count:e.length,max:r})})]}),e.length>0&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:x("selected_tags")}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:e.map(e=>(0,s.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[v(e),(0,s.jsx)("button",{type:"button",onClick:()=>h(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,s.jsx)(f.A,{className:"h-3 w-3"})})]},e))})]}),(0,s.jsx)("div",{className:"space-y-3",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:x("select_tags_max",{max:r})}),(0,s.jsxs)("div",{className:"relative mb-3",children:[(0,s.jsx)("input",{type:"text",placeholder:i||x("search_tags"),value:n,onChange:e=>d(e.target.value),onFocus:()=>m(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,s.jsx)(w.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(c||n)&&(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:y.length>0?(0,s.jsxs)("div",{className:"p-2",children:[(0,s.jsx)("div",{className:"grid grid-cols-1 gap-1",children:y.map(t=>{let a=e.length>=r;return(0,s.jsx)("button",{type:"button",onClick:()=>{p(t.key),d(""),m(!1)},disabled:a,className:`
                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors
                              ${a?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700"}
                            `,children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(j.A,{className:"h-3 w-3 mr-2 text-gray-400"}),t.label]})},t.key)})}),y.length>50&&(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:x("found_tags",{count:y.length})})]}):(0,s.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:x(n?"no_tags_found":"start_typing")})})})]})}),(c||n)&&(0,s.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{m(!1),d("")}}),e.length>=r&&(0,s.jsx)("p",{className:"text-sm text-amber-600",children:x("max_tags_limit",{max:r})})]})}var v=r(18549);let N={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:2e3,label:"Description"},WEBSITE_URL:{min:10,max:100,label:"Website URL"}};function _({current:e,max:t,min:r=0,className:a=""}){let i=e>t,l=e<r,o=e/t*100,n="text-gray-500";return n=i?"text-red-500":l?"text-orange-500":o>80?"text-yellow-600":"text-green-600",(0,s.jsxs)("div",{className:`text-sm ${n} ${a}`,children:[(0,s.jsx)("span",{className:"font-medium",children:e}),(0,s.jsxs)("span",{className:"text-gray-400",children:["/",t]}),r>0&&e<r&&(0,s.jsxs)("span",{className:"ml-2 text-orange-500",children:["(At least ",r," characters)"]}),i&&(0,s.jsxs)("span",{className:"ml-2 text-red-500",children:["(Exceeded by ",e-t," characters)"]})]})}process.env.UPLOAD_BASE_DIR;var k=r(76180),E=r.n(k);let A=(0,r(30036).default)(async()=>{},{loadableGenerated:{modules:["src/components/MarkdownEditor.tsx -> @uiw/react-md-editor"]},ssr:!1});function F({value:e,onChange:t,placeholder:r,maxLength:i,error:l,className:n="",height:d=300}){let c=(0,o.c3)("form"),[m,u]=(0,a.useState)(!1),x=e=>{let r=e||"";i&&r.length>i||t(r)};return m?(0,s.jsxs)("div",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]])+" "+(n||""),children:[(0,s.jsx)("div",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]])+" markdown-editor-wrapper",children:(0,s.jsx)(A,{value:e,onChange:x,height:d,preview:"edit",hideToolbar:!1,textareaProps:{placeholder:r||c("description_placeholder"),style:{fontSize:14,lineHeight:1.5,fontFamily:"inherit"},maxLength:i},"data-color-mode":"light"})}),i&&(0,s.jsxs)("div",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]])+" mt-1 flex justify-between items-center text-sm",children:[(0,s.jsxs)("div",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]])+" text-gray-500",children:["Supports Markdown: ",(0,s.jsx)("b",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]]),children:"**bold**"}),", ",(0,s.jsx)("b",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]]),children:"## headings"}),", ",(0,s.jsx)("b",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]]),children:"- lists"})]}),(0,s.jsxs)("div",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]])+" "+`${e.length>.9*i?"text-orange-600":"text-gray-500"}`,children:[e.length,"/",i]})]}),l&&(0,s.jsx)("div",{className:E().dynamic([["ede86589fc8ae36d",[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""]]])+" mt-1 text-sm text-red-600",children:l}),(0,s.jsx)(E(),{id:"ede86589fc8ae36d",dynamic:[l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""],children:`.markdown-editor-wrapper .w-md-editor{background-color:transparent}.markdown-editor-wrapper .w-md-editor-text-container{-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;border:1px solid#d1d5db}.markdown-editor-wrapper .w-md-editor-text-container:focus-within{border-color:#3b82f6;-webkit-box-shadow:0 0 0 2px rgba(59,130,246,.1);-moz-box-shadow:0 0 0 2px rgba(59,130,246,.1);box-shadow:0 0 0 2px rgba(59,130,246,.1)}.markdown-editor-wrapper .w-md-editor-text-input,.markdown-editor-wrapper .w-md-editor-text-textarea{font-size:14px!important;line-height:1.5!important;padding:12px!important}.markdown-editor-wrapper .w-md-editor-toolbar{border-bottom:1px solid#e5e7eb;padding:8px 12px;background-color:#f9fafb;-webkit-border-radius:.375rem .375rem 0 0;-moz-border-radius:.375rem .375rem 0 0;border-radius:.375rem .375rem 0 0}.markdown-editor-wrapper .w-md-editor-toolbar-divider{background-color:#e5e7eb}.markdown-editor-wrapper .w-md-editor-toolbar button{color:#6b7280;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;padding:4px 6px}.markdown-editor-wrapper .w-md-editor-toolbar button:hover{background-color:#e5e7eb;color:#374151}.markdown-editor-wrapper .w-md-editor-toolbar button.active{background-color:#dbeafe;color:#1d4ed8}${l?`
        .markdown-editor-wrapper .w-md-editor-text-container {
          border-color: #ef4444 !important;
        }
        `:""}      `})]}):(0,s.jsxs)("div",{className:n,children:[(0,s.jsx)("textarea",{value:e,onChange:e=>x(e.target.value),placeholder:r,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${l?"border-red-300":"border-gray-300"}`,style:{height:`${d}px`},maxLength:i}),i&&(0,s.jsxs)("div",{className:"mt-1 text-right text-sm text-gray-500",children:[e.length,"/",i]}),l&&(0,s.jsx)("div",{className:"mt-1 text-sm text-red-600",children:l})]})}function I({categoryOptions:e,isEditMode:t=!1,toolId:r,initialTool:b}){let f=(0,o.c3)("submit"),{data:w,status:j}=(0,i.useSession)(),k=(0,l.rd)(),[E,A]=(0,a.useState)(b||null),[I,C]=(0,a.useState)(t&&!b),[L,R]=(0,a.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[S,T]=(0,a.useState)(null),[P,U]=(0,a.useState)(""),[O,M]=(0,a.useState)(!1),[D,$]=(0,a.useState)(!1),[z,B]=(0,a.useState)("idle"),[q,H]=(0,a.useState)(""),[Y,G]=(0,a.useState)({}),[W,J]=(0,a.useState)(!1),[V,X]=(0,a.useState)(!1),Z=e=>{let{name:t,value:r}=e.target;R(e=>({...e,[t]:r}))},K=e=>{let t=e.target.files?.[0];if(t){R(e=>({...e,logoFile:t}));let e=new FileReader;e.onload=e=>{T(e.target?.result)},e.readAsDataURL(t)}},Q=async()=>{if(!L.website.trim())return void G(e=>({...e,website:f("form.website_url_invalid")}));try{new URL(L.website)}catch(e){G(e=>({...e,website:f("form.website_url_invalid")}));return}X(!0),G(e=>({...e,website:""}));try{let e=await fetch("/api/ai/generate-product-info",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({website:L.website})}),t=await e.json();t.success?(R(e=>({...e,name:t.data.name||e.name,tagline:t.data.tagline||e.tagline,description:t.data.description||e.description,category:t.data.category||e.category,tags:t.data.tags&&t.data.tags.length>0?t.data.tags:e.tags})),G(e=>{let t={...e};return delete t.name,delete t.tagline,delete t.description,delete t.category,delete t.tags,t})):G(e=>({...e,website:t.error||"AI generation failed, please try again"}))}catch(e){console.error("AI generation error:",e),G(e=>({...e,website:"Network error, please try again"}))}finally{X(!1)}},ee=()=>{let e={};return L.name.trim()||(e.name=f("form.tool_name")+" is required"),L.description.trim()||(e.description=f("form.description")+" is required"),L.website.trim()||(e.website=f("form.website_url")+" is required"),L.category||(e.category=f("form.category")+" is required"),L.pricing||(e.pricing=f("form.pricing_model")+" is required"),L.website&&!L.website.match(/^https?:\/\/.+/)&&(e.website=f("form.website_url_placeholder")),t||L.logoFile||(e.logo=f("form.logo_required")),0===L.tags.length&&(e.tags=f("form.tags_placeholder")),G(e),0===Object.keys(e).length},et=async e=>{if(e.preventDefault(),!w?.user?.email)return void J(!0);if(ee()){$(!0),B("idle");try{let e=P;if(L.logoFile){let t=new FormData;t.append("logo",L.logoFile);let r=await fetch("/api/upload/logo",{method:"POST",body:t});if(r.ok)e=(await r.json()).data.url;else{let e=await r.json();throw Error(e.message||"Logo upload failed")}}if(t&&r){let t={name:L.name,tagline:L.tagline,description:L.description,website:L.website,logo:e||void 0,category:L.category,tags:L.tags,pricing:L.pricing},s=await fetch(`/api/tools/${r}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),a=await s.json();a.success?(B("success"),H("工具信息更新成功！"),setTimeout(()=>{k.push("/profile/submitted")},2e3)):(B("error"),H(a.error||"Update failed, please retry"))}else{let t={name:L.name,tagline:L.tagline,description:L.description,website:L.website,logo:e,category:L.category,tags:L.tags,pricing:L.pricing},r=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(r.ok){let e=await r.json();B("success"),H(f("form.success_message")),setTimeout(()=>{k.push(`/submit/tool-info-success?toolId=${e.data.toolId}`)},500)}else{let e=await r.json();throw Error(e.message||"Submission failed")}}}catch(e){console.error("Submit error:",e),B("error"),H(e.message+". "+f("form.error_message"))}finally{$(!1)}}};return"loading"===j||I?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsx)(n.A,{size:"lg"})}):w?t&&!E?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tool not found"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"The tool you want to edit does not exist or has been deleted."}),(0,s.jsx)(l.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Back to Tools"})]})}):"success"===z?(0,s.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,s.jsx)(d.A,{message:q||f("form.success_message")})}):(0,s.jsxs)(a.Fragment,{children:[(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[t&&(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)(l.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,s.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Back to Tools"]}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Tool"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"Update your tool information to help more users understand your product."})]}),!t&&(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("div",{className:"flex justify-center mb-4",children:(0,s.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-blue-600"})})}),(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:f("title")}),(0,s.jsx)("p",{className:"text-lg text-gray-600",children:f("subtitle")})]}),(0,s.jsxs)("form",{onSubmit:et,className:"max-w-4xl mx-auto space-y-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,s.jsx)(g.A,{className:"h-5 w-5 mr-2 text-blue-600"}),f("form.basic_info")]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.website_url")," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),!t&&(0,s.jsx)("input",{type:"url",id:"website",name:"website",value:L.website,onChange:Z,placeholder:t?"https://example.com":f("form.website_url_placeholder"),maxLength:N.WEBSITE_URL.max,className:`w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${Y.website?"border-red-300":"border-gray-300"}`,required:!0}),t&&(0,s.jsx)("div",{className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:L.website})]}),!t&&(0,s.jsx)("button",{type:"button",onClick:Q,disabled:V||!L.website.trim(),className:"px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-sm font-medium rounded-md hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2 whitespace-nowrap",children:V?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.A,{size:"sm"}),"生成中..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"AI 表单信息生成"]})})]}),(0,s.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[!t&&(0,s.jsx)(_,{current:L.website.length,max:N.WEBSITE_URL.max,min:N.WEBSITE_URL.min}),Y.website&&(0,s.jsx)("span",{className:"text-red-500 text-sm",children:Y.website})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tool_name")," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"name",name:"name",value:L.name,onChange:Z,placeholder:f("form.tool_name_placeholder"),maxLength:N.TOOL_NAME.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),(0,s.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,s.jsx)(_,{current:L.name.length,max:N.TOOL_NAME.max,min:N.TOOL_NAME.min}),Y.name&&(0,s.jsx)("span",{className:"text-red-500 text-sm",children:Y.name})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tagline")," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:L.tagline,onChange:Z,placeholder:f("form.tagline_placeholder"),maxLength:N.TAGLINE.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,s.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,s.jsx)(_,{current:L.tagline.length,max:N.TAGLINE.max,min:N.TAGLINE.min}),Y.tagline&&(0,s.jsx)("span",{className:"text-red-500 text-sm",children:Y.tagline})]})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.description")," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(F,{value:L.description,onChange:e=>R(t=>({...t,description:e})),placeholder:f("form.description_placeholder"),maxLength:N.DESCRIPTION.max,error:Y.description,height:200})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.logo_upload")," ",!t&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,s.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{e.target.files?.[0]&&K(e)},className:"hidden",id:"logo-upload",required:!t}),(0,s.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,s.jsx)(x.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:O?f("form.uploading"):f("form.click_to_upload")}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:f("form.logo_upload_hint")})]})]}),Y.logo&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:Y.logo})]}),S&&(0,s.jsx)(v.Ay,{alt:"app logo",src:S,width:v.iu.toolLogo.width,height:v.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:v.ng.toolLogo,placeholder:"blur"})]})]})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:f("form.category_and_pricing")}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.category")," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("select",{id:"category",name:"category",value:L.category,onChange:Z,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,s.jsx)("option",{value:"",children:f("form.category_placeholder")}),e.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"pricing",className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.pricing_model")," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("select",{id:"pricing",name:"pricing",value:L.pricing,onChange:Z,className:`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${Y.pricing?"border-red-300":"border-gray-300"}`,required:!0,children:[(0,s.jsx)("option",{value:"",children:f("form.pricing_placeholder")}),m.Y$.map(e=>(0,s.jsx)("option",{value:e.value,children:f(`form.${e.value}`)},e.value))]}),Y.pricing&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:Y.pricing})]})]}),(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[f("form.tags")," ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)(y,{selectedTags:L.tags,onTagsChange:e=>{R(t=>({...t,tags:e}))},maxTags:3,placeholder:f("form.tags_placeholder")}),Y.tags&&(0,s.jsx)("p",{className:"text-red-600 text-sm mt-1",children:Y.tags})]})]}),!t&&(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:f("form.guidelines_title")}),(0,s.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_1")]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_2")]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_3")]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_4")]}),(0,s.jsxs)("li",{className:"flex items-start",children:[(0,s.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),f("form.guideline_5")]})]})]}),(0,s.jsx)("div",{className:t?"flex justify-end":"flex justify-center",children:(0,s.jsx)("button",{type:"submit",disabled:D,className:"inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors px-8 py-3 text-base",children:D?(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)(n.A,{size:"sm",className:"mr-2"}),f("form.submitting")]}):(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)(x.A,{className:"h-5 w-5 mr-2"}),f("form.submit_button")]})})})]}),"error"===z&&(0,s.jsx)("div",{className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsx)("p",{className:"text-red-800",children:q})})]}),(0,s.jsx)(c.A,{isOpen:W,onClose:()=>J(!1)})]}):(0,s.jsxs)(a.Fragment,{children:[(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:f("auth.login_required")}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:f("auth.login_to_submit")}),(0,s.jsx)("button",{onClick:()=>J(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:f("auth.login")})]})}),(0,s.jsx)(c.A,{isOpen:W,onClose:()=>J(!1)})]})}},33823:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(60687);function a({size:e="md",className:t=""}){return(0,s.jsx)("div",{className:`flex justify-center items-center ${t}`,children:(0,s.jsx)("div",{className:`${{sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[e]} border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin`})})}},39636:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx","default")},60366:(e,t,r)=>{"use strict";r.d(t,{BB:()=>l,PZ:()=>o,RI:()=>d,ut:()=>n});var s=r(64348);let a=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function i(e){let t=await (0,s.A)({locale:e||"en",namespace:"categories"});return a.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function l(e){return(await i(e)).map(e=>({value:e.slug,label:e.name}))}async function o(e,t){return(await i(t)).find(t=>t.slug===e)}let n=a.map(e=>e.slug),d=a.reduce((e,t)=>(e[t.slug]=t,e),{})},78890:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687),a=r(5336),i=r(11860);function l({message:e,onClose:t,className:r=""}){return(0,s.jsx)("div",{className:`bg-green-50 border border-green-200 rounded-lg p-4 ${r}`,children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)(a.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("p",{className:"text-green-800 text-sm",children:e})}),t&&(0,s.jsx)("button",{onClick:t,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,s.jsx)(i.A,{className:"w-4 h-4"})})]})})}},89979:(e,t,r)=>{Promise.resolve().then(r.bind(r,80994)),Promise.resolve().then(r.bind(r,39636))},94865:(e,t,r)=>{"use strict";r.d(t,{$g:()=>c,Ef:()=>n,S9:()=>m,Y$:()=>o,kX:()=>s,mV:()=>d,mp:()=>x,sT:()=>u,tF:()=>g,v4:()=>l,vS:()=>a});let s={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}},a=[{id:"free",title:"免费发布",description:s.FREE_LAUNCH.description,price:s.FREE_LAUNCH.displayPrice,features:s.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:s.PRIORITY_LAUNCH.description,price:s.PRIORITY_LAUNCH.displayPrice,features:s.PRIORITY_LAUNCH.features,recommended:!0}],i={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}},l=[{value:"",label:"All Prices"},{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],o=[{value:i.FREE.value,label:i.FREE.label},{value:i.FREEMIUM.value,label:i.FREEMIUM.label},{value:i.PAID.value,label:i.PAID.label}],n=e=>{switch(e){case i.FREE.value:return i.FREE.color;case i.FREEMIUM.value:return i.FREEMIUM.color;case i.PAID.value:return i.PAID.color;default:return"bg-gray-100 text-gray-800"}},d=e=>{switch(e){case i.FREE.value:return i.FREE.label;case i.FREEMIUM.value:return i.FREEMIUM.label;case i.PAID.value:return i.PAID.label;default:return e}},c=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,m=(e,t)=>0===e?"zh"===t?"免费":"Free":`\xa5${e}`,u=()=>s.PRIORITY_LAUNCH.promotion,x=()=>{let e=s.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},g=(e,t="cny")=>new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}};