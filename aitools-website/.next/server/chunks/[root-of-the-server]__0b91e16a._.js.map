{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI;\n\nif (!MONGODB_URI) {\n  throw new Error(\n    'Please define the MONGODB_URI environment variable inside .env.local'\n  );\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function dbConnect() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI!, opts) as any;\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default dbConnect;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW;AAE3C,IAAI,CAAC,aAAa;IAChB,MAAM,IAAI,MACR;AAEJ;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAc;IAClD;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\n\nexport interface IAccount {\n  provider: 'google' | 'github' | 'email';\n  providerId: string;\n  providerAccountId: string;\n  accessToken?: string;\n  refreshToken?: string;\n  expiresAt?: Date;\n}\n\nexport interface IUser extends Document {\n  email: string;\n  name: string;\n  avatar?: string;\n  role: 'user' | 'admin';\n  isActive: boolean;\n\n  // 个人信息\n  bio?: string;\n  website?: string;\n  location?: string;\n\n  // 认证相关\n  emailVerified: boolean;\n  emailVerificationToken?: string;\n  emailVerificationExpires?: Date;\n\n  // OAuth账户关联\n  accounts: IAccount[];\n\n  // 用户行为\n  submittedTools: string[]; // Tool IDs\n  likedTools: string[]; // Tool IDs\n  comments: string[]; // Comment IDs\n\n  // 时间戳\n  createdAt: Date;\n  updatedAt: Date;\n  lastLoginAt?: Date;\n}\n\nconst AccountSchema = new Schema({\n  provider: {\n    type: String,\n    required: true,\n    enum: ['google', 'github', 'email']\n  },\n  providerId: {\n    type: String,\n    required: true\n  },\n  providerAccountId: {\n    type: String,\n    required: true\n  },\n  accessToken: String,\n  refreshToken: String,\n  expiresAt: Date\n}, { _id: false });\n\nconst UserSchema: Schema = new Schema({\n  email: {\n    type: String,\n    required: [true, 'Email is required'],\n    unique: true,\n    trim: true,\n    lowercase: true,\n    validate: {\n      validator: function(v: string) {\n        return /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(v);\n      },\n      message: 'Please enter a valid email address'\n    }\n  },\n  name: {\n    type: String,\n    required: [true, 'Name is required'],\n    trim: true,\n    maxlength: [100, 'Name cannot exceed 100 characters']\n  },\n  avatar: {\n    type: String,\n    trim: true\n  },\n  role: {\n    type: String,\n    required: true,\n    enum: ['user', 'admin'],\n    default: 'user'\n  },\n  isActive: {\n    type: Boolean,\n    default: true\n  },\n\n  // 个人信息\n  bio: {\n    type: String,\n    trim: true,\n    maxlength: [500, 'Bio cannot exceed 500 characters']\n  },\n  website: {\n    type: String,\n    trim: true,\n    validate: {\n      validator: function(v: string) {\n        if (!v) return true; // 允许空值\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Website must be a valid URL starting with http:// or https://'\n    }\n  },\n  location: {\n    type: String,\n    trim: true,\n    maxlength: [100, 'Location cannot exceed 100 characters']\n  },\n\n  // 认证相关\n  emailVerified: {\n    type: Boolean,\n    default: false\n  },\n  emailVerificationToken: {\n    type: String,\n    trim: true\n  },\n  emailVerificationExpires: {\n    type: Date\n  },\n\n  // OAuth账户关联\n  accounts: [AccountSchema],\n\n  // 用户行为\n  submittedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  likedTools: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Tool'\n  }],\n  comments: [{\n    type: Schema.Types.ObjectId,\n    ref: 'Comment'\n  }],\n\n  // 时间戳\n  lastLoginAt: {\n    type: Date\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes\nUserSchema.index({ email: 1 });\nUserSchema.index({ role: 1 });\nUserSchema.index({ emailVerificationToken: 1 });\nUserSchema.index({ 'accounts.provider': 1, 'accounts.providerAccountId': 1 });\n\n// 实例方法\nUserSchema.methods.addAccount = function(account: IAccount) {\n  // 检查是否已存在相同的账户\n  const existingAccount = this.accounts.find(\n    (acc: IAccount) => acc.provider === account.provider && acc.providerAccountId === account.providerAccountId\n  );\n\n  if (!existingAccount) {\n    this.accounts.push(account);\n  } else {\n    // 更新现有账户信息\n    Object.assign(existingAccount, account);\n  }\n};\n\nUserSchema.methods.removeAccount = function(provider: string, providerAccountId: string) {\n  this.accounts = this.accounts.filter(\n    (acc: IAccount) => !(acc.provider === provider && acc.providerAccountId === providerAccountId)\n  );\n};\n\nexport default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA0CA,MAAM,gBAAgB,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC/B,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAU;YAAU;SAAQ;IACrC;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,mBAAmB;QACjB,MAAM;QACN,UAAU;IACZ;IACA,aAAa;IACb,cAAc;IACd,WAAW;AACb,GAAG;IAAE,KAAK;AAAM;AAEhB,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,OAAO;QACL,MAAM;QACN,UAAU;YAAC;YAAM;SAAoB;QACrC,QAAQ;QACR,MAAM;QACN,WAAW;QACX,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,6BAA6B,IAAI,CAAC;YAC3C;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAmB;QACpC,MAAM;QACN,WAAW;YAAC;YAAK;SAAoC;IACvD;IACA,QAAQ;QACN,MAAM;QACN,MAAM;IACR;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAQ;SAAQ;QACvB,SAAS;IACX;IACA,UAAU;QACR,MAAM;QACN,SAAS;IACX;IAEA,OAAO;IACP,KAAK;QACH,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAmC;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,IAAI,CAAC,GAAG,OAAO,MAAM,OAAO;gBAC5B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,UAAU;QACR,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAK;SAAwC;IAC3D;IAEA,OAAO;IACP,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,wBAAwB;QACtB,MAAM;QACN,MAAM;IACR;IACA,0BAA0B;QACxB,MAAM;IACR;IAEA,YAAY;IACZ,UAAU;QAAC;KAAc;IAEzB,OAAO;IACP,gBAAgB;QAAC;YACf,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,YAAY;QAAC;YACX,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IACF,UAAU;QAAC;YACT,MAAM,yGAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ;YAC3B,KAAK;QACP;KAAE;IAEF,MAAM;IACN,aAAa;QACX,MAAM;IACR;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,UAAU;AACV,WAAW,KAAK,CAAC;IAAE,OAAO;AAAE;AAC5B,WAAW,KAAK,CAAC;IAAE,MAAM;AAAE;AAC3B,WAAW,KAAK,CAAC;IAAE,wBAAwB;AAAE;AAC7C,WAAW,KAAK,CAAC;IAAE,qBAAqB;IAAG,8BAA8B;AAAE;AAE3E,OAAO;AACP,WAAW,OAAO,CAAC,UAAU,GAAG,SAAS,OAAiB;IACxD,eAAe;IACf,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,IAAI,CACxC,CAAC,MAAkB,IAAI,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAAI,iBAAiB,KAAK,QAAQ,iBAAiB;IAG7G,IAAI,CAAC,iBAAiB;QACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACrB,OAAO;QACL,WAAW;QACX,OAAO,MAAM,CAAC,iBAAiB;IACjC;AACF;AAEA,WAAW,OAAO,CAAC,aAAa,GAAG,SAAS,QAAgB,EAAE,iBAAyB;IACrF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAClC,CAAC,MAAkB,CAAC,CAAC,IAAI,QAAQ,KAAK,YAAY,IAAI,iBAAiB,KAAK,iBAAiB;AAEjG;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth';\nimport GoogleProvider from 'next-auth/providers/google';\nimport GitHubProvider from 'next-auth/providers/github';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport dbConnect from './mongodb';\nimport User from '../models/User';\nimport { getNextAuthUrl } from './env';\n\nexport const authOptions: NextAuthOptions = {\n  // 注意：Credentials provider与adapter不兼容，所以我们使用JWT策略\n  // adapter: MongoDBAdapter(client),\n\n  // 动态配置基础URL，支持不同环境\n  // NextAuth会自动从环境变量或请求头中检测URL，但我们也可以显式设置\n  // 在生产环境中，这将被环境变量覆盖\n  ...(process.env.NODE_ENV === 'development' && {\n    // 仅在开发环境中设置，生产环境让NextAuth自动检测\n    // 这样可以支持不同的开发端口\n  }),\n\n  providers: [\n    GoogleProvider({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n    GitHubProvider({\n      clientId: process.env.GITHUB_CLIENT_ID!,\n      clientSecret: process.env.GITHUB_CLIENT_SECRET!,\n    }),\n    CredentialsProvider({\n      id: 'email-code',\n      name: 'Email Code',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        code: { label: 'Code', type: 'text' },\n        token: { label: 'Token', type: 'text' },\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.code || !credentials?.token) {\n          return null;\n        }\n\n        try {\n          await dbConnect();\n\n          // 查找用户\n          const user = await User.findOne({\n            email: credentials.email.toLowerCase(),\n            emailVerificationExpires: { $gt: new Date() }\n          });\n\n          if (!user) {\n            return null;\n          }\n\n          // 验证token和验证码\n          const storedData = user.emailVerificationToken;\n          if (!storedData || !storedData.includes(':')) {\n            return null;\n          }\n\n          const [storedToken, storedCode] = storedData.split(':');\n\n          if (storedToken !== credentials.token || storedCode !== credentials.code) {\n            return null;\n          }\n\n          // 验证成功，更新用户状态\n          user.emailVerified = true;\n          user.emailVerificationToken = undefined;\n          user.emailVerificationExpires = undefined;\n          user.lastLoginAt = new Date();\n\n          // 如果用户没有邮箱账户记录，添加一个\n          const hasEmailAccount = user.accounts.some((acc: any) => acc.provider === 'email');\n          if (!hasEmailAccount) {\n            user.accounts.push({\n              provider: 'email',\n              providerId: 'email',\n              providerAccountId: user.email,\n            });\n          }\n\n          await user.save();\n\n          return {\n            id: user._id.toString(),\n            email: user.email,\n            name: user.name,\n            image: user.avatar,\n            role: user.role,\n          };\n        } catch (error) {\n          console.error('Email code authorization error:', error);\n          return null;\n        }\n      },\n    }),\n  ],\n  session: {\n    strategy: 'jwt',\n  },\n  callbacks: {\n    async signIn({ user, account, profile }) {\n      // 对于credentials provider，用户已经在authorize中处理过了\n      if (account?.provider === 'email-code') {\n        return true;\n      }\n      console.log('auth callback, Sign in user:..........', user);\n      await dbConnect();\n\n      try {\n        // 查找或创建用户（仅用于OAuth providers）\n        let existingUser = await User.findOne({ email: user.email });\n\n        if (!existingUser) {\n          // 创建新用户\n          existingUser = new User({\n            email: user.email,\n            name: user.name || profile?.name || 'User',\n            avatar: user.image || profile?.image,\n            emailVerified: true, // OAuth登录默认已验证\n            lastLoginAt: new Date(),\n          });\n          await existingUser.save();\n        } else {\n          // 更新最后登录时间\n          existingUser.lastLoginAt = new Date();\n          await existingUser.save();\n        }\n\n        // 添加或更新账户信息\n        if (account && account.provider !== 'email-code') {\n          existingUser.addAccount({\n            provider: account.provider as 'google' | 'github',\n            providerId: account.provider,\n            providerAccountId: account.providerAccountId || account.id || '',\n            accessToken: account.access_token,\n            refreshToken: account.refresh_token,\n            expiresAt: account.expires_at ? new Date(account.expires_at * 1000) : undefined,\n          });\n          await existingUser.save();\n        }\n\n        return true;\n      } catch (error) {\n        console.error('Sign in error:', error);\n        return false;\n      }\n    },\n    async jwt({ token, user }) {\n      if (user) {\n        // 对于credentials provider，user对象已经包含了我们需要的信息\n        token.userId = user.id;\n        token.role = (user as any).role || 'user';\n      }\n      return token;\n    },\n    async session({ session, token }) {\n      if (token && session.user) {\n        (session.user as any).id = token.userId as string;\n        (session.user as any).role = token.role as string;\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n"], "names": [], "mappings": ";;;AACA;AACA;AACA;AACA;AACA;;;;;;AAGO,MAAM,cAA+B;IAC1C,iDAAiD;IACjD,mCAAmC;IAEnC,mBAAmB;IACnB,wCAAwC;IACxC,mBAAmB;IACnB,GAAI,oDAAyB,iBAAiB;IAG9C,CAAC;IAED,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;YACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;QACA,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,IAAI;YACJ,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,MAAM;oBAAE,OAAO;oBAAQ,MAAM;gBAAO;gBACpC,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAO;YACxC;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,QAAQ,CAAC,aAAa,OAAO;oBACpE,OAAO;gBACT;gBAEA,IAAI;oBACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;oBAEd,OAAO;oBACP,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;wBAC9B,OAAO,YAAY,KAAK,CAAC,WAAW;wBACpC,0BAA0B;4BAAE,KAAK,IAAI;wBAAO;oBAC9C;oBAEA,IAAI,CAAC,MAAM;wBACT,OAAO;oBACT;oBAEA,cAAc;oBACd,MAAM,aAAa,KAAK,sBAAsB;oBAC9C,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,CAAC,MAAM;wBAC5C,OAAO;oBACT;oBAEA,MAAM,CAAC,aAAa,WAAW,GAAG,WAAW,KAAK,CAAC;oBAEnD,IAAI,gBAAgB,YAAY,KAAK,IAAI,eAAe,YAAY,IAAI,EAAE;wBACxE,OAAO;oBACT;oBAEA,cAAc;oBACd,KAAK,aAAa,GAAG;oBACrB,KAAK,sBAAsB,GAAG;oBAC9B,KAAK,wBAAwB,GAAG;oBAChC,KAAK,WAAW,GAAG,IAAI;oBAEvB,oBAAoB;oBACpB,MAAM,kBAAkB,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,MAAa,IAAI,QAAQ,KAAK;oBAC1E,IAAI,CAAC,iBAAiB;wBACpB,KAAK,QAAQ,CAAC,IAAI,CAAC;4BACjB,UAAU;4BACV,YAAY;4BACZ,mBAAmB,KAAK,KAAK;wBAC/B;oBACF;oBAEA,MAAM,KAAK,IAAI;oBAEf,OAAO;wBACL,IAAI,KAAK,GAAG,CAAC,QAAQ;wBACrB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI;wBACf,OAAO,KAAK,MAAM;wBAClB,MAAM,KAAK,IAAI;oBACjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,mCAAmC;oBACjD,OAAO;gBACT;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,QAAO,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;YACrC,6CAA6C;YAC7C,IAAI,SAAS,aAAa,cAAc;gBACtC,OAAO;YACT;YACA,QAAQ,GAAG,CAAC,0CAA0C;YACtD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YAEd,IAAI;gBACF,8BAA8B;gBAC9B,IAAI,eAAe,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,KAAK,KAAK;gBAAC;gBAE1D,IAAI,CAAC,cAAc;oBACjB,QAAQ;oBACR,eAAe,IAAI,uHAAA,CAAA,UAAI,CAAC;wBACtB,OAAO,KAAK,KAAK;wBACjB,MAAM,KAAK,IAAI,IAAI,SAAS,QAAQ;wBACpC,QAAQ,KAAK,KAAK,IAAI,SAAS;wBAC/B,eAAe;wBACf,aAAa,IAAI;oBACnB;oBACA,MAAM,aAAa,IAAI;gBACzB,OAAO;oBACL,WAAW;oBACX,aAAa,WAAW,GAAG,IAAI;oBAC/B,MAAM,aAAa,IAAI;gBACzB;gBAEA,YAAY;gBACZ,IAAI,WAAW,QAAQ,QAAQ,KAAK,cAAc;oBAChD,aAAa,UAAU,CAAC;wBACtB,UAAU,QAAQ,QAAQ;wBAC1B,YAAY,QAAQ,QAAQ;wBAC5B,mBAAmB,QAAQ,iBAAiB,IAAI,QAAQ,EAAE,IAAI;wBAC9D,aAAa,QAAQ,YAAY;wBACjC,cAAc,QAAQ,aAAa;wBACnC,WAAW,QAAQ,UAAU,GAAG,IAAI,KAAK,QAAQ,UAAU,GAAG,QAAQ;oBACxE;oBACA,MAAM,aAAa,IAAI;gBACzB;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB;gBAChC,OAAO;YACT;QACF;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,4CAA4C;gBAC5C,MAAM,MAAM,GAAG,KAAK,EAAE;gBACtB,MAAM,IAAI,GAAG,AAAC,KAAa,IAAI,IAAI;YACrC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACxB,QAAQ,IAAI,CAAS,EAAE,GAAG,MAAM,MAAM;gBACtC,QAAQ,IAAI,CAAS,IAAI,GAAG,MAAM,IAAI;YACzC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC", "debugId": null}}, {"offset": {"line": 557, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts"], "sourcesContent": ["// 支持的语言列表\nexport const locales = ['en', 'zh'] as const;\nexport type Locale = (typeof locales)[number];\n\n// 默认语言\nexport const defaultLocale: Locale = 'en';\n\n// 语言显示名称\nexport const localeNames: Record<Locale, string> = {\n  zh: '中文',\n  en: 'English',\n};\n\n// 验证语言是否有效\nexport function isValidLocale(locale: string): locale is Locale {\n  return locales.includes(locale as Locale);\n}\n"], "names": [], "mappings": "AAAA,UAAU;;;;;;;AACH,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,gBAAwB;AAG9B,MAAM,cAAsC;IACjD,IAAI;IACJ,IAAI;AACN;AAGO,SAAS,cAAc,MAAc;IAC1C,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "debugId": null}}, {"offset": {"line": 582, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server';\nimport { locales, defaultLocale, type Locale } from './config';\nimport { notFound } from 'next/navigation';\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locale || !locales.find(l => l.toString() === locale?.toString())) {\n    locale = defaultLocale;\n  }\n\n  return {\n    locale,\n    messages: (await import(`./messages/${locale}.json`)).default\n  };\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;;;uCAGe,CAAA,GAAA,4PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,UAAU,CAAC,uHAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,OAAO,QAAQ,aAAa;QACtE,SAAS,uHAAA,CAAA,gBAAa;IACxB;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,WAAW,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IAC/D;AACF", "debugId": null}}, {"offset": {"line": 614, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts"], "sourcesContent": ["// 国际化分类配置文件\n// 支持多语言的AI工具分类配置\n\nimport { useTranslations } from 'next-intl';\nimport { getTranslations } from 'next-intl/server';\n\nexport interface CategoryConfig {\n  slug: string;\n  name: string;\n  description: string;\n  icon: string;\n  color: string;\n}\n\nexport interface CategoryOption {\n  value: string;\n  label: string;\n}\n\n// 分类的基础配置（不包含翻译文本）\nexport const CATEGORY_BASE_CONFIGS = [\n  {\n    slug: 'productivity',\n    icon: '⚡',\n    color: '#14B8A6'\n  },\n  {\n    slug: 'education',\n    icon: '📚',\n    color: '#A855F7'\n  },\n  {\n    slug: 'text-generation',\n    icon: '📝',\n    color: '#3B82F6'\n  },\n  {\n    slug: 'image-generation',\n    icon: '🎨',\n    color: '#10B981'\n  },\n  {\n    slug: 'code-generation',\n    icon: '💻',\n    color: '#8B5CF6'\n  },\n  {\n    slug: 'audio-processing',\n    icon: '🎵',\n    color: '#EF4444'\n  },\n  {\n    slug: 'video-editing',\n    icon: '🎬',\n    color: '#06B6D4'\n  },\n  {\n    slug: 'translation',\n    icon: '🌐',\n    color: '#84CC16'\n  },\n  {\n    slug: 'search-engines',\n    icon: '🔍',\n    color: '#F97316'\n  },\n  {\n    slug: 'marketing',\n    icon: '📈',\n    color: '#EC4899'\n  },\n  {\n    slug: 'customer-service',\n    icon: '🎧',\n    color: '#F59E0B'\n  },\n  {\n    slug: 'data-analysis',\n    icon: '📊',\n    color: '#F59E0B'\n  }\n];\n\n// 客户端钩子：获取国际化的分类配置\nexport function useCategoryConfigs(): CategoryConfig[] {\n  const t = useTranslations('categories');\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 客户端钩子：获取分类选项（用于下拉框等）\nexport function useCategoryOptions(): CategoryOption[] {\n  const configs = useCategoryConfigs();\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 客户端钩子：获取包含\"所有分类\"选项的分类选项\nexport function useCategoryOptionsWithAll(): CategoryOption[] {\n  const t = useTranslations('categories');\n  const options = useCategoryOptions();\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 客户端钩子：获取分类名称\nexport function useCategoryName(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 客户端钩子：获取分类描述\nexport function useCategoryDescription(slug: string): string {\n  const t = useTranslations('categories');\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取国际化的分类配置\nexport async function getCategoryConfigs(locale?: string): Promise<CategoryConfig[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  \n  return CATEGORY_BASE_CONFIGS.map(config => ({\n    slug: config.slug,\n    name: t(`category_names.${config.slug}`),\n    description: t(`category_descriptions.${config.slug}`),\n    icon: config.icon,\n    color: config.color\n  }));\n}\n\n// 服务器端函数：获取分类选项\nexport async function getCategoryOptions(locale?: string): Promise<CategoryOption[]> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.map(config => ({\n    value: config.slug,\n    label: config.name\n  }));\n}\n\n// 服务器端函数：获取包含\"所有分类\"选项的分类选项\nexport async function getCategoryOptionsWithAll(locale?: string): Promise<CategoryOption[]> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  const options = await getCategoryOptions(locale);\n  \n  return [\n    { value: '', label: t('all_categories') },\n    ...options\n  ];\n}\n\n// 服务器端函数：获取分类名称\nexport async function getCategoryName(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  return t(`category_names.${slug}`) || slug;\n}\n\n// 服务器端函数：获取分类描述\nexport async function getCategoryDescription(slug: string, locale?: string): Promise<string> {\n  const t = await getTranslations({ locale: locale || 'en', namespace: 'categories' });\n  return t(`category_descriptions.${slug}`) || '';\n}\n\n// 服务器端函数：获取分类配置\nexport async function getCategoryConfig(slug: string, locale?: string): Promise<CategoryConfig | undefined> {\n  const configs = await getCategoryConfigs(locale);\n  return configs.find(config => config.slug === slug);\n}\n\n// 验证分类是否存在的辅助函数\nexport function isValidCategory(slug: string): boolean {\n  return CATEGORY_BASE_CONFIGS.some(config => config.slug === slug);\n}\n\n// 获取所有分类slug的数组\nexport const CATEGORY_SLUGS = CATEGORY_BASE_CONFIGS.map(config => config.slug);\n\n// 分类元数据映射（slug -> 基础配置）\nexport const CATEGORY_BASE_METADATA: Record<string, typeof CATEGORY_BASE_CONFIGS[0]> = \n  CATEGORY_BASE_CONFIGS.reduce((acc, config) => {\n    acc[config.slug] = config;\n    return acc;\n  }, {} as Record<string, typeof CATEGORY_BASE_CONFIGS[0]>);\n"], "names": [], "mappings": "AAAA,YAAY;AACZ,iBAAiB;;;;;;;;;;;;;;;;;;AAEjB;AACA;;;AAgBO,MAAM,wBAAwB;IACnC;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,UAAU;IAChB,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,SAAS;IACd,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,UAAU;IAEhB,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,SAAS,gBAAgB,IAAY;IAC1C,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,SAAS,uBAAuB,IAAY;IACjD,MAAM,IAAI,CAAA,GAAA,gPAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAElF,OAAO,sBAAsB,GAAG,CAAC,CAAA,SAAU,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,EAAE,CAAC,eAAe,EAAE,OAAO,IAAI,EAAE;YACvC,aAAa,EAAE,CAAC,sBAAsB,EAAE,OAAO,IAAI,EAAE;YACrD,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;QACrB,CAAC;AACH;AAGO,eAAe,mBAAmB,MAAe;IACtD,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;YAC5B,OAAO,OAAO,IAAI;YAClB,OAAO,OAAO,IAAI;QACpB,CAAC;AACH;AAGO,eAAe,0BAA0B,MAAe;IAC7D,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAClF,MAAM,UAAU,MAAM,mBAAmB;IAEzC,OAAO;QACL;YAAE,OAAO;YAAI,OAAO,EAAE;QAAkB;WACrC;KACJ;AACH;AAGO,eAAe,gBAAgB,IAAY,EAAE,MAAe;IACjE,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAClF,OAAO,EAAE,CAAC,eAAe,EAAE,MAAM,KAAK;AACxC;AAGO,eAAe,uBAAuB,IAAY,EAAE,MAAe;IACxE,MAAM,IAAI,MAAM,CAAA,GAAA,0PAAA,CAAA,kBAAe,AAAD,EAAE;QAAE,QAAQ,UAAU;QAAM,WAAW;IAAa;IAClF,OAAO,EAAE,CAAC,sBAAsB,EAAE,MAAM,KAAK;AAC/C;AAGO,eAAe,kBAAkB,IAAY,EAAE,MAAe;IACnE,MAAM,UAAU,MAAM,mBAAmB;IACzC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAChD;AAGO,SAAS,gBAAgB,IAAY;IAC1C,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC9D;AAGO,MAAM,iBAAiB,sBAAsB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;AAGtE,MAAM,yBACX,sBAAsB,MAAM,CAAC,CAAC,KAAK;IACjC,GAAG,CAAC,OAAO,IAAI,CAAC,GAAG;IACnB,OAAO;AACT,GAAG,CAAC", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/upload-config.ts"], "sourcesContent": ["/**\n * 上传配置和字段长度限制\n */\n\n// 字段长度限制配置\nexport const FIELD_LIMITS = {\n  // 工具基本信息字段\n  TOOL_NAME: {\n    min: 2,\n    max: 40,\n    label: 'Tool Name'\n  },\n  TAGLINE: {\n    min: 4,\n    max: 80,\n    label: 'Tagline'\n  },\n  DESCRIPTION: {\n    min: 10,\n    max: 8000,\n    label: 'Description'\n  },\n  LONG_DESCRIPTION: {\n    min: 0,\n    max: 3000,\n    label: 'Long Description'\n  },\n  PRICING_DETAILS: {\n    min: 0,\n    max: 500,\n    label: 'Pricing Details'\n  },\n  WEBSITE_URL: {\n    min: 10,\n    max: 100,\n    label: 'Website URL'\n  },\n  \n  // 用户相关字段\n  USER_NAME: {\n    min: 1,\n    max: 40,\n    label: 'User Name'\n  },\n  USER_BIO: {\n    min: 0,\n    max: 1000,\n    label: 'User Bio'\n  },\n  \n  // 评论字段\n  COMMENT: {\n    min: 1,\n    max: 1000,\n    label: 'Comment'\n  }\n} as const;\n\n// 上传目录配置\nexport const UPLOAD_CONFIG = {\n  // 基础上传目录（相对于 public 目录）\n  BASE_DIR: process.env.UPLOAD_BASE_DIR || 'uploads',\n  \n  // 各类文件的子目录\n  DIRECTORIES: {\n    LOGOS: 'logos',\n    AVATARS: 'avatars',\n    SCREENSHOTS: 'screenshots',\n    TEMP: 'temp'\n  },\n  \n  // 文件大小限制（字节）\n  FILE_SIZE_LIMITS: {\n    LOGO: 5 * 1024 * 1024, // 5MB\n    AVATAR: 5 * 1024 * 1024, // 5MB\n    SCREENSHOT: 10 * 1024 * 1024, // 10MB\n  },\n  \n  // 允许的文件类型\n  ALLOWED_TYPES: {\n    IMAGES: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],\n  },\n  \n  // 文件命名规则\n  NAMING: {\n    LOGO_PREFIX: 'logo_',\n    AVATAR_PREFIX: 'avatar_',\n    SCREENSHOT_PREFIX: 'screenshot_',\n    TIMESTAMP_FORMAT: 'timestamp_random' // timestamp + random string\n  }\n} as const;\n\n// 获取完整的上传目录路径\nexport function getUploadDir(subDir: keyof typeof UPLOAD_CONFIG.DIRECTORIES): string {\n  const baseDir = UPLOAD_CONFIG.BASE_DIR;\n  const subDirectory = UPLOAD_CONFIG.DIRECTORIES[subDir];\n  return `${baseDir}/${subDirectory}`;\n}\n\n// 获取文件的URL路径\nexport function getFileUrl(subDir: keyof typeof UPLOAD_CONFIG.DIRECTORIES, filename: string): string {\n  const subDirectory = UPLOAD_CONFIG.DIRECTORIES[subDir];\n  return `/api/uploads/${subDirectory}/${filename}`;\n}\n\n// 生成唯一文件名\nexport function generateFileName(\n  prefix: string,\n  originalName: string,\n  namingType: string = UPLOAD_CONFIG.NAMING.TIMESTAMP_FORMAT\n): string {\n  const timestamp = Date.now();\n  const randomString = Math.random().toString(36).substring(2, 15);\n  const fileExtension = originalName.split('.').pop();\n  \n  switch (namingType) {\n    case 'timestamp_random':\n      return `${prefix}${timestamp}_${randomString}.${fileExtension}`;\n    default:\n      return `${prefix}${timestamp}_${randomString}.${fileExtension}`;\n  }\n}\n\n// 验证字段长度\nexport function validateFieldLength(\n  fieldName: keyof typeof FIELD_LIMITS,\n  value: string\n): { isValid: boolean; error?: string } {\n  const limits = FIELD_LIMITS[fieldName];\n  if (!limits) {\n    return { isValid: true };\n  }\n  \n  const length = value.trim().length;\n  \n  if (length < limits.min) {\n    return {\n      isValid: false,\n      error: `${limits.label} must be at least ${limits.min} characters long`\n    };\n  }\n  \n  if (length > limits.max) {\n    return {\n      isValid: false,\n      error: `${limits.label} cannot exceed ${limits.max} characters`\n    };\n  }\n  \n  return { isValid: true };\n}\n\n// 验证文件大小\nexport function validateFileSize(\n  fileType: keyof typeof UPLOAD_CONFIG.FILE_SIZE_LIMITS,\n  fileSize: number\n): { isValid: boolean; error?: string } {\n  const limit = UPLOAD_CONFIG.FILE_SIZE_LIMITS[fileType];\n  \n  if (fileSize > limit) {\n    const limitMB = Math.round(limit / (1024 * 1024));\n    return {\n      isValid: false,\n      error: `File size cannot exceed ${limitMB}MB`\n    };\n  }\n  \n  return { isValid: true };\n}\n\n// 验证文件类型\nexport function validateFileType(\n  fileType: string,\n  allowedTypes: readonly string[]\n): { isValid: boolean; error?: string } {\n  if (!allowedTypes.includes(fileType)) {\n    return {\n      isValid: false,\n      error: `File type ${fileType} is not allowed. Allowed types: ${allowedTypes.join(', ')}`\n    };\n  }\n  \n  return { isValid: true };\n}\n\n\n// 验证文件上传的类型和大小\nexport function validateFileUpload(\n  file: { type: string; size: number },\n  fileCategory: keyof typeof UPLOAD_CONFIG.FILE_SIZE_LIMITS\n): { isValid: boolean; error?: string } {\n  // 验证大小\n  const sizeValidation = validateFileSize(fileCategory, file.size);\n  if (!sizeValidation.isValid) {\n    return sizeValidation;\n  }\n\n  // 根据类别选择允许的类型，目前只有 IMAGES 可选，可根据需要扩展\n  const allowedTypes = UPLOAD_CONFIG.ALLOWED_TYPES.IMAGES;\n  const typeValidation = validateFileType(file.type, allowedTypes);\n  if (!typeValidation.isValid) {\n    return typeValidation;\n  }\n\n  return { isValid: true };\n}"], "names": [], "mappings": "AAAA;;CAEC,GAED,WAAW;;;;;;;;;;;;AACJ,MAAM,eAAe;IAC1B,WAAW;IACX,WAAW;QACT,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,SAAS;QACP,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,aAAa;QACX,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,kBAAkB;QAChB,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,iBAAiB;QACf,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,aAAa;QACX,KAAK;QACL,KAAK;QACL,OAAO;IACT;IAEA,SAAS;IACT,WAAW;QACT,KAAK;QACL,KAAK;QACL,OAAO;IACT;IACA,UAAU;QACR,KAAK;QACL,KAAK;QACL,OAAO;IACT;IAEA,OAAO;IACP,SAAS;QACP,KAAK;QACL,KAAK;QACL,OAAO;IACT;AACF;AAGO,MAAM,gBAAgB;IAC3B,wBAAwB;IACxB,UAAU,QAAQ,GAAG,CAAC,eAAe,IAAI;IAEzC,WAAW;IACX,aAAa;QACX,OAAO;QACP,SAAS;QACT,aAAa;QACb,MAAM;IACR;IAEA,aAAa;IACb,kBAAkB;QAChB,MAAM,IAAI,OAAO;QACjB,QAAQ,IAAI,OAAO;QACnB,YAAY,KAAK,OAAO;IAC1B;IAEA,UAAU;IACV,eAAe;QACb,QAAQ;YAAC;YAAc;YAAa;YAAa;YAAa;SAAa;IAC7E;IAEA,SAAS;IACT,QAAQ;QACN,aAAa;QACb,eAAe;QACf,mBAAmB;QACnB,kBAAkB,mBAAmB,4BAA4B;IACnE;AACF;AAGO,SAAS,aAAa,MAA8C;IACzE,MAAM,UAAU,cAAc,QAAQ;IACtC,MAAM,eAAe,cAAc,WAAW,CAAC,OAAO;IACtD,OAAO,GAAG,QAAQ,CAAC,EAAE,cAAc;AACrC;AAGO,SAAS,WAAW,MAA8C,EAAE,QAAgB;IACzF,MAAM,eAAe,cAAc,WAAW,CAAC,OAAO;IACtD,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,UAAU;AACnD;AAGO,SAAS,iBACd,MAAc,EACd,YAAoB,EACpB,aAAqB,cAAc,MAAM,CAAC,gBAAgB;IAE1D,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,eAAe,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAC7D,MAAM,gBAAgB,aAAa,KAAK,CAAC,KAAK,GAAG;IAEjD,OAAQ;QACN,KAAK;YACH,OAAO,GAAG,SAAS,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,eAAe;QACjE;YACE,OAAO,GAAG,SAAS,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,eAAe;IACnE;AACF;AAGO,SAAS,oBACd,SAAoC,EACpC,KAAa;IAEb,MAAM,SAAS,YAAY,CAAC,UAAU;IACtC,IAAI,CAAC,QAAQ;QACX,OAAO;YAAE,SAAS;QAAK;IACzB;IAEA,MAAM,SAAS,MAAM,IAAI,GAAG,MAAM;IAElC,IAAI,SAAS,OAAO,GAAG,EAAE;QACvB,OAAO;YACL,SAAS;YACT,OAAO,GAAG,OAAO,KAAK,CAAC,kBAAkB,EAAE,OAAO,GAAG,CAAC,gBAAgB,CAAC;QACzE;IACF;IAEA,IAAI,SAAS,OAAO,GAAG,EAAE;QACvB,OAAO;YACL,SAAS;YACT,OAAO,GAAG,OAAO,KAAK,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC,WAAW,CAAC;QACjE;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,SAAS,iBACd,QAAqD,EACrD,QAAgB;IAEhB,MAAM,QAAQ,cAAc,gBAAgB,CAAC,SAAS;IAEtD,IAAI,WAAW,OAAO;QACpB,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ,CAAC,OAAO,IAAI;QAC/C,OAAO;YACL,SAAS;YACT,OAAO,CAAC,wBAAwB,EAAE,QAAQ,EAAE,CAAC;QAC/C;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAGO,SAAS,iBACd,QAAgB,EAChB,YAA+B;IAE/B,IAAI,CAAC,aAAa,QAAQ,CAAC,WAAW;QACpC,OAAO;YACL,SAAS;YACT,OAAO,CAAC,UAAU,EAAE,SAAS,gCAAgC,EAAE,aAAa,IAAI,CAAC,OAAO;QAC1F;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAIO,SAAS,mBACd,IAAoC,EACpC,YAAyD;IAEzD,OAAO;IACP,MAAM,iBAAiB,iBAAiB,cAAc,KAAK,IAAI;IAC/D,IAAI,CAAC,eAAe,OAAO,EAAE;QAC3B,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,eAAe,cAAc,aAAa,CAAC,MAAM;IACvD,MAAM,iBAAiB,iBAAiB,KAAK,IAAI,EAAE;IACnD,IAAI,CAAC,eAAe,OAAO,EAAE;QAC3B,OAAO;IACT;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB", "debugId": null}}, {"offset": {"line": 989, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts"], "sourcesContent": ["import mongoose, { Document, Schema } from 'mongoose';\nimport { CATEGORY_SLUGS } from '@/constants/categories-i18n';\nimport { FIELD_LIMITS } from '@/constants/upload-config';\n\nexport interface ITool extends Document {\n  name: string;\n  tagline?: string; // 工具标语/副标题\n  description: string;\n  longDescription?: string;\n  website: string;\n  logo?: string;\n  category: string;\n  tags: string[];\n  pricing: 'free' | 'freemium' | 'paid';\n  pricingDetails?: string;\n  screenshots?: string[];\n  submittedBy: string; // User ID who submitted\n  submittedAt: Date;\n  launchDate?: Date; // 实际发布日期，一般等于selectedLaunchDate\n  status: 'draft' | 'pending' | 'approved' | 'rejected'; // 去掉published状态\n  reviewNotes?: string;\n  reviewedBy?: string; // Admin ID who reviewed\n  reviewedAt?: Date;\n\n  // 发布日期选择相关\n  launchDateSelected?: boolean; // 是否已选择发布日期\n  selectedLaunchDate?: Date; // 用户选择的发布日期\n  launchOption?: 'free' | 'paid'; // 发布选项：免费或付费\n\n  // 付费相关\n  paymentRequired?: boolean; // 是否需要付费\n  paymentAmount?: number; // 付费金额（分为单位）\n  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'; // 支付状态\n  orderId?: string; // 订单ID\n  paymentMethod?: string; // 支付方式\n  paidAt?: Date; // 支付完成时间\n\n  // Badge验证相关\n  badgeVerified?: boolean; // 是否已验证badge\n  badgeVerifiedAt?: Date; // badge验证时间\n\n  views: number;\n  likes: number;\n  likedBy: string[]; // 点赞用户ID列表\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst ToolSchema: Schema = new Schema({\n  name: {\n    type: String,\n    required: [true, 'Tool name is required'],\n    trim: true,\n    minlength: [FIELD_LIMITS.TOOL_NAME.min, `Tool name must be at least ${FIELD_LIMITS.TOOL_NAME.min} characters`],\n    maxlength: [FIELD_LIMITS.TOOL_NAME.max, `Tool name cannot exceed ${FIELD_LIMITS.TOOL_NAME.max} characters`]\n  },\n  tagline: {\n    type: String,\n    trim: true,\n    maxlength: [FIELD_LIMITS.TAGLINE.max, `Tagline cannot exceed ${FIELD_LIMITS.TAGLINE.max} characters`]\n  },\n  description: {\n    type: String,\n    required: [true, 'Tool description is required'],\n    trim: true,\n    minlength: [FIELD_LIMITS.DESCRIPTION.min, `Description must be at least ${FIELD_LIMITS.DESCRIPTION.min} characters`],\n    maxlength: [FIELD_LIMITS.DESCRIPTION.max, `Description cannot exceed ${FIELD_LIMITS.DESCRIPTION.max} characters`]\n  },\n  longDescription: {\n    type: String,\n    trim: true,\n    maxlength: [FIELD_LIMITS.LONG_DESCRIPTION.max, `Long description cannot exceed ${FIELD_LIMITS.LONG_DESCRIPTION.max} characters`]\n  },\n  website: {\n    type: String,\n    required: [true, 'Website URL is required'],\n    trim: true,\n    maxlength: [FIELD_LIMITS.WEBSITE_URL.max, `Website URL cannot exceed ${FIELD_LIMITS.WEBSITE_URL.max} characters`],\n    validate: {\n      validator: function(v: string) {\n        return /^https?:\\/\\/.+/.test(v);\n      },\n      message: 'Please enter a valid URL'\n    }\n  },\n  logo: {\n    type: String,\n    trim: true\n  },\n  category: {\n    type: String,\n    required: [true, 'Category is required'],\n    enum: CATEGORY_SLUGS\n  },\n  tags: [{\n    type: String,\n    trim: true,\n    lowercase: true\n  }],\n  pricing: {\n    type: String,\n    required: [true, 'Pricing model is required'],\n    enum: ['free', 'freemium', 'paid']\n  },\n  pricingDetails: {\n    type: String,\n    trim: true,\n    maxlength: [FIELD_LIMITS.PRICING_DETAILS.max, `Pricing details cannot exceed ${FIELD_LIMITS.PRICING_DETAILS.max} characters`]\n  },\n  screenshots: [{\n    type: String,\n    trim: true\n  }],\n  submittedBy: {\n    type: String,\n    required: [true, 'Submitter ID is required'],\n    trim: true\n  },\n  submittedAt: {\n    type: Date,\n    default: Date.now\n  },\n  launchDate: {\n    type: Date\n  },\n  status: {\n    type: String,\n    required: true,\n    enum: ['draft', 'pending', 'approved', 'rejected'],\n    default: 'draft'\n  },\n  reviewNotes: {\n    type: String,\n    trim: true,\n    maxlength: [1000, 'Review notes cannot exceed 1000 characters']\n  },\n  reviewedBy: {\n    type: String,\n    trim: true\n  },\n  reviewedAt: {\n    type: Date\n  },\n\n  // 发布日期选择相关\n  launchDateSelected: {\n    type: Boolean,\n    default: false\n  },\n  selectedLaunchDate: {\n    type: Date\n  },\n  launchOption: {\n    type: String,\n    enum: ['free', 'paid']\n  },\n\n  // 付费相关\n  paymentRequired: {\n    type: Boolean,\n    default: false\n  },\n  paymentAmount: {\n    type: Number,\n    min: 0\n  },\n  paymentStatus: {\n    type: String,\n    enum: ['pending', 'completed', 'failed', 'refunded']\n  },\n  orderId: {\n    type: String,\n    trim: true\n  },\n  paymentMethod: {\n    type: String,\n    trim: true\n  },\n  paidAt: {\n    type: Date\n  },\n\n  // Badge验证相关\n  badgeVerified: {\n    type: Boolean,\n    default: false\n  },\n  badgeVerifiedAt: {\n    type: Date\n  },\n\n  views: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likes: {\n    type: Number,\n    default: 0,\n    min: 0\n  },\n  likedBy: [{\n    type: String,\n    trim: true\n  }],\n  isActive: {\n    type: Boolean,\n    default: true\n  }\n}, {\n  timestamps: true,\n  toJSON: { virtuals: true },\n  toObject: { virtuals: true }\n});\n\n// Indexes for better query performance\nToolSchema.index({ status: 1, isActive: 1 });\nToolSchema.index({ category: 1, status: 1 });\nToolSchema.index({ tags: 1, status: 1 });\nToolSchema.index({ submittedBy: 1 });\nToolSchema.index({ launchDate: -1 });\nToolSchema.index({ views: -1 });\nToolSchema.index({ likes: -1 });\n\n// Text search index\nToolSchema.index({\n  name: 'text',\n  tagline: 'text',\n  description: 'text',\n  longDescription: 'text',\n  tags: 'text'\n});\n\nexport default mongoose.models.Tool || mongoose.model<ITool>('Tool', ToolSchema);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AA+CA,MAAM,aAAqB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACpC,MAAM;QACJ,MAAM;QACN,UAAU;YAAC;YAAM;SAAwB;QACzC,MAAM;QACN,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG;YAAE,CAAC,2BAA2B,EAAE,sIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;QAC9G,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG;YAAE,CAAC,wBAAwB,EAAE,sIAAA,CAAA,eAAY,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;IAC7G;IACA,SAAS;QACP,MAAM;QACN,MAAM;QACN,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,GAAG;YAAE,CAAC,sBAAsB,EAAE,sIAAA,CAAA,eAAY,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;IACvG;IACA,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA+B;QAChD,MAAM;QACN,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;YAAE,CAAC,6BAA6B,EAAE,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;QACpH,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;YAAE,CAAC,0BAA0B,EAAE,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;IACnH;IACA,iBAAiB;QACf,MAAM;QACN,MAAM;QACN,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,GAAG;YAAE,CAAC,+BAA+B,EAAE,sIAAA,CAAA,eAAY,CAAC,gBAAgB,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;IAClI;IACA,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA0B;QAC3C,MAAM;QACN,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG;YAAE,CAAC,0BAA0B,EAAE,sIAAA,CAAA,eAAY,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;QACjH,UAAU;YACR,WAAW,SAAS,CAAS;gBAC3B,OAAO,iBAAiB,IAAI,CAAC;YAC/B;YACA,SAAS;QACX;IACF;IACA,MAAM;QACJ,MAAM;QACN,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;YAAC;YAAM;SAAuB;QACxC,MAAM,wIAAA,CAAA,iBAAc;IACtB;IACA,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,SAAS;QACP,MAAM;QACN,UAAU;YAAC;YAAM;SAA4B;QAC7C,MAAM;YAAC;YAAQ;YAAY;SAAO;IACpC;IACA,gBAAgB;QACd,MAAM;QACN,MAAM;QACN,WAAW;YAAC,sIAAA,CAAA,eAAY,CAAC,eAAe,CAAC,GAAG;YAAE,CAAC,8BAA8B,EAAE,sIAAA,CAAA,eAAY,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW,CAAC;SAAC;IAC/H;IACA,aAAa;QAAC;YACZ,MAAM;YACN,MAAM;QACR;KAAE;IACF,aAAa;QACX,MAAM;QACN,UAAU;YAAC;YAAM;SAA2B;QAC5C,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,SAAS,KAAK,GAAG;IACnB;IACA,YAAY;QACV,MAAM;IACR;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,MAAM;YAAC;YAAS;YAAW;YAAY;SAAW;QAClD,SAAS;IACX;IACA,aAAa;QACX,MAAM;QACN,MAAM;QACN,WAAW;YAAC;YAAM;SAA6C;IACjE;IACA,YAAY;QACV,MAAM;QACN,MAAM;IACR;IACA,YAAY;QACV,MAAM;IACR;IAEA,WAAW;IACX,oBAAoB;QAClB,MAAM;QACN,SAAS;IACX;IACA,oBAAoB;QAClB,MAAM;IACR;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAQ;SAAO;IACxB;IAEA,OAAO;IACP,iBAAiB;QACf,MAAM;QACN,SAAS;IACX;IACA,eAAe;QACb,MAAM;QACN,KAAK;IACP;IACA,eAAe;QACb,MAAM;QACN,MAAM;YAAC;YAAW;YAAa;YAAU;SAAW;IACtD;IACA,SAAS;QACP,MAAM;QACN,MAAM;IACR;IACA,eAAe;QACb,MAAM;QACN,MAAM;IACR;IACA,QAAQ;QACN,MAAM;IACR;IAEA,YAAY;IACZ,eAAe;QACb,MAAM;QACN,SAAS;IACX;IACA,iBAAiB;QACf,MAAM;IACR;IAEA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,OAAO;QACL,MAAM;QACN,SAAS;QACT,KAAK;IACP;IACA,SAAS;QAAC;YACR,MAAM;YACN,MAAM;QACR;KAAE;IACF,UAAU;QACR,MAAM;QACN,SAAS;IACX;AACF,GAAG;IACD,YAAY;IACZ,QAAQ;QAAE,UAAU;IAAK;IACzB,UAAU;QAAE,UAAU;IAAK;AAC7B;AAEA,uCAAuC;AACvC,WAAW,KAAK,CAAC;IAAE,QAAQ;IAAG,UAAU;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,UAAU;IAAG,QAAQ;AAAE;AAC1C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAG,QAAQ;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,aAAa;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,YAAY,CAAC;AAAE;AAClC,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAC7B,WAAW,KAAK,CAAC;IAAE,OAAO,CAAC;AAAE;AAE7B,oBAAoB;AACpB,WAAW,KAAK,CAAC;IACf,MAAM;IACN,SAAS;IACT,aAAa;IACb,iBAAiB;IACjB,MAAM;AACR;uCAEe,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAQ,QAAQ", "debugId": null}}, {"offset": {"line": 1272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts"], "sourcesContent": ["// API 响应消息国际化\nexport interface ApiMessages {\n  // 通用错误消息\n  errors: {\n    fetch_failed: string;\n    network_error: string;\n    validation_failed: string;\n    unauthorized: string;\n    forbidden: string;\n    not_found: string;\n    internal_error: string;\n    invalid_request: string;\n    missing_required_field: string;\n    duplicate_name: string;\n    create_failed: string;\n    update_failed: string;\n    delete_failed: string;\n  };\n  \n  // 成功消息\n  success: {\n    created: string;\n    updated: string;\n    deleted: string;\n    submitted: string;\n    approved: string;\n    rejected: string;\n    published: string;\n  };\n  \n  // 工具相关消息\n  tools: {\n    fetch_failed: string;\n    create_failed: string;\n    name_required: string;\n    description_required: string;\n    website_required: string;\n    category_required: string;\n    pricing_required: string;\n    submitter_name_required: string;\n    submitter_email_required: string;\n    name_exists: string;\n    website_exists: string;\n    submit_success: string;\n    approve_success: string;\n    reject_success: string;\n    approve_failed: string;\n    reject_failed: string;\n    not_found: string;\n    update_success: string;\n    update_failed: string;\n    launch_date_already_set: string;\n    free_date_restriction: string;\n    paid_date_restriction: string;\n    launch_date_set_success: string;\n    edit_not_allowed: string;\n    already_published: string;\n    launch_date_updated: string;\n    publish_success: string;\n    publish_failed: string;\n  };\n  \n  // 用户相关消息\n  user: {\n    not_found: string;\n    unauthorized: string;\n    profile_update_success: string;\n    profile_update_failed: string;\n  };\n  \n  // 认证相关消息\n  auth: {\n    invalid_credentials: string;\n    code_sent: string;\n    code_send_failed: string;\n    invalid_code: string;\n    login_success: string;\n    login_failed: string;\n    logout_success: string;\n  };\n  \n  // 支付相关消息\n  payment: {\n    create_intent_failed: string;\n    payment_success: string;\n    payment_failed: string;\n    webhook_error: string;\n    order_created: string;\n    upgrade_order_created: string;\n  };\n\n  // 上传相关消息\n  upload: {\n    no_file: string;\n    invalid_type: string;\n    file_too_large: string;\n    upload_failed: string;\n    upload_success: string;\n  };\n}\n\n// 中文消息\nexport const zhMessages: ApiMessages = {\n  errors: {\n    fetch_failed: '获取数据失败',\n    network_error: '网络错误，请重试',\n    validation_failed: '验证失败',\n    unauthorized: '未授权访问',\n    forbidden: '禁止访问',\n    not_found: '资源未找到',\n    internal_error: '服务器内部错误',\n    invalid_request: '无效请求',\n    missing_required_field: '缺少必需字段',\n    duplicate_name: '名称已存在',\n    create_failed: '创建失败',\n    update_failed: '更新失败',\n    delete_failed: '删除失败',\n  },\n  success: {\n    created: '创建成功',\n    updated: '更新成功',\n    deleted: '删除成功',\n    submitted: '提交成功',\n    approved: '批准成功',\n    rejected: '拒绝成功',\n    published: '发布成功',\n  },\n  tools: {\n    fetch_failed: '获取工具列表失败',\n    create_failed: '创建工具失败',\n    name_required: 'name 是必需的',\n    description_required: 'description 是必需的',\n    website_required: 'website 是必需的',\n    category_required: 'category 是必需的',\n    pricing_required: 'pricing 是必需的',\n    submitter_name_required: 'submitterName 是必需的',\n    submitter_email_required: 'submitterEmail 是必需的',\n    name_exists: '该工具名称已存在',\n    website_exists: '该网站已存在',\n    submit_success: '工具提交成功，等待审核',\n    approve_success: '工具审核通过',\n    reject_success: '工具已拒绝',\n    approve_failed: '审核通过失败',\n    reject_failed: '拒绝失败',\n    not_found: '工具未找到',\n    update_success: '工具更新成功',\n    update_failed: '工具更新失败',\n    launch_date_already_set: '此工具已经选择了发布日期',\n    free_date_restriction: '免费选项只能选择一个月后的日期',\n    paid_date_restriction: '付费选项最早只能选择明天的日期',\n    launch_date_set_success: '发布日期设置成功，工具已进入审核队列',\n    edit_not_allowed: '当前状态不允许修改发布日期',\n    already_published: '工具已发布，无法修改发布日期',\n    launch_date_updated: '发布日期修改成功',\n    publish_success: '工具发布成功',\n    publish_failed: '工具发布失败',\n  },\n  user: {\n    not_found: '用户未找到',\n    unauthorized: '用户未授权',\n    profile_update_success: '个人资料更新成功',\n    profile_update_failed: '个人资料更新失败',\n  },\n  auth: {\n    invalid_credentials: '无效的登录凭据',\n    code_sent: '验证码已发送',\n    code_send_failed: '验证码发送失败',\n    invalid_code: '无效的验证码',\n    login_success: '登录成功',\n    login_failed: '登录失败',\n    logout_success: '退出成功',\n  },\n  payment: {\n    create_intent_failed: '创建支付意图失败',\n    payment_success: '支付成功',\n    payment_failed: '支付失败',\n    webhook_error: 'Webhook 处理错误',\n    order_created: '订单创建成功，请完成支付',\n    upgrade_order_created: '升级订单创建成功，请完成支付',\n  },\n  upload: {\n    no_file: '请选择要上传的文件',\n    invalid_type: '只支持 JPEG、PNG、GIF、WebP 格式的图片',\n    file_too_large: '文件大小不能超过 5MB',\n    upload_failed: '文件上传失败',\n    upload_success: '文件上传成功',\n  },\n};\n\n// 英文消息\nexport const enMessages: ApiMessages = {\n  errors: {\n    fetch_failed: 'Failed to fetch data',\n    network_error: 'Network error, please try again',\n    validation_failed: 'Validation failed',\n    unauthorized: 'Unauthorized access',\n    forbidden: 'Access forbidden',\n    not_found: 'Resource not found',\n    internal_error: 'Internal server error',\n    invalid_request: 'Invalid request',\n    missing_required_field: 'Missing required field',\n    duplicate_name: 'Name already exists',\n    create_failed: 'Creation failed',\n    update_failed: 'Update failed',\n    delete_failed: 'Deletion failed',\n  },\n  success: {\n    created: 'Created successfully',\n    updated: 'Updated successfully',\n    deleted: 'Deleted successfully',\n    submitted: 'Submitted successfully',\n    approved: 'Approved successfully',\n    rejected: 'Rejected successfully',\n    published: 'Published successfully',\n  },\n  tools: {\n    fetch_failed: 'Failed to fetch tools list',\n    create_failed: 'Failed to create tool',\n    name_required: 'name is required',\n    description_required: 'description is required',\n    website_required: 'website is required',\n    category_required: 'category is required',\n    pricing_required: 'pricing is required',\n    submitter_name_required: 'submitterName is required',\n    submitter_email_required: 'submitterEmail is required',\n    name_exists: 'Tool name already exists',\n    website_exists: 'Website already exists',\n    submit_success: 'Tool submitted successfully, awaiting review',\n    approve_success: 'Tool approved successfully',\n    reject_success: 'Tool rejected successfully',\n    approve_failed: 'Failed to approve tool',\n    reject_failed: 'Failed to reject tool',\n    not_found: 'Tool not found',\n    update_success: 'Tool updated successfully',\n    update_failed: 'Failed to update tool',\n    launch_date_already_set: 'This tool has already selected a launch date',\n    free_date_restriction: 'Free option can only select dates one month later',\n    paid_date_restriction: 'Paid option can only select dates from tomorrow',\n    launch_date_set_success: 'Launch date set successfully, tool entered review queue',\n    edit_not_allowed: 'Current status does not allow modifying launch date',\n    already_published: 'Tool already published, cannot modify launch date',\n    launch_date_updated: 'Launch date updated successfully',\n    publish_success: 'Tool published successfully',\n    publish_failed: 'Failed to publish tool',\n  },\n  user: {\n    not_found: 'User not found',\n    unauthorized: 'User unauthorized',\n    profile_update_success: 'Profile updated successfully',\n    profile_update_failed: 'Failed to update profile',\n  },\n  auth: {\n    invalid_credentials: 'Invalid credentials',\n    code_sent: 'Verification code sent',\n    code_send_failed: 'Failed to send verification code',\n    invalid_code: 'Invalid verification code',\n    login_success: 'Login successful',\n    login_failed: 'Login failed',\n    logout_success: 'Logout successful',\n  },\n  payment: {\n    create_intent_failed: 'Failed to create payment intent',\n    payment_success: 'Payment successful',\n    payment_failed: 'Payment failed',\n    webhook_error: 'Webhook processing error',\n    order_created: 'Order created successfully, please complete payment',\n    upgrade_order_created: 'Upgrade order created successfully, please complete payment',\n  },\n  upload: {\n    no_file: 'Please select a file to upload',\n    invalid_type: 'Only JPEG, PNG, GIF, WebP image formats are supported',\n    file_too_large: 'File size cannot exceed 5MB',\n    upload_failed: 'File upload failed',\n    upload_success: 'File uploaded successfully',\n  },\n};\n\n// 获取API消息的工具函数\nexport function getApiMessage(locale: 'zh' | 'en', key: string): string {\n  const messages = locale === 'zh' ? zhMessages : enMessages;\n  \n  // 支持嵌套键，如 'tools.fetch_failed'\n  const keys = key.split('.');\n  let message: any = messages;\n  \n  for (const k of keys) {\n    if (message && typeof message === 'object' && k in message) {\n      message = message[k];\n    } else {\n      // 如果找不到对应的键，返回默认的中文消息\n      return locale === 'zh' ? '操作失败' : 'Operation failed';\n    }\n  }\n  \n  return typeof message === 'string' ? message : (locale === 'zh' ? '操作失败' : 'Operation failed');\n}\n\n// 从请求头中获取语言偏好\nexport function getLocaleFromRequest(request: Request): 'zh' | 'en' {\n  // 首先检查自定义的 X-Locale 头\n  const xLocale = request.headers.get('x-locale');\n  if (xLocale === 'en' || xLocale === 'zh') {\n    return xLocale;\n  }\n\n  const acceptLanguage = request.headers.get('accept-language') || '';\n  const pathname = new URL(request.url).pathname;\n\n  // 然后检查URL路径中的语言前缀\n  if (pathname.startsWith('/en/')) {\n    return 'en';\n  } else if (pathname.startsWith('/zh/')) {\n    return 'zh';\n  }\n\n  // 最后检查Accept-Language头\n  if (acceptLanguage.includes('en')) {\n    return 'en';\n  }\n\n  // 默认返回中文\n  return 'zh';\n}\n"], "names": [], "mappings": "AAAA,cAAc;;;;;;;AAsGP,MAAM,aAA0B;IACrC,QAAQ;QACN,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,wBAAwB;QACxB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,SAAS;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,cAAc;QACd,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;QACzB,0BAA0B;QAC1B,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,yBAAyB;QACzB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IACA,MAAM;QACJ,WAAW;QACX,cAAc;QACd,wBAAwB;QACxB,uBAAuB;IACzB;IACA,MAAM;QACJ,qBAAqB;QACrB,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;IAClB;IACA,SAAS;QACP,sBAAsB;QACtB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,uBAAuB;IACzB;IACA,QAAQ;QACN,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF;AAGO,MAAM,aAA0B;IACrC,QAAQ;QACN,cAAc;QACd,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,wBAAwB;QACxB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,eAAe;IACjB;IACA,SAAS;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,WAAW;QACX,UAAU;QACV,UAAU;QACV,WAAW;IACb;IACA,OAAO;QACL,cAAc;QACd,eAAe;QACf,eAAe;QACf,sBAAsB;QACtB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,yBAAyB;QACzB,0BAA0B;QAC1B,aAAa;QACb,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,gBAAgB;QAChB,eAAe;QACf,WAAW;QACX,gBAAgB;QAChB,eAAe;QACf,yBAAyB;QACzB,uBAAuB;QACvB,uBAAuB;QACvB,yBAAyB;QACzB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,iBAAiB;QACjB,gBAAgB;IAClB;IACA,MAAM;QACJ,WAAW;QACX,cAAc;QACd,wBAAwB;QACxB,uBAAuB;IACzB;IACA,MAAM;QACJ,qBAAqB;QACrB,WAAW;QACX,kBAAkB;QAClB,cAAc;QACd,eAAe;QACf,cAAc;QACd,gBAAgB;IAClB;IACA,SAAS;QACP,sBAAsB;QACtB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,eAAe;QACf,uBAAuB;IACzB;IACA,QAAQ;QACN,SAAS;QACT,cAAc;QACd,gBAAgB;QAChB,eAAe;QACf,gBAAgB;IAClB;AACF;AAGO,SAAS,cAAc,MAAmB,EAAE,GAAW;IAC5D,MAAM,WAAW,WAAW,OAAO,aAAa;IAEhD,+BAA+B;IAC/B,MAAM,OAAO,IAAI,KAAK,CAAC;IACvB,IAAI,UAAe;IAEnB,KAAK,MAAM,KAAK,KAAM;QACpB,IAAI,WAAW,OAAO,YAAY,YAAY,KAAK,SAAS;YAC1D,UAAU,OAAO,CAAC,EAAE;QACtB,OAAO;YACL,sBAAsB;YACtB,OAAO,WAAW,OAAO,SAAS;QACpC;IACF;IAEA,OAAO,OAAO,YAAY,WAAW,UAAW,WAAW,OAAO,SAAS;AAC7E;AAGO,SAAS,qBAAqB,OAAgB;IACnD,sBAAsB;IACtB,MAAM,UAAU,QAAQ,OAAO,CAAC,GAAG,CAAC;IACpC,IAAI,YAAY,QAAQ,YAAY,MAAM;QACxC,OAAO;IACT;IAEA,MAAM,iBAAiB,QAAQ,OAAO,CAAC,GAAG,CAAC,sBAAsB;IACjE,MAAM,WAAW,IAAI,IAAI,QAAQ,GAAG,EAAE,QAAQ;IAE9C,kBAAkB;IAClB,IAAI,SAAS,UAAU,CAAC,SAAS;QAC/B,OAAO;IACT,OAAO,IAAI,SAAS,UAAU,CAAC,SAAS;QACtC,OAAO;IACT;IAEA,uBAAuB;IACvB,IAAI,eAAe,QAAQ,CAAC,OAAO;QACjC,OAAO;IACT;IAEA,SAAS;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1493, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/%5Bid%5D/like/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { getServerSession } from 'next-auth';\nimport { authOptions } from '@/lib/auth';\nimport dbConnect from '@/lib/mongodb';\nimport Tool from '@/models/Tool';\nimport User from '@/models/User';\nimport { getApiMessage, getLocaleFromRequest } from '@/lib/api-messages';\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    // 检查用户认证\n    const session = await getServerSession(authOptions);\n    const locale = getLocaleFromRequest(request);\n\n    if (!session?.user?.email) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.unauthorized') },\n        { status: 401 }\n      );\n    }\n\n    await dbConnect();\n\n    // 获取用户信息\n    const user = await User.findOne({ email: session.user.email });\n    if (!user) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'user.not_found') },\n        { status: 404 }\n      );\n    }\n\n    const { id: toolId } = await params;\n\n    // 查找工具\n    const tool = await Tool.findById(toolId);\n    if (!tool) {\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.not_found') },\n        { status: 404 }\n      );\n    }\n\n    // 检查请求体中是否有强制操作标识\n    const body = await request.json().catch(() => ({}));\n    const forceUnlike = body.forceUnlike === true;\n\n    // 检查用户是否已经点赞（以tool.likedBy为准）\n    const userIdStr = user._id.toString();\n    const currentlyLiked = tool.likedBy.find((lb: string) => lb.toString() === userIdStr);\n\n    let newLikedState: boolean;\n\n    if (forceUnlike) {\n      // 强制取消点赞\n      newLikedState = false;\n    } else {\n      // 切换点赞状态\n      newLikedState = !currentlyLiked;\n    }\n\n    if (newLikedState) {\n      // 添加点赞\n      if (!currentlyLiked) {\n        tool.likedBy.push(userIdStr);\n      }\n      if (!user.likedTools.includes(toolId)) {\n        user.likedTools.push(toolId);\n      }\n\n      tool.likes = tool.likes + 1;\n    } else {\n      // 取消点赞\n      tool.likedBy = tool.likedBy.filter((id: string) => id.toString() !== userIdStr);\n      user.likedTools = user.likedTools.filter((id: string) => id.toString() !== toolId);\n\n      tool.likes = tool.likes - 1;\n    }\n\n    // 保存更改\n    await tool.save();\n    await user.save();\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        liked: newLikedState,\n        likes: tool.likes\n      }\n    });\n\n  } catch (error) {\n    console.error('Like tool error:', error);\n    const locale = getLocaleFromRequest(request);\n    return NextResponse.json(\n      { success: false, message: getApiMessage(locale, 'errors.internal_error') },\n      { status: 500 }\n    );\n  }\n}\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions);\n\n    await dbConnect();\n\n    const { id: toolId } = await params;\n    const tool = await Tool.findById(toolId);\n\n    if (!tool) {\n      const locale = getLocaleFromRequest(request);\n      return NextResponse.json(\n        { success: false, message: getApiMessage(locale, 'tools.not_found') },\n        { status: 404 }\n      );\n    }\n\n    let liked = false;\n    if (session?.user?.email) {\n      const user = await User.findOne({ email: session.user.email });\n      if (user) {\n        // 以tool.likedBy为准检查点赞状态\n        liked = tool.likedBy.includes(user._id.toString());\n      }\n    }\n\n    // 确保likes数量与likedBy数组长度一致\n    const actualLikes = tool.likedBy.length;\n    if (tool.likes !== actualLikes) {\n      tool.likes = actualLikes;\n      await tool.save();\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        liked,\n        likes: tool.likes\n      }\n    });\n\n  } catch (error) {\n    console.error('Get like status error:', error);\n    const locale = getLocaleFromRequest(request);\n    return NextResponse.json(\n      { success: false, message: getApiMessage(locale, 'errors.internal_error') },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,SAAS;QACT,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAClD,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QAEpC,IAAI,CAAC,SAAS,MAAM,OAAO;YACzB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAqB,GACtE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,SAAS;QACT,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;YAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;QAAC;QAC5D,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAkB,GACnE;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG,MAAM;QAE7B,OAAO;QACP,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAmB,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,kBAAkB;QAClB,MAAM,OAAO,MAAM,QAAQ,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACjD,MAAM,cAAc,KAAK,WAAW,KAAK;QAEzC,8BAA8B;QAC9B,MAAM,YAAY,KAAK,GAAG,CAAC,QAAQ;QACnC,MAAM,iBAAiB,KAAK,OAAO,CAAC,IAAI,CAAC,CAAC,KAAe,GAAG,QAAQ,OAAO;QAE3E,IAAI;QAEJ,IAAI,aAAa;YACf,SAAS;YACT,gBAAgB;QAClB,OAAO;YACL,SAAS;YACT,gBAAgB,CAAC;QACnB;QAEA,IAAI,eAAe;YACjB,OAAO;YACP,IAAI,CAAC,gBAAgB;gBACnB,KAAK,OAAO,CAAC,IAAI,CAAC;YACpB;YACA,IAAI,CAAC,KAAK,UAAU,CAAC,QAAQ,CAAC,SAAS;gBACrC,KAAK,UAAU,CAAC,IAAI,CAAC;YACvB;YAEA,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG;QAC5B,OAAO;YACL,OAAO;YACP,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,KAAe,GAAG,QAAQ,OAAO;YACrE,KAAK,UAAU,GAAG,KAAK,UAAU,CAAC,MAAM,CAAC,CAAC,KAAe,GAAG,QAAQ,OAAO;YAE3E,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG;QAC5B;QAEA,OAAO;QACP,MAAM,KAAK,IAAI;QACf,MAAM,KAAK,IAAI;QAEf,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO;gBACP,OAAO,KAAK,KAAK;YACnB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAAyB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,IAAI,MAAM,EAAE,GAAG,MAAM;QAC7B,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,QAAQ,CAAC;QAEjC,IAAI,CAAC,MAAM;YACT,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;YACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YAAmB,GACpE;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,QAAQ;QACZ,IAAI,SAAS,MAAM,OAAO;YACxB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;gBAAE,OAAO,QAAQ,IAAI,CAAC,KAAK;YAAC;YAC5D,IAAI,MAAM;gBACR,wBAAwB;gBACxB,QAAQ,KAAK,OAAO,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,QAAQ;YACjD;QACF;QAEA,0BAA0B;QAC1B,MAAM,cAAc,KAAK,OAAO,CAAC,MAAM;QACvC,IAAI,KAAK,KAAK,KAAK,aAAa;YAC9B,KAAK,KAAK,GAAG;YACb,MAAM,KAAK,IAAI;QACjB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA,OAAO,KAAK,KAAK;YACnB;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,SAAS,CAAA,GAAA,+HAAA,CAAA,uBAAoB,AAAD,EAAE;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,SAAS,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAAyB,GAC1E;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}