exports.id=1158,exports.ids=[1158],exports.modules={471:(e,t,i)=>{Promise.resolve().then(i.bind(i,75788)),Promise.resolve().then(i.bind(i,80994))},5199:(e,t,i)=>{Promise.resolve().then(i.bind(i,39130)),Promise.resolve().then(i.bind(i,45196))},12909:(e,t,i)=>{"use strict";i.d(t,{N:()=>l});var r=i(36344),a=i(65752),n=i(13581),o=i(75745),s=i(17063);let l={...!1,providers:[(0,r.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET}),(0,a.A)({clientId:process.env.GITHUB_CLIENT_ID,clientSecret:process.env.GITHUB_CLIENT_SECRET}),(0,n.A)({id:"email-code",name:"Email Code",credentials:{email:{label:"Email",type:"email"},code:{label:"Code",type:"text"},token:{label:"Token",type:"text"}},async authorize(e){if(!e?.email||!e?.code||!e?.token)return null;try{await (0,o.A)();let t=await s.A.findOne({email:e.email.toLowerCase(),emailVerificationExpires:{$gt:new Date}});if(!t)return null;let i=t.emailVerificationToken;if(!i||!i.includes(":"))return null;let[r,a]=i.split(":");if(r!==e.token||a!==e.code)return null;return t.emailVerified=!0,t.emailVerificationToken=void 0,t.emailVerificationExpires=void 0,t.lastLoginAt=new Date,t.accounts.some(e=>"email"===e.provider)||t.accounts.push({provider:"email",providerId:"email",providerAccountId:t.email}),await t.save(),{id:t._id.toString(),email:t.email,name:t.name,image:t.avatar,role:t.role}}catch(e){return console.error("Email code authorization error:",e),null}}})],session:{strategy:"jwt"},callbacks:{async signIn({user:e,account:t,profile:i}){if(t?.provider==="email-code")return!0;console.log("auth callback, Sign in user:..........",e),await (0,o.A)();try{let r=await s.A.findOne({email:e.email});return r?r.lastLoginAt=new Date:r=new s.A({email:e.email,name:e.name||i?.name||"User",avatar:e.image||i?.image,emailVerified:!0,lastLoginAt:new Date}),await r.save(),t&&"email-code"!==t.provider&&(r.addAccount({provider:t.provider,providerId:t.provider,providerAccountId:t.providerAccountId||t.id||"",accessToken:t.access_token,refreshToken:t.refresh_token,expiresAt:t.expires_at?new Date(1e3*t.expires_at):void 0}),await r.save()),!0}catch(e){return console.error("Sign in error:",e),!1}},jwt:async({token:e,user:t})=>(t&&(e.userId=t.id,e.role=t.role||"user"),e),session:async({session:e,token:t})=>(t&&e.user&&(e.user.id=t.userId,e.user.role=t.role),e)},pages:{signIn:"/auth/signin",error:"/auth/error"},secret:process.env.NEXTAUTH_SECRET}},17063:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(56037),a=i.n(r);let n=new r.Schema({provider:{type:String,required:!0,enum:["google","github","email"]},providerId:{type:String,required:!0},providerAccountId:{type:String,required:!0},accessToken:String,refreshToken:String,expiresAt:Date},{_id:!1}),o=new r.Schema({email:{type:String,required:[!0,"Email is required"],unique:!0,trim:!0,lowercase:!0,validate:{validator:function(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},message:"Please enter a valid email address"}},name:{type:String,required:[!0,"Name is required"],trim:!0,maxlength:[100,"Name cannot exceed 100 characters"]},avatar:{type:String,trim:!0},role:{type:String,required:!0,enum:["user","admin"],default:"user"},isActive:{type:Boolean,default:!0},bio:{type:String,trim:!0,maxlength:[500,"Bio cannot exceed 500 characters"]},website:{type:String,trim:!0,validate:{validator:function(e){return!e||/^https?:\/\/.+/.test(e)},message:"Website must be a valid URL starting with http:// or https://"}},location:{type:String,trim:!0,maxlength:[100,"Location cannot exceed 100 characters"]},emailVerified:{type:Boolean,default:!1},emailVerificationToken:{type:String,trim:!0},emailVerificationExpires:{type:Date},accounts:[n],submittedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],likedTools:[{type:r.Schema.Types.ObjectId,ref:"Tool"}],comments:[{type:r.Schema.Types.ObjectId,ref:"Comment"}],lastLoginAt:{type:Date}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});o.index({email:1}),o.index({role:1}),o.index({emailVerificationToken:1}),o.index({"accounts.provider":1,"accounts.providerAccountId":1}),o.methods.addAccount=function(e){let t=this.accounts.find(t=>t.provider===e.provider&&t.providerAccountId===e.providerAccountId);t?Object.assign(t,e):this.accounts.push(e)},o.methods.removeAccount=function(e,t){this.accounts=this.accounts.filter(i=>i.provider!==e||i.providerAccountId!==t)};let s=a().models.User||a().model("User",o)},26373:(e,t,i)=>{"use strict";i.d(t,{A:()=>m});var r=i(61120);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,i)=>i?i.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=(...e)=>e.filter((e,t,i)=>!!e&&""!==e.trim()&&i.indexOf(e)===t).join(" ").trim(),l=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var c={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:i=2,absoluteStrokeWidth:a,className:n="",children:o,iconNode:d,...m},u)=>(0,r.createElement)("svg",{ref:u,...c,width:t,height:t,stroke:e,strokeWidth:a?24*Number(i)/Number(t):i,className:s("lucide",n),...!o&&!l(m)&&{"aria-hidden":"true"},...m},[...d.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(o)?o:[o]])),m=(e,t)=>{let i=(0,r.forwardRef)(({className:i,...n},l)=>(0,r.createElement)(d,{ref:l,iconNode:t,className:s(`lucide-${a(o(e))}`,`lucide-${e}`,i),...n}));return i.displayName=o(e),i}},29703:(e,t,i)=>{"use strict";i.d(t,{P5:()=>s,Tr:()=>n,Y5:()=>r,kx:()=>l,w8:()=>o});let r={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:8e3,label:"Description"},LONG_DESCRIPTION:{min:0,max:3e3,label:"Long Description"},PRICING_DETAILS:{min:0,max:500,label:"Pricing Details"},WEBSITE_URL:{min:10,max:100,label:"Website URL"},USER_NAME:{min:1,max:40,label:"User Name"},USER_BIO:{min:0,max:1e3,label:"User Bio"},COMMENT:{min:1,max:1e3,label:"Comment"}},a={BASE_DIR:process.env.UPLOAD_BASE_DIR||"uploads",DIRECTORIES:{LOGOS:"logos",AVATARS:"avatars",SCREENSHOTS:"screenshots",TEMP:"temp"},FILE_SIZE_LIMITS:{LOGO:5242880,AVATAR:5242880,SCREENSHOT:0xa00000},ALLOWED_TYPES:{IMAGES:["image/jpeg","image/jpg","image/png","image/gif","image/webp"]},NAMING:{LOGO_PREFIX:"logo_",AVATAR_PREFIX:"avatar_",SCREENSHOT_PREFIX:"screenshot_",TIMESTAMP_FORMAT:"timestamp_random"}};function n(e){let t=a.BASE_DIR,i=a.DIRECTORIES[e];return`${t}/${i}`}function o(e,t){let i=a.DIRECTORIES[e];return`/api/uploads/${i}/${t}`}function s(e,t){let i=r[e];if(!i)return{isValid:!0};let a=t.trim().length;return a<i.min?{isValid:!1,error:`${i.label} must be at least ${i.min} characters long`}:a>i.max?{isValid:!1,error:`${i.label} cannot exceed ${i.max} characters`}:{isValid:!0}}function l(e,t){var i;let r=function(e,t){let i=a.FILE_SIZE_LIMITS[e];if(t>i){let e=Math.round(i/1048576);return{isValid:!1,error:`File size cannot exceed ${e}MB`}}return{isValid:!0}}(t,e.size);if(!r.isValid)return r;let n=a.ALLOWED_TYPES.IMAGES,o=(i=e.type,n.includes(i)?{isValid:!0}:{isValid:!1,error:`File type ${i} is not allowed. Allowed types: ${n.join(", ")}`});return o.isValid?{isValid:!0}:o}},30762:(e,t,i)=>{"use strict";i.d(t,{A:()=>l});var r=i(56037),a=i.n(r),n=i(60366),o=i(29703);let s=new r.Schema({name:{type:String,required:[!0,"Tool name is required"],trim:!0,minlength:[o.Y5.TOOL_NAME.min,`Tool name must be at least ${o.Y5.TOOL_NAME.min} characters`],maxlength:[o.Y5.TOOL_NAME.max,`Tool name cannot exceed ${o.Y5.TOOL_NAME.max} characters`]},tagline:{type:String,trim:!0,maxlength:[o.Y5.TAGLINE.max,`Tagline cannot exceed ${o.Y5.TAGLINE.max} characters`]},description:{type:String,required:[!0,"Tool description is required"],trim:!0,minlength:[o.Y5.DESCRIPTION.min,`Description must be at least ${o.Y5.DESCRIPTION.min} characters`],maxlength:[o.Y5.DESCRIPTION.max,`Description cannot exceed ${o.Y5.DESCRIPTION.max} characters`]},longDescription:{type:String,trim:!0,maxlength:[o.Y5.LONG_DESCRIPTION.max,`Long description cannot exceed ${o.Y5.LONG_DESCRIPTION.max} characters`]},website:{type:String,required:[!0,"Website URL is required"],trim:!0,maxlength:[o.Y5.WEBSITE_URL.max,`Website URL cannot exceed ${o.Y5.WEBSITE_URL.max} characters`],validate:{validator:function(e){return/^https?:\/\/.+/.test(e)},message:"Please enter a valid URL"}},logo:{type:String,trim:!0},category:{type:String,required:[!0,"Category is required"],enum:n.ut},tags:[{type:String,trim:!0,lowercase:!0}],pricing:{type:String,required:[!0,"Pricing model is required"],enum:["free","freemium","paid"]},pricingDetails:{type:String,trim:!0,maxlength:[o.Y5.PRICING_DETAILS.max,`Pricing details cannot exceed ${o.Y5.PRICING_DETAILS.max} characters`]},screenshots:[{type:String,trim:!0}],submittedBy:{type:String,required:[!0,"Submitter ID is required"],trim:!0},submittedAt:{type:Date,default:Date.now},launchDate:{type:Date},status:{type:String,required:!0,enum:["draft","pending","approved","rejected"],default:"draft"},reviewNotes:{type:String,trim:!0,maxlength:[1e3,"Review notes cannot exceed 1000 characters"]},reviewedBy:{type:String,trim:!0},reviewedAt:{type:Date},launchDateSelected:{type:Boolean,default:!1},selectedLaunchDate:{type:Date},launchOption:{type:String,enum:["free","paid"]},paymentRequired:{type:Boolean,default:!1},paymentAmount:{type:Number,min:0},paymentStatus:{type:String,enum:["pending","completed","failed","refunded"]},orderId:{type:String,trim:!0},paymentMethod:{type:String,trim:!0},paidAt:{type:Date},badgeVerified:{type:Boolean,default:!1},badgeVerifiedAt:{type:Date},views:{type:Number,default:0,min:0},likes:{type:Number,default:0,min:0},likedBy:[{type:String,trim:!0}],isActive:{type:Boolean,default:!0}},{timestamps:!0,toJSON:{virtuals:!0},toObject:{virtuals:!0}});s.index({status:1,isActive:1}),s.index({category:1,status:1}),s.index({tags:1,status:1}),s.index({submittedBy:1}),s.index({launchDate:-1}),s.index({views:-1}),s.index({likes:-1}),s.index({name:"text",tagline:"text",description:"text",longDescription:"text",tags:"text"});let l=a().models.Tool||a().model("Tool",s)},40918:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(26373).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},60366:(e,t,i)=>{"use strict";i.d(t,{BB:()=>o,PZ:()=>s,RI:()=>c,ut:()=>l});var r=i(64348);let a=[{slug:"productivity",icon:"⚡",color:"#14B8A6"},{slug:"education",icon:"\uD83D\uDCDA",color:"#A855F7"},{slug:"text-generation",icon:"\uD83D\uDCDD",color:"#3B82F6"},{slug:"image-generation",icon:"\uD83C\uDFA8",color:"#10B981"},{slug:"code-generation",icon:"\uD83D\uDCBB",color:"#8B5CF6"},{slug:"audio-processing",icon:"\uD83C\uDFB5",color:"#EF4444"},{slug:"video-editing",icon:"\uD83C\uDFAC",color:"#06B6D4"},{slug:"translation",icon:"\uD83C\uDF10",color:"#84CC16"},{slug:"search-engines",icon:"\uD83D\uDD0D",color:"#F97316"},{slug:"marketing",icon:"\uD83D\uDCC8",color:"#EC4899"},{slug:"customer-service",icon:"\uD83C\uDFA7",color:"#F59E0B"},{slug:"data-analysis",icon:"\uD83D\uDCCA",color:"#F59E0B"}];async function n(e){let t=await (0,r.A)({locale:e||"en",namespace:"categories"});return a.map(e=>({slug:e.slug,name:t(`category_names.${e.slug}`),description:t(`category_descriptions.${e.slug}`),icon:e.icon,color:e.color}))}async function o(e){return(await n(e)).map(e=>({value:e.slug,label:e.name}))}async function s(e,t){return(await n(t)).find(t=>t.slug===e)}let l=a.map(e=>e.slug),c=a.reduce((e,t)=>(e[t.slug]=t,e),{})},75745:(e,t,i)=>{"use strict";i.d(t,{A:()=>s});var r=i(56037),a=i.n(r);let n=process.env.MONGODB_URI;if(!n)throw Error("Please define the MONGODB_URI environment variable inside .env.local");let o=global.mongoose;o||(o=global.mongoose={conn:null,promise:null});let s=async function(){if(o.conn)return o.conn;o.promise||(o.promise=a().connect(n,{bufferCommands:!1}));try{o.conn=await o.promise}catch(e){throw o.promise=null,e}return o.conn}},91142:(e,t,i)=>{"use strict";i.d(t,{A:()=>r});let r=(0,i(26373).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])}};