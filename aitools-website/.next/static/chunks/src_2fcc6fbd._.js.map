{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts"], "sourcesContent": ["/**\n * 统一的价格配置文件\n * 所有与价格相关的配置都应该在这里定义，避免散落在各个文件中\n */\n\n// 基础价格配置\nexport const PRICING_CONFIG = {\n  // Priority launch service pricing\n  PRIORITY_LAUNCH: {\n    // Display price (CNY)\n    displayPrice: 19.9,\n    // Original price (CNY) - for showing pre-discount price\n    originalPrice: 49.9,\n    // Stripe price (in cents)\n    stripeAmount: 1990,\n    // Original Stripe amount (in cents)\n    originalStripeAmount: 4990,\n    // Currency\n    currency: 'USD',\n    // Stripe currency code (lowercase)\n    stripeCurrency: 'usd', // Note: currently using USD for testing\n    // Product name\n    productName: 'AI Tool Priority Launch Service',\n    // Product description\n    description: 'Get your AI tool prioritized for review and featured placement',\n    // Limited-time promotion info\n    promotion: {\n      // Whether the promotion is enabled\n      enabled: true,\n      // Promotion description\n      description: 'Limited-time offer - First 100 paid users',\n      // Discount percentage\n      discountPercent: 50,\n      // Remaining slots (can be dynamically fetched from database)\n      remainingSlots: 85\n    },\n    // Feature list\n    features: [\n      'Choose any publish date',\n      'Priority review processing',\n      'Featured homepage placement',\n      'Dedicated customer support'\n    ]\n  },\n\n  // Free launch configuration\n  FREE_LAUNCH: {\n    displayPrice: 0,\n    stripeAmount: 0,\n    currency: 'USD',\n    stripeCurrency: 'usd',\n    productName: 'Free Launch Service',\n    description: 'Choose any publish date after one month',\n    features: [\n      'Free submission for review',\n      'Publish date: from one month later',\n      'Standard review process',\n      'Standard display placement'\n    ]\n  }\n} as const;\n\n// 发布选项配置\nexport const LAUNCH_OPTIONS = [\n  {\n    id: 'free' as const,\n    title: '免费发布',\n    description: PRICING_CONFIG.FREE_LAUNCH.description,\n    price: PRICING_CONFIG.FREE_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.FREE_LAUNCH.features\n  },\n  {\n    id: 'paid' as const,\n    title: '优先发布',\n    description: PRICING_CONFIG.PRIORITY_LAUNCH.description,\n    price: PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice,\n    features: PRICING_CONFIG.PRIORITY_LAUNCH.features,\n    recommended: true\n  }\n] as const;\n\n// 工具定价类型配置\nexport const TOOL_PRICING_TYPES = {\n  FREE: {\n    value: 'free',\n    label: 'Free',\n    color: 'bg-green-100 text-green-800'\n  },\n  FREEMIUM: {\n    value: 'freemium',\n    label: ' Freemium',\n    color: 'bg-blue-100 text-blue-800'\n  },\n  PAID: {\n    value: 'paid',\n    label: 'Paid',\n    color: 'bg-orange-100 text-orange-800'\n  }\n} as const;\n\n// 工具定价选项（用于筛选）\nexport const TOOL_PRICING_OPTIONS = [\n  { value: '', label: 'All Prices' },\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 工具定价选项（用于表单）\nexport const TOOL_PRICING_FORM_OPTIONS = [\n  { value: TOOL_PRICING_TYPES.FREE.value, label: TOOL_PRICING_TYPES.FREE.label },\n  { value: TOOL_PRICING_TYPES.FREEMIUM.value, label: TOOL_PRICING_TYPES.FREEMIUM.label },\n  { value: TOOL_PRICING_TYPES.PAID.value, label: TOOL_PRICING_TYPES.PAID.label }\n] as const;\n\n// 类型定义\nexport type LaunchOptionId = typeof LAUNCH_OPTIONS[number]['id'];\nexport type ToolPricingType = typeof TOOL_PRICING_TYPES[keyof typeof TOOL_PRICING_TYPES]['value'];\n\n// 辅助函数\nexport const getPricingConfig = (optionId: LaunchOptionId) => {\n  return optionId === 'paid' ? PRICING_CONFIG.PRIORITY_LAUNCH : PRICING_CONFIG.FREE_LAUNCH;\n};\n\nexport const getToolPricingColor = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.color;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.color;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.color;\n    default:\n      return 'bg-gray-100 text-gray-800';\n  }\n};\n\nexport const getToolPricingText = (pricing: string) => {\n  switch (pricing) {\n    case TOOL_PRICING_TYPES.FREE.value:\n      return TOOL_PRICING_TYPES.FREE.label;\n    case TOOL_PRICING_TYPES.FREEMIUM.value:\n      return TOOL_PRICING_TYPES.FREEMIUM.label;\n    case TOOL_PRICING_TYPES.PAID.value:\n      return TOOL_PRICING_TYPES.PAID.label;\n    default:\n      return pricing;\n  }\n};\n\n// 格式化价格显示\nexport const formatPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 格式化原价显示（带删除线）\nexport const formatOriginalPrice = (price: number, locale?: string) => {\n  if (price === 0) {\n    return locale === 'zh' ? '免费' : 'Free';\n  }\n  return `¥${price}`;\n};\n\n// 获取促销信息\nexport const getPromotionInfo = () => {\n  return PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n};\n\n// 检查是否有促销活动\nexport const hasActivePromotion = () => {\n  const promotion = PRICING_CONFIG.PRIORITY_LAUNCH.promotion;\n  return promotion.enabled && promotion.remainingSlots > 0;\n};\n\n// 格式化Stripe金额显示\nexport const formatStripeAmount = (amount: number, currency: string = 'cny'): string => {\n  return new Intl.NumberFormat('zh-CN', {\n    style: 'currency',\n    currency: currency.toUpperCase(),\n    minimumFractionDigits: 2,\n  }).format(amount / 100);\n};\n"], "names": [], "mappings": "AAAA;;;CAGC,GAED,SAAS;;;;;;;;;;;;;;;;AACF,MAAM,iBAAiB;IAC5B,kCAAkC;IAClC,iBAAiB;QACf,sBAAsB;QACtB,cAAc;QACd,wDAAwD;QACxD,eAAe;QACf,0BAA0B;QAC1B,cAAc;QACd,oCAAoC;QACpC,sBAAsB;QACtB,WAAW;QACX,UAAU;QACV,mCAAmC;QACnC,gBAAgB;QAChB,eAAe;QACf,aAAa;QACb,sBAAsB;QACtB,aAAa;QACb,8BAA8B;QAC9B,WAAW;YACT,mCAAmC;YACnC,SAAS;YACT,wBAAwB;YACxB,aAAa;YACb,sBAAsB;YACtB,iBAAiB;YACjB,6DAA6D;YAC7D,gBAAgB;QAClB;QACA,eAAe;QACf,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;IAEA,4BAA4B;IAC5B,aAAa;QACX,cAAc;QACd,cAAc;QACd,UAAU;QACV,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,iBAAiB;IAC5B;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,WAAW,CAAC,WAAW;QACnD,OAAO,eAAe,WAAW,CAAC,YAAY;QAC9C,UAAU,eAAe,WAAW,CAAC,QAAQ;IAC/C;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa,eAAe,eAAe,CAAC,WAAW;QACvD,OAAO,eAAe,eAAe,CAAC,YAAY;QAClD,UAAU,eAAe,eAAe,CAAC,QAAQ;QACjD,aAAa;IACf;CACD;AAGM,MAAM,qBAAqB;IAChC,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,UAAU;QACR,OAAO;QACP,OAAO;QACP,OAAO;IACT;IACA,MAAM;QACJ,OAAO;QACP,OAAO;QACP,OAAO;IACT;AACF;AAGO,MAAM,uBAAuB;IAClC;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAGM,MAAM,4BAA4B;IACvC;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;IAC7E;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAAE,OAAO,mBAAmB,QAAQ,CAAC,KAAK;IAAC;IACrF;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;QAAE,OAAO,mBAAmB,IAAI,CAAC,KAAK;IAAC;CAC9E;AAOM,MAAM,mBAAmB,CAAC;IAC/B,OAAO,aAAa,SAAS,eAAe,eAAe,GAAG,eAAe,WAAW;AAC1F;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,OAAQ;QACN,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC,KAAK,mBAAmB,QAAQ,CAAC,KAAK;YACpC,OAAO,mBAAmB,QAAQ,CAAC,KAAK;QAC1C,KAAK,mBAAmB,IAAI,CAAC,KAAK;YAChC,OAAO,mBAAmB,IAAI,CAAC,KAAK;QACtC;YACE,OAAO;IACX;AACF;AAGO,MAAM,cAAc,CAAC,OAAe;IACzC,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,sBAAsB,CAAC,OAAe;IACjD,IAAI,UAAU,GAAG;QACf,OAAO,WAAW,OAAO,OAAO;IAClC;IACA,OAAO,CAAC,CAAC,EAAE,OAAO;AACpB;AAGO,MAAM,mBAAmB;IAC9B,OAAO,eAAe,eAAe,CAAC,SAAS;AACjD;AAGO,MAAM,qBAAqB;IAChC,MAAM,YAAY,eAAe,eAAe,CAAC,SAAS;IAC1D,OAAO,UAAU,OAAO,IAAI,UAAU,cAAc,GAAG;AACzD;AAGO,MAAM,qBAAqB,CAAC,QAAgB,WAAmB,KAAK;IACzE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU,SAAS,WAAW;QAC9B,uBAAuB;IACzB,GAAG,MAAM,CAAC,SAAS;AACrB", "debugId": null}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/FeaturedBadge.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Award, Copy, Check } from 'lucide-react';\n\nexport type BadgeTheme = 'light' | 'dark' | 'neutral';\n\ninterface FeaturedBadgeProps {\n  theme?: BadgeTheme;\n  size?: 'sm' | 'md' | 'lg';\n  showCopyButton?: boolean;\n  toolName?: string;\n  className?: string;\n}\n\nconst themeStyles = {\n  light: {\n    container: 'bg-white border-2 border-blue-500 text-blue-600',\n    icon: 'text-blue-500',\n    text: 'text-blue-600',\n    accent: 'bg-blue-500'\n  },\n  dark: {\n    container: 'bg-gray-900 border-2 border-yellow-400 text-yellow-400',\n    icon: 'text-yellow-400',\n    text: 'text-yellow-400',\n    accent: 'bg-yellow-400'\n  },\n  neutral: {\n    container: 'bg-gray-50 border-2 border-gray-400 text-gray-700',\n    icon: 'text-gray-600',\n    text: 'text-gray-700',\n    accent: 'bg-gray-400'\n  }\n};\n\nconst sizeStyles = {\n  sm: {\n    container: 'px-3 py-2 text-xs',\n    icon: 'h-3 w-3',\n    spacing: 'gap-1.5'\n  },\n  md: {\n    container: 'px-4 py-2.5 text-sm',\n    icon: 'h-4 w-4',\n    spacing: 'gap-2'\n  },\n  lg: {\n    container: 'px-6 py-3 text-base',\n    icon: 'h-5 w-5',\n    spacing: 'gap-2.5'\n  }\n};\n\nexport default function FeaturedBadge({ \n  theme = 'light', \n  size = 'md', \n  showCopyButton = false,\n  toolName = 'Your Tool',\n  className = ''\n}: FeaturedBadgeProps) {\n  const [copied, setCopied] = useState(false);\n  \n  const themeStyle = themeStyles[theme];\n  const sizeStyle = sizeStyles[size];\n\n  const generateBadgeCode = (selectedTheme: BadgeTheme) => {\n    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://aitools.com';\n    \n    return `<a href=\"${baseUrl}\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: inline-flex; align-items: center; padding: ${size === 'sm' ? '8px 12px' : size === 'md' ? '10px 16px' : '12px 24px'}; border-radius: 8px; text-decoration: none; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-weight: 600; font-size: ${size === 'sm' ? '12px' : size === 'md' ? '14px' : '16px'}; gap: ${size === 'sm' ? '6px' : '8px'}; ${\n      selectedTheme === 'light' \n        ? 'background-color: #ffffff; border: 2px solid #3b82f6; color: #3b82f6;'\n        : selectedTheme === 'dark'\n        ? 'background-color: #111827; border: 2px solid #fbbf24; color: #fbbf24;'\n        : 'background-color: #f9fafb; border: 2px solid #6b7280; color: #374151;'\n    } transition: all 0.2s ease;\">\n  <svg width=\"${size === 'sm' ? '12' : size === 'md' ? '16' : '20'}\" height=\"${size === 'sm' ? '12' : size === 'md' ? '16' : '20'}\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n    <path d=\"m9 12 2 2 4-4\"></path>\n    <path d=\"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z\"></path>\n    <path d=\"M21.21 15.89A10 10 0 1 1 8 2.83\"></path>\n  </svg>\n  Featured on AITools\n</a>`;\n  };\n\n  const copyToClipboard = async () => {\n    try {\n      await navigator.clipboard.writeText(generateBadgeCode(theme));\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy:', err);\n    }\n  };\n\n  return (\n    <div className={`inline-flex items-center ${className}`}>\n      <a \n        href=\"https://aitools.com\" \n        target=\"_blank\" \n        rel=\"noopener noreferrer\"\n        className={`\n          inline-flex items-center rounded-lg font-semibold transition-all duration-200 hover:scale-105 hover:shadow-lg\n          ${themeStyle.container} ${sizeStyle.container} ${sizeStyle.spacing}\n        `}\n      >\n        <Award className={`${themeStyle.icon} ${sizeStyle.icon}`} />\n        <span className={themeStyle.text}>Featured on AITools</span>\n      </a>\n      \n      {showCopyButton && (\n        <button\n          onClick={copyToClipboard}\n          className=\"ml-2 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors\"\n          title=\"Copy embed code\"\n        >\n          {copied ? (\n            <Check className=\"h-4 w-4 text-green-500\" />\n          ) : (\n            <Copy className=\"h-4 w-4\" />\n          )}\n        </button>\n      )}\n    </div>\n  );\n}\n\n// 导出生成代码的函数，供其他组件使用\nexport function generateBadgeEmbedCode(theme: BadgeTheme = 'light', size: 'sm' | 'md' | 'lg' = 'md'): string {\n  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://aitools.com';\n  \n  return `<a href=\"${baseUrl}\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"display: inline-flex; align-items: center; padding: ${size === 'sm' ? '8px 12px' : size === 'md' ? '10px 16px' : '12px 24px'}; border-radius: 8px; text-decoration: none; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-weight: 600; font-size: ${size === 'sm' ? '12px' : size === 'md' ? '14px' : '16px'}; gap: ${size === 'sm' ? '6px' : '8px'}; ${\n    theme === 'light' \n      ? 'background-color: #ffffff; border: 2px solid #3b82f6; color: #3b82f6;'\n      : theme === 'dark'\n      ? 'background-color: #111827; border: 2px solid #fbbf24; color: #fbbf24;'\n      : 'background-color: #f9fafb; border: 2px solid #6b7280; color: #374151;'\n  } transition: all 0.2s ease;\">\n  <svg width=\"${size === 'sm' ? '12' : size === 'md' ? '16' : '20'}\" height=\"${size === 'sm' ? '12' : size === 'md' ? '16' : '20'}\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n    <path d=\"m9 12 2 2 4-4\"></path>\n    <path d=\"M21 12c.552 0 1.005-.449.95-.998a10 10 0 0 0-8.953-8.951c-.55-.055-.998.398-.998.95v8a1 1 0 0 0 1 1z\"></path>\n    <path d=\"M21.21 15.89A10 10 0 1 1 8 2.83\"></path>\n  </svg>\n  Featured on AITools\n</a>`;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;;;AAHA;;;AAeA,MAAM,cAAc;IAClB,OAAO;QACL,WAAW;QACX,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA,MAAM;QACJ,WAAW;QACX,MAAM;QACN,MAAM;QACN,QAAQ;IACV;IACA,SAAS;QACP,WAAW;QACX,MAAM;QACN,MAAM;QACN,QAAQ;IACV;AACF;AAEA,MAAM,aAAa;IACjB,IAAI;QACF,WAAW;QACX,MAAM;QACN,SAAS;IACX;IACA,IAAI;QACF,WAAW;QACX,MAAM;QACN,SAAS;IACX;IACA,IAAI;QACF,WAAW;QACX,MAAM;QACN,SAAS;IACX;AACF;AAEe,SAAS,cAAc,EACpC,QAAQ,OAAO,EACf,OAAO,IAAI,EACX,iBAAiB,KAAK,EACtB,WAAW,WAAW,EACtB,YAAY,EAAE,EACK;;IACnB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa,WAAW,CAAC,MAAM;IACrC,MAAM,YAAY,UAAU,CAAC,KAAK;IAElC,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU,uCAAgC,OAAO,QAAQ,CAAC,MAAM;QAEtE,OAAO,CAAC,SAAS,EAAE,QAAQ,uGAAuG,EAAE,SAAS,OAAO,aAAa,SAAS,OAAO,cAAc,YAAY,0JAA0J,EAAE,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS,OAAO,OAAO,EAAE,SAAS,OAAO,QAAQ,MAAM,EAAE,EACvc,kBAAkB,UACd,0EACA,kBAAkB,SAClB,0EACA,wEACL;cACS,EAAE,SAAS,OAAO,OAAO,SAAS,OAAO,OAAO,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,SAAS,OAAO,OAAO,KAAK;;;;;;IAM9H,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC,kBAAkB;YACtD,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,yBAAyB,EAAE,WAAW;;0BACrD,6LAAC;gBACC,MAAK;gBACL,QAAO;gBACP,KAAI;gBACJ,WAAW,CAAC;;UAEV,EAAE,WAAW,SAAS,CAAC,CAAC,EAAE,UAAU,SAAS,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC;QACrE,CAAC;;kCAED,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAW,GAAG,WAAW,IAAI,CAAC,CAAC,EAAE,UAAU,IAAI,EAAE;;;;;;kCACxD,6LAAC;wBAAK,WAAW,WAAW,IAAI;kCAAE;;;;;;;;;;;;YAGnC,gCACC,6LAAC;gBACC,SAAS;gBACT,WAAU;gBACV,OAAM;0BAEL,uBACC,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;yCAEjB,6LAAC,qMAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAM5B;GAvEwB;KAAA;AA0EjB,SAAS,uBAAuB,QAAoB,OAAO,EAAE,OAA2B,IAAI;IACjG,MAAM,UAAU,uCAAgC,OAAO,QAAQ,CAAC,MAAM;IAEtE,OAAO,CAAC,SAAS,EAAE,QAAQ,uGAAuG,EAAE,SAAS,OAAO,aAAa,SAAS,OAAO,cAAc,YAAY,0JAA0J,EAAE,SAAS,OAAO,SAAS,SAAS,OAAO,SAAS,OAAO,OAAO,EAAE,SAAS,OAAO,QAAQ,MAAM,EAAE,EACvc,UAAU,UACN,0EACA,UAAU,SACV,0EACA,wEACL;cACW,EAAE,SAAS,OAAO,OAAO,SAAS,OAAO,OAAO,KAAK,UAAU,EAAE,SAAS,OAAO,OAAO,SAAS,OAAO,OAAO,KAAK;;;;;;IAM9H,CAAC;AACL", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/BadgeVerification.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useTranslations } from 'next-intl';\nimport { Award, Copy, Check, ExternalLink, Loader2, AlertCircle, CheckCircle, Globe } from 'lucide-react';\nimport FeaturedBadge, { BadgeTheme, generateBadgeEmbedCode } from './FeaturedBadge';\n\ninterface BadgeVerificationProps {\n  toolId?: string;\n  toolWebsite?: string;\n  onVerificationSuccess: () => void;\n  className?: string;\n}\n\nexport default function BadgeVerification({\n  toolId,\n  toolWebsite,\n  onVerificationSuccess,\n  className = ''\n}: BadgeVerificationProps) {\n  const [selectedTheme, setSelectedTheme] = useState<BadgeTheme>('light');\n  const [copied, setCopied] = useState(false);\n  const [isVerifying, setIsVerifying] = useState(false);\n  const [verificationStatus, setVerificationStatus] = useState<'idle' | 'success' | 'error'>('idle');\n  const [verificationError, setVerificationError] = useState('');\n\n  const t = useTranslations('badge_verification');\n\n  const themes: { value: BadgeTheme; label: string; description: string }[] = [\n    { value: 'light', label: t('themes.light.label'), description: t('themes.light.description') },\n    { value: 'dark', label: t('themes.dark.label'), description: t('themes.dark.description') },\n    { value: 'neutral', label: t('themes.neutral.label'), description: t('themes.neutral.description') }\n  ];\n\n  const copyEmbedCode = async () => {\n    try {\n      const code = generateBadgeEmbedCode(selectedTheme, 'lg'); // 固定使用large尺寸\n      await navigator.clipboard.writeText(code);\n      setCopied(true);\n      setTimeout(() => setCopied(false), 2000);\n    } catch (err) {\n      console.error('Failed to copy:', err);\n    }\n  };\n\n  const verifyBadge = async () => {\n    if (!toolWebsite) {\n      setVerificationError(t('errors.no_website'));\n      return;\n    }\n\n    setIsVerifying(true);\n    setVerificationError('');\n    setVerificationStatus('idle');\n\n    try {\n      const response = await fetch('/api/tools/verify-badge', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          toolId,\n          website: toolWebsite,\n          expectedText: 'Featured on AITools'\n        }),\n      });\n\n      const data = await response.json();\n\n      if (data.success) {\n        setVerificationStatus('success');\n        setTimeout(() => {\n          onVerificationSuccess();\n        }, 1500);\n      } else {\n        setVerificationStatus('error');\n        setVerificationError(data.error || t('errors.verification_failed'));\n      }\n    } catch (error) {\n      setVerificationStatus('error');\n      setVerificationError(t('errors.network_error'));\n    } finally {\n      setIsVerifying(false);\n    }\n  };\n\n  return (\n    <div className={`bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200 ${className}`}>\n      <div className=\"flex items-center mb-6\">\n        <Award className=\"h-6 w-6 text-blue-600 mr-3\" />\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">{t('title')}</h3>\n          <p className=\"text-sm text-gray-600\">{t('subtitle')}</p>\n        </div>\n      </div>\n\n      <div className=\"space-y-8\">\n        {/* 步骤1: 选择主题 */}\n        <div>\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3\">\n              1\n            </div>\n            <h4 className=\"text-md font-medium text-gray-900\">{t('step1.title')}</h4>\n          </div>\n\n          {/* 主题选择 - Tab样式 */}\n          <div className=\"mb-4\">\n            <div className=\"flex border-b border-gray-200\">\n              {themes.map((theme) => (\n                <button\n                  key={theme.value}\n                  onClick={() => setSelectedTheme(theme.value)}\n                  className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                    selectedTheme === theme.value\n                      ? 'border-blue-500 text-blue-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  {theme.label}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* Badge预览 */}\n          <div className=\"bg-white rounded-lg p-4 border border-gray-200 mb-4\">\n            <div className=\"text-sm text-gray-600 mb-2\">预览：</div>\n            <FeaturedBadge theme={selectedTheme} size=\"lg\" />\n          </div>\n\n          {/* 复制按钮 */}\n          <button\n            onClick={copyEmbedCode}\n            className=\"w-full bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2\"\n          >\n            {copied ? (\n              <>\n                <Check className=\"h-4 w-4\" />\n                {t('buttons.copied')}\n              </>\n            ) : (\n              <>\n                <Copy className=\"h-4 w-4\" />\n                复制嵌入代码\n              </>\n            )}\n          </button>\n        </div>\n\n        {/* 步骤2: 添加到网站 */}\n        <div>\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3\">\n              2\n            </div>\n            <h4 className=\"text-md font-medium text-gray-900\">{t('step2.title')}</h4>\n          </div>\n          <p className=\"text-sm text-gray-600 ml-11\">{t('step2.description')}</p>\n        </div>\n\n        {/* 步骤3: 验证 */}\n        <div>\n          <div className=\"flex items-center mb-4\">\n            <div className=\"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3\">\n              3\n            </div>\n            <h4 className=\"text-md font-medium text-gray-900\">{t('step3.title')}</h4>\n          </div>\n\n          {toolWebsite && (\n            <div className=\"bg-white rounded-lg p-4 border border-gray-200 mb-4 ml-11\">\n              <div className=\"flex items-center gap-2 text-sm text-gray-600 mb-2\">\n                <Globe className=\"h-4 w-4\" />\n                {t('step3.website_label')}\n              </div>\n              <a\n                href={toolWebsite}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-blue-600 hover:text-blue-800 flex items-center gap-1\"\n              >\n                {toolWebsite}\n                <ExternalLink className=\"h-3 w-3\" />\n              </a>\n            </div>\n          )}\n\n          {/* 验证状态 */}\n          {verificationStatus === 'success' && (\n            <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-4 ml-11\">\n              <div className=\"flex items-center gap-2 text-green-800\">\n                <CheckCircle className=\"h-5 w-5\" />\n                <span className=\"font-medium\">{t('verification.success')}</span>\n              </div>\n              <p className=\"text-sm text-green-600 mt-1\">{t('verification.success_description')}</p>\n            </div>\n          )}\n\n          {verificationStatus === 'error' && (\n            <div className=\"bg-red-50 border border-red-200 rounded-lg p-4 mb-4 ml-11\">\n              <div className=\"flex items-center gap-2 text-red-800\">\n                <AlertCircle className=\"h-5 w-5\" />\n                <span className=\"font-medium\">{t('verification.error')}</span>\n              </div>\n              <p className=\"text-sm text-red-600 mt-1\">{verificationError}</p>\n            </div>\n          )}\n\n          <div className=\"ml-11\">\n            <button\n              onClick={verifyBadge}\n              disabled={isVerifying || verificationStatus === 'success'}\n              className=\"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors\"\n            >\n              {isVerifying ? (\n                <>\n                  <Loader2 className=\"h-4 w-4 animate-spin\" />\n                  {t('buttons.verifying')}\n                </>\n              ) : verificationStatus === 'success' ? (\n                <>\n                  <CheckCircle className=\"h-4 w-4\" />\n                  {t('buttons.verified')}\n                </>\n              ) : (\n                t('buttons.verify')\n              )}\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAce,SAAS,kBAAkB,EACxC,MAAM,EACN,WAAW,EACX,qBAAqB,EACrB,YAAY,EAAE,EACS;;IACvB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IAC3F,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,SAAsE;QAC1E;YAAE,OAAO;YAAS,OAAO,EAAE;YAAuB,aAAa,EAAE;QAA4B;QAC7F;YAAE,OAAO;YAAQ,OAAO,EAAE;YAAsB,aAAa,EAAE;QAA2B;QAC1F;YAAE,OAAO;YAAW,OAAO,EAAE;YAAyB,aAAa,EAAE;QAA8B;KACpG;IAED,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,OAAO,CAAA,GAAA,sIAAA,CAAA,yBAAsB,AAAD,EAAE,eAAe,OAAO,cAAc;YACxE,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,UAAU;YACV,WAAW,IAAM,UAAU,QAAQ;QACrC,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa;YAChB,qBAAqB,EAAE;YACvB;QACF;QAEA,eAAe;QACf,qBAAqB;QACrB,sBAAsB;QAEtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,2BAA2B;gBACtD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,SAAS;oBACT,cAAc;gBAChB;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,sBAAsB;gBACtB,WAAW;oBACT;gBACF,GAAG;YACL,OAAO;gBACL,sBAAsB;gBACtB,qBAAqB,KAAK,KAAK,IAAI,EAAE;YACvC;QACF,EAAE,OAAO,OAAO;YACd,sBAAsB;YACtB,qBAAqB,EAAE;QACzB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,mFAAmF,EAAE,WAAW;;0BAC/G,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAuC,EAAE;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAyB,EAAE;;;;;;;;;;;;;;;;;;0BAI5C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwG;;;;;;kDAGvH,6LAAC;wCAAG,WAAU;kDAAqC,EAAE;;;;;;;;;;;;0CAIvD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;4CAEC,SAAS,IAAM,iBAAiB,MAAM,KAAK;4CAC3C,WAAW,CAAC,2DAA2D,EACrE,kBAAkB,MAAM,KAAK,GACzB,kCACA,wDACJ;sDAED,MAAM,KAAK;2CARP,MAAM,KAAK;;;;;;;;;;;;;;;0CAexB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC,sIAAA,CAAA,UAAa;wCAAC,OAAO;wCAAe,MAAK;;;;;;;;;;;;0CAI5C,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAET,uBACC;;sDACE,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,EAAE;;iEAGL;;sDACE,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;kCAQpC,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwG;;;;;;kDAGvH,6LAAC;wCAAG,WAAU;kDAAqC,EAAE;;;;;;;;;;;;0CAEvD,6LAAC;gCAAE,WAAU;0CAA+B,EAAE;;;;;;;;;;;;kCAIhD,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAwG;;;;;;kDAGvH,6LAAC;wCAAG,WAAU;kDAAqC,EAAE;;;;;;;;;;;;4BAGtD,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAChB,EAAE;;;;;;;kDAEL,6LAAC;wCACC,MAAM;wCACN,QAAO;wCACP,KAAI;wCACJ,WAAU;;4CAET;0DACD,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;4BAM7B,uBAAuB,2BACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAe,EAAE;;;;;;;;;;;;kDAEnC,6LAAC;wCAAE,WAAU;kDAA+B,EAAE;;;;;;;;;;;;4BAIjD,uBAAuB,yBACtB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAe,EAAE;;;;;;;;;;;;kDAEnC,6LAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAI9C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;oCACT,UAAU,eAAe,uBAAuB;oCAChD,WAAU;8CAET,4BACC;;0DACE,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAClB,EAAE;;uDAEH,uBAAuB,0BACzB;;0DACE,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;4CACtB,EAAE;;uDAGL,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlB;GA7NwB;;QAYZ,yMAAA,CAAA,kBAAe;;;KAZH", "debugId": null}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useTranslations, useLocale } from 'next-intl';\nimport { Calendar, Clock, CreditCard, CheckCircle, Tag, Award } from 'lucide-react';\nimport { LAUNCH_OPTIONS, formatPrice, formatOriginalPrice, PRICING_CONFIG, hasActivePromotion } from '@/constants/pricing';\nimport BadgeVerification from './BadgeVerification';\n\n\n// 使用统一的发布选项配置\nconst launchOptions = LAUNCH_OPTIONS;\n\ninterface LaunchDateSelectorProps {\n  toolId?: string;\n  toolWebsite?: string;\n  currentOption?: 'free' | 'paid';\n  currentDate?: string;\n  isEditing?: boolean;\n  onSubmit: (option: 'free' | 'paid', date: string) => Promise<void>;\n  isSubmitting: boolean;\n  error?: string;\n  hasPaidOrder?: boolean;\n  badgeVerified?: boolean;\n}\n\nexport default function LaunchDateSelector({\n  toolId,\n  toolWebsite,\n  currentOption = 'paid',\n  currentDate,\n  isEditing = false,\n  onSubmit,\n  isSubmitting,\n  error,\n  hasPaidOrder = false,\n  badgeVerified = false\n}: LaunchDateSelectorProps) {\n  // 如果已付费，强制设置为 paid 选项\n  const [selectedOption, setSelectedOption] = useState<'free' | 'paid'>(\n    hasPaidOrder ? 'paid' : currentOption\n  );\n  const [selectedDate, setSelectedDate] = useState<string>('');\n  const [showBadgeVerification, setShowBadgeVerification] = useState(false);\n  const [isBadgeVerified, setIsBadgeVerified] = useState(badgeVerified);\n\n  const t = useTranslations('launch');\n  const locale = useLocale();\n\n  // 计算折扣百分比\n  const getDiscountPercentage = () => {\n    if (!hasActivePromotion()) return 0;\n    \n    const originalPrice = PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice;\n    const currentPrice = PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice;\n    const discount = Math.round(((originalPrice - currentPrice) / originalPrice) * 100);\n    return discount;\n  };\n\n  // 获取最早可选择的免费日期（一个月后，除非badge已验证）\n  const getMinFreeDate = () => {\n    if (isBadgeVerified) {\n      // Badge验证用户可以选择明天开始的日期\n      const date = new Date();\n      date.setDate(date.getDate() + 1);\n      return date.toISOString().split('T')[0];\n    }\n    const date = new Date();\n    date.setMonth(date.getMonth() + 1);\n    return date.toISOString().split('T')[0];\n  };\n\n  // 获取最早可选择的付费日期（明天）\n  const getMinPaidDate = () => {\n    const date = new Date();\n    date.setDate(date.getDate() + 1);\n    return date.toISOString().split('T')[0];\n  };\n\n  useEffect(() => {\n    if (currentDate) {\n      setSelectedDate(currentDate);\n    } else {\n      // 根据选择的选项设置默认日期\n      if (selectedOption === 'free') {\n        setSelectedDate(getMinFreeDate());\n      } else {\n        setSelectedDate(getMinPaidDate());\n      }\n    }\n  }, [selectedOption, currentDate]);\n\n  const handleOptionChange = (option: 'free' | 'paid') => {\n    // 如果已经付费，不允许切换选项\n    if (hasPaidOrder) {\n      return;\n    }\n\n    setSelectedOption(option);\n    // 当切换选项时，重新设置日期\n    if (option === 'free') {\n      setSelectedDate(getMinFreeDate());\n    } else {\n      setSelectedDate(getMinPaidDate());\n    }\n  };\n\n  const handleSubmit = async () => {\n    if (!selectedDate) {\n      return;\n    }\n    await onSubmit(selectedOption, selectedDate);\n  };\n\n  const handleBadgeVerificationSuccess = () => {\n    setIsBadgeVerified(true);\n    setShowBadgeVerification(false);\n    // 如果当前是免费选项，重新设置日期为明天\n    if (selectedOption === 'free') {\n      const date = new Date();\n      date.setDate(date.getDate() + 1);\n      setSelectedDate(date.toISOString().split('T')[0]);\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* 选项选择 - 如果已付费则不显示 */}\n      {!hasPaidOrder && (\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\n            {isEditing ? t('select_plan') : t('select_option')}\n          </h3>\n          <div className=\"grid md:grid-cols-2 gap-6\">\n            {launchOptions.map((option) => (\n              <div\n                key={option.id}\n                className={`relative border-2 rounded-lg p-6 cursor-pointer transition-all flex flex-col ${\n                  selectedOption === option.id\n                    ? 'border-blue-500 bg-blue-50'\n                    : 'border-gray-200 hover:border-gray-300'\n                } ${'recommended' in option && option.recommended ? 'ring-2 ring-blue-200' : ''}`}\n                onClick={() => handleOptionChange(option.id)}\n              >\n              {'recommended' in option && option.recommended && (\n                <div className=\"absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium\">\n                  {t('recommended')}\n                </div>\n              )}\n              \n              <div className=\"flex items-center justify-between mb-4 flex-1\">\n                <div className=\"flex items-center\">\n                  {option.id === 'free' ? (\n                    <Calendar className=\"h-6 w-6 text-gray-600 mr-3\" />\n                  ) : (\n                    <CreditCard className=\"h-6 w-6 text-blue-600 mr-3\" />\n                  )}\n                  <div>\n                    <h4 className=\"text-lg font-semibold text-gray-900\">{t(`plans.${option.id}.title`)}</h4>\n                    <p className=\"text-sm text-gray-600\">{t(`plans.${option.id}.description`)}</p>\n                  </div>\n                </div>\n                \n                <div className=\"text-right\">\n                  {option.id === 'paid' && hasActivePromotion() ? (\n                    <div className=\"text-right\">\n                      {/* 早鸟价标签 */}\n                      <div className=\"inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-semibold mb-3 shadow-lg\">\n                        <Tag className=\"h-3 w-3 mr-1\" />\n                        {t('promotion.early_bird')}\n                      </div>\n                      {/* 价格展示 */}\n                      <div className=\"space-y-1\">\n                        <div className=\"text-sm text-gray-500\">\n                          {t('promotion.original_price')} <span className=\"line-through font-medium text-gray-400\">{formatOriginalPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice, locale)}</span>\n                        </div>\n                        <div className=\"text-3xl font-bold text-gray-900\">\n                          {formatPrice(option.price, locale)}\n                        </div>\n                        {/* 折扣信息 */}\n                        <div className=\"inline-flex items-center px-2 py-1 rounded-full bg-red-100 text-red-600 text-xs font-bold\">\n                          Save {getDiscountPercentage()}%\n                        </div>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"text-2xl font-bold text-gray-900\">\n                      {formatPrice(option.price, locale)}\n                    </div>\n                  )}\n                </div>\n              </div>\n              \n              <ul className=\"space-y-2\">\n                {option.features.map((_, index) => (\n                  <li key={index} className=\"flex items-center text-sm text-gray-600\">\n                    <CheckCircle className=\"h-4 w-4 text-green-500 mr-2 flex-shrink-0\" />\n                    {t(`plans.${option.id}.features.${index}`)}\n                  </li>\n                ))}\n              </ul>\n              \n              <div className=\"mt-4\">\n                <input\n                  type=\"radio\"\n                  name=\"launchOption\"\n                  value={option.id}\n                  checked={selectedOption === option.id}\n                  onChange={() => handleOptionChange(option.id)}\n                  className=\"sr-only\"\n                />\n                <div className={`w-4 h-4 rounded-full border-2 ${\n                  selectedOption === option.id\n                    ? 'border-blue-500 bg-blue-500'\n                    : 'border-gray-300'\n                }`}>\n                  {selectedOption === option.id && (\n                    <div className=\"w-2 h-2 bg-white rounded-full mx-auto mt-0.5\"></div>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n      )}\n\n      {/* Badge验证选项 - 仅在免费选项且未付费且未验证时显示 */}\n      {!hasPaidOrder && selectedOption === 'free' && !isBadgeVerified && toolWebsite && (\n        <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6\">\n          <div className=\"flex items-start\">\n            <Award className=\"h-6 w-6 text-blue-600 mr-3 mt-0.5\" />\n            <div className=\"flex-1\">\n              <h4 className=\"text-lg font-semibold text-blue-900 mb-2\">{t('badge_option_title')}</h4>\n              <p className=\"text-blue-700 mb-4\">{t('badge_option_description')}</p>\n              <button\n                onClick={() => setShowBadgeVerification(true)}\n                className=\"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center gap-2\"\n              >\n                <Award className=\"h-4 w-4\" />\n                {t('badge_option_button')}\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Badge验证成功提示 */}\n      {isBadgeVerified && (\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\">\n          <div className=\"flex items-center\">\n            <CheckCircle className=\"h-5 w-5 text-green-500 mr-2\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-green-800\">Badge验证成功！</h4>\n              <p className=\"text-sm text-green-600 mt-1\">您现在可以选择任意发布日期，无需等待一个月。</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Badge验证组件 */}\n      {showBadgeVerification && (\n        <div className=\"mb-6\">\n          <BadgeVerification\n            toolId={toolId}\n            toolWebsite={toolWebsite}\n            onVerificationSuccess={handleBadgeVerificationSuccess}\n          />\n        </div>\n      )}\n\n      {/* 已付费用户的提示信息 */}\n      {hasPaidOrder && (\n        <div className=\"bg-green-50 border border-green-200 rounded-lg p-4 mb-6\">\n          <div className=\"flex items-center\">\n            <CheckCircle className=\"h-5 w-5 text-green-500 mr-2\" />\n            <div>\n              <h4 className=\"text-sm font-medium text-green-800\">{t('priority_service_activated_title')}</h4>\n              <p className=\"text-sm text-green-600 mt-1\">{t('priority_service_activated_description')}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* 日期选择 */}\n      <div>\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n          <Clock className=\"h-5 w-5 mr-2\" />\n          {t('select_date')}\n        </h3>\n        \n        <div className=\"max-w-md\">\n          <input\n            type=\"date\"\n            value={selectedDate}\n            onChange={(e) => setSelectedDate(e.target.value)}\n            min={hasPaidOrder || selectedOption === 'paid' ? getMinPaidDate() : getMinFreeDate()}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          \n          <p className=\"text-sm text-gray-500 mt-2\">\n            {hasPaidOrder\n              ? t('paid_date_info')\n              : selectedOption === 'free'\n              ? isBadgeVerified\n                ? t('paid_date_info') + ' (Badge已验证)'\n                : t('free_date_info')\n              : t('paid_date_info')\n            }\n          </p>\n        </div>\n      </div>\n\n      {/* 提交按钮 */}\n      <div className=\"text-center\">\n        <button\n          onClick={handleSubmit}\n          disabled={isSubmitting || !selectedDate}\n          className=\"bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto\"\n        >\n          {isSubmitting ? (\n            <>\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              {selectedOption === 'paid' ? t('processing') : t('saving')}\n            </>\n          ) : (\n            <>\n              {hasPaidOrder ? (\n                <>\n                  <CheckCircle className=\"h-4 w-4 mr-2\" />\n                  {t('save_changes')}\n                </>\n              ) : selectedOption === 'paid' ? (\n                <>\n                  <CreditCard className=\"h-4 w-4 mr-2\" />\n                  {hasActivePromotion() ? (\n                    <span className=\"flex items-center\">\n                      {isEditing ? (locale === 'zh' ? '升级并支付 ' : 'Upgrade and Pay ') : (locale === 'zh' ? '立即支付 ' : 'Pay Now ')}\n                      <span className=\"line-through text-blue-200 mx-1 text-sm\">\n                        {formatOriginalPrice(PRICING_CONFIG.PRIORITY_LAUNCH.originalPrice, locale)}\n                      </span>\n                      <span className=\"font-bold\">\n                        {formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice, locale)}\n                      </span>\n                      <span className=\"ml-2 px-2 py-0.5 bg-amber-400 text-amber-900 text-xs rounded-full font-medium\">\n                        {t('promotion.early_bird')}\n                      </span>\n                    </span>\n                  ) : (\n                    <span>\n                      {isEditing ? t('upgrade_and_pay', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice, locale) }) : t('pay_amount', { price: formatPrice(PRICING_CONFIG.PRIORITY_LAUNCH.displayPrice, locale) })}\n                    </span>\n                  )}\n                </>\n              ) : (\n                <>\n                  <CheckCircle className=\"h-4 w-4 mr-2\" />\n                  {isEditing ? t('save_changes') : t('confirm_date')}\n                </>\n              )}\n            </>\n          )}\n        </button>\n        \n        {error && (\n          <p className=\"text-red-600 text-sm mt-4\">{error}</p>\n        )}\n        \n        <p className=\"text-gray-500 text-sm mt-4\">\n          {hasPaidOrder\n            ? t('changes_effective')\n            : selectedOption === 'paid'\n            ? t('payment_redirect')\n            : isEditing\n              ? t('changes_effective')\n              : t('review_queue')\n          }\n        </p>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AASA,cAAc;AACd,MAAM,gBAAgB,8HAAA,CAAA,iBAAc;AAerB,SAAS,mBAAmB,EACzC,MAAM,EACN,WAAW,EACX,gBAAgB,MAAM,EACtB,WAAW,EACX,YAAY,KAAK,EACjB,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,eAAe,KAAK,EACpB,gBAAgB,KAAK,EACG;;IACxB,sBAAsB;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD,eAAe,SAAS;IAE1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,UAAU;IACV,MAAM,wBAAwB;QAC5B,IAAI,CAAC,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,KAAK,OAAO;QAElC,MAAM,gBAAgB,8HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,aAAa;QAClE,MAAM,eAAe,8HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,YAAY;QAChE,MAAM,WAAW,KAAK,KAAK,CAAC,AAAC,CAAC,gBAAgB,YAAY,IAAI,gBAAiB;QAC/E,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,iBAAiB;QACrB,IAAI,iBAAiB;YACnB,uBAAuB;YACvB,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAC9B,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACzC;QACA,MAAM,OAAO,IAAI;QACjB,KAAK,QAAQ,CAAC,KAAK,QAAQ,KAAK;QAChC,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACzC;IAEA,mBAAmB;IACnB,MAAM,iBAAiB;QACrB,MAAM,OAAO,IAAI;QACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;QAC9B,OAAO,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACzC;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,IAAI,aAAa;gBACf,gBAAgB;YAClB,OAAO;gBACL,gBAAgB;gBAChB,IAAI,mBAAmB,QAAQ;oBAC7B,gBAAgB;gBAClB,OAAO;oBACL,gBAAgB;gBAClB;YACF;QACF;uCAAG;QAAC;QAAgB;KAAY;IAEhC,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB;QACjB,IAAI,cAAc;YAChB;QACF;QAEA,kBAAkB;QAClB,gBAAgB;QAChB,IAAI,WAAW,QAAQ;YACrB,gBAAgB;QAClB,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc;YACjB;QACF;QACA,MAAM,SAAS,gBAAgB;IACjC;IAEA,MAAM,iCAAiC;QACrC,mBAAmB;QACnB,yBAAyB;QACzB,sBAAsB;QACtB,IAAI,mBAAmB,QAAQ;YAC7B,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAC9B,gBAAgB,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAClD;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,CAAC,8BACA,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCACX,YAAY,EAAE,iBAAiB,EAAE;;;;;;kCAEpC,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;gCAEC,WAAW,CAAC,6EAA6E,EACvF,mBAAmB,OAAO,EAAE,GACxB,+BACA,wCACL,CAAC,EAAE,iBAAiB,UAAU,OAAO,WAAW,GAAG,yBAAyB,IAAI;gCACjF,SAAS,IAAM,mBAAmB,OAAO,EAAE;;oCAE5C,iBAAiB,UAAU,OAAO,WAAW,kBAC5C,6LAAC;wCAAI,WAAU;kDACZ,EAAE;;;;;;kDAIP,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,OAAO,EAAE,KAAK,uBACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;6EAEpB,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;kEAExB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAuC,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC;;;;;;0EACjF,6LAAC;gEAAE,WAAU;0EAAyB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,YAAY,CAAC;;;;;;;;;;;;;;;;;;0DAI5E,6LAAC;gDAAI,WAAU;0DACZ,OAAO,EAAE,KAAK,UAAU,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,oBACxC,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,mMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEACd,EAAE;;;;;;;sEAGL,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEACZ,EAAE;wEAA4B;sFAAC,6LAAC;4EAAK,WAAU;sFAA0C,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,8HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,aAAa,EAAE;;;;;;;;;;;;8EAE9J,6LAAC;oEAAI,WAAU;8EACZ,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK,EAAE;;;;;;8EAG7B,6LAAC;oEAAI,WAAU;;wEAA4F;wEACnG;wEAAwB;;;;;;;;;;;;;;;;;;yEAKpC,6LAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK,EAAE;;;;;;;;;;;;;;;;;kDAMnC,6LAAC;wCAAG,WAAU;kDACX,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,sBACvB,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDACtB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,UAAU,EAAE,OAAO;;+CAFlC;;;;;;;;;;kDAOb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,OAAO,EAAE;gDAChB,SAAS,mBAAmB,OAAO,EAAE;gDACrC,UAAU,IAAM,mBAAmB,OAAO,EAAE;gDAC5C,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAW,CAAC,8BAA8B,EAC7C,mBAAmB,OAAO,EAAE,GACxB,gCACA,mBACJ;0DACC,mBAAmB,OAAO,EAAE,kBAC3B,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BAjFd,OAAO,EAAE;;;;;;;;;;;;;;;;YA4FvB,CAAC,gBAAgB,mBAAmB,UAAU,CAAC,mBAAmB,6BACjE,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4C,EAAE;;;;;;8CAC5D,6LAAC;oCAAE,WAAU;8CAAsB,EAAE;;;;;;8CACrC,6LAAC;oCACC,SAAS,IAAM,yBAAyB;oCACxC,WAAU;;sDAEV,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAChB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;YAQZ,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,6LAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;YAOlD,uCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,0IAAA,CAAA,UAAiB;oBAChB,QAAQ;oBACR,aAAa;oBACb,uBAAuB;;;;;;;;;;;YAM5B,8BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsC,EAAE;;;;;;8CACtD,6LAAC;oCAAE,WAAU;8CAA+B,EAAE;;;;;;;;;;;;;;;;;;;;;;;0BAOtD,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAChB,EAAE;;;;;;;kCAGL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,KAAK,gBAAgB,mBAAmB,SAAS,mBAAmB;gCACpE,WAAU;;;;;;0CAGZ,6LAAC;gCAAE,WAAU;0CACV,eACG,EAAE,oBACF,mBAAmB,SACnB,kBACE,EAAE,oBAAoB,gBACtB,EAAE,oBACJ,EAAE;;;;;;;;;;;;;;;;;;0BAOZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;wBACT,UAAU,gBAAgB,CAAC;wBAC3B,WAAU;kCAET,6BACC;;8CACE,6LAAC;oCAAI,WAAU;;;;;;gCACd,mBAAmB,SAAS,EAAE,gBAAgB,EAAE;;yDAGnD;sCACG,6BACC;;kDACE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,EAAE;;+CAEH,mBAAmB,uBACrB;;kDACE,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCACrB,CAAA,GAAA,8HAAA,CAAA,qBAAkB,AAAD,oBAChB,6LAAC;wCAAK,WAAU;;4CACb,YAAa,WAAW,OAAO,WAAW,qBAAuB,WAAW,OAAO,UAAU;0DAC9F,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EAAE,8HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,aAAa,EAAE;;;;;;0DAErE,6LAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,8HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,YAAY,EAAE;;;;;;0DAE5D,6LAAC;gDAAK,WAAU;0DACb,EAAE;;;;;;;;;;;6DAIP,6LAAC;kDACE,YAAY,EAAE,mBAAmB;4CAAE,OAAO,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,8HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,YAAY,EAAE;wCAAQ,KAAK,EAAE,cAAc;4CAAE,OAAO,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD,EAAE,8HAAA,CAAA,iBAAc,CAAC,eAAe,CAAC,YAAY,EAAE;wCAAQ;;;;;;;6DAKjN;;kDACE,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;oCACtB,YAAY,EAAE,kBAAkB,EAAE;;;;;;;;;oBAO5C,uBACC,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG5C,6LAAC;wBAAE,WAAU;kCACV,eACG,EAAE,uBACF,mBAAmB,SACnB,EAAE,sBACF,YACE,EAAE,uBACF,EAAE;;;;;;;;;;;;;;;;;;AAMlB;GAnWwB;;QAoBZ,yMAAA,CAAA,kBAAe;QACV,qKAAA,CAAA,YAAS;;;KArBF", "debugId": null}}, {"offset": {"line": 1623, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/workspace/aitools/aitools-website/src/app/%5Blocale%5D/demo-launch-selector/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport LaunchDateSelector from '@/components/LaunchDateSelector';\nimport { useTranslations } from 'next-intl';\n\nexport default function DemoLaunchSelectorPage() {\n  const [selectedOption, setSelectedOption] = useState<'free' | 'paid'>('free');\n  const [selectedDate, setSelectedDate] = useState<string>('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [error, setError] = useState('');\n  const [hasPaidOrder, setHasPaidOrder] = useState(false);\n  const [badgeVerified, setBadgeVerified] = useState(false);\n\n  const t = useTranslations('launch');\n\n  const handleSubmit = async (option: 'free' | 'paid', date: string) => {\n    setIsSubmitting(true);\n    setError('');\n    \n    // 模拟API调用\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    if (option === 'paid') {\n      // 模拟支付流程\n      alert(`模拟支付流程：选择了付费选项，发布日期：${date}`);\n    } else {\n      // 模拟免费提交\n      alert(`模拟免费提交：发布日期：${date}`);\n    }\n    \n    setSelectedOption(option);\n    setSelectedDate(date);\n    setIsSubmitting(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-4\">\n            Launch Date Selector 演示\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            展示带有Badge验证功能的发布日期选择器\n          </p>\n        </div>\n\n        {/* 控制面板 */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">演示控制</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                付费状态\n              </label>\n              <button\n                onClick={() => setHasPaidOrder(!hasPaidOrder)}\n                className={`w-full px-4 py-2 rounded-lg font-medium transition-colors ${\n                  hasPaidOrder \n                    ? 'bg-green-600 text-white' \n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                }`}\n              >\n                {hasPaidOrder ? '已付费' : '未付费'}\n              </button>\n            </div>\n            \n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Badge验证状态\n              </label>\n              <button\n                onClick={() => setBadgeVerified(!badgeVerified)}\n                className={`w-full px-4 py-2 rounded-lg font-medium transition-colors ${\n                  badgeVerified \n                    ? 'bg-blue-600 text-white' \n                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                }`}\n              >\n                {badgeVerified ? 'Badge已验证' : 'Badge未验证'}\n              </button>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                重置状态\n              </label>\n              <button\n                onClick={() => {\n                  setHasPaidOrder(false);\n                  setBadgeVerified(false);\n                  setError('');\n                }}\n                className=\"w-full px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors\"\n              >\n                重置\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* LaunchDateSelector 组件 */}\n        <div className=\"bg-white rounded-lg shadow-sm border border-gray-200 p-8\">\n          <LaunchDateSelector\n            toolId=\"demo-tool-id\"\n            toolWebsite=\"https://example.com\"\n            currentOption={selectedOption}\n            currentDate={selectedDate}\n            isEditing={false}\n            onSubmit={handleSubmit}\n            isSubmitting={isSubmitting}\n            error={error}\n            hasPaidOrder={hasPaidOrder}\n            badgeVerified={badgeVerified}\n          />\n        </div>\n\n        {/* 状态显示 */}\n        <div className=\"bg-blue-50 rounded-lg border border-blue-200 p-6 mt-8\">\n          <h3 className=\"text-lg font-semibold text-blue-900 mb-4\">当前状态</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n            <div>\n              <span className=\"font-medium text-blue-800\">选择的选项：</span>\n              <span className=\"text-blue-600 ml-2\">{selectedOption || '未选择'}</span>\n            </div>\n            <div>\n              <span className=\"font-medium text-blue-800\">选择的日期：</span>\n              <span className=\"text-blue-600 ml-2\">{selectedDate || '未选择'}</span>\n            </div>\n            <div>\n              <span className=\"font-medium text-blue-800\">付费状态：</span>\n              <span className=\"text-blue-600 ml-2\">{hasPaidOrder ? '已付费' : '未付费'}</span>\n            </div>\n            <div>\n              <span className=\"font-medium text-blue-800\">Badge状态：</span>\n              <span className=\"text-blue-600 ml-2\">{badgeVerified ? '已验证' : '未验证'}</span>\n            </div>\n          </div>\n        </div>\n\n        {/* 功能说明 */}\n        <div className=\"bg-gray-50 rounded-lg border border-gray-200 p-6 mt-8\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">功能说明</h3>\n          <div className=\"space-y-3 text-sm text-gray-700\">\n            <div>\n              <strong>免费发布 + Badge未验证：</strong> 只能选择一个月后的日期\n            </div>\n            <div>\n              <strong>免费发布 + Badge已验证：</strong> 可以选择明天开始的任意日期，显示badge验证成功提示\n            </div>\n            <div>\n              <strong>付费发布：</strong> 可以选择明天开始的任意日期\n            </div>\n            <div>\n              <strong>已付费：</strong> 显示优先服务激活提示，可以选择任意日期\n            </div>\n            <div>\n              <strong>Badge验证流程：</strong> 在免费选项下会显示badge获取选项，用户可以通过验证badge来解锁立即发布权限\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,MAAM,eAAe,OAAO,QAAyB;QACnD,gBAAgB;QAChB,SAAS;QAET,UAAU;QACV,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,IAAI,WAAW,QAAQ;YACrB,SAAS;YACT,MAAM,CAAC,oBAAoB,EAAE,MAAM;QACrC,OAAO;YACL,SAAS;YACT,MAAM,CAAC,YAAY,EAAE,MAAM;QAC7B;QAEA,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,SAAS,IAAM,gBAAgB,CAAC;4CAChC,WAAW,CAAC,0DAA0D,EACpE,eACI,4BACA,+CACJ;sDAED,eAAe,QAAQ;;;;;;;;;;;;8CAI5B,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,SAAS,IAAM,iBAAiB,CAAC;4CACjC,WAAW,CAAC,0DAA0D,EACpE,gBACI,2BACA,+CACJ;sDAED,gBAAgB,aAAa;;;;;;;;;;;;8CAIlC,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,SAAS;gDACP,gBAAgB;gDAChB,iBAAiB;gDACjB,SAAS;4CACX;4CACA,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,2IAAA,CAAA,UAAkB;wBACjB,QAAO;wBACP,aAAY;wBACZ,eAAe;wBACf,aAAa;wBACb,WAAW;wBACX,UAAU;wBACV,cAAc;wBACd,OAAO;wBACP,cAAc;wBACd,eAAe;;;;;;;;;;;8BAKnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;sDAAsB,kBAAkB;;;;;;;;;;;;8CAE1D,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;sDAAsB,gBAAgB;;;;;;;;;;;;8CAExD,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;sDAAsB,eAAe,QAAQ;;;;;;;;;;;;8CAE/D,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAA4B;;;;;;sDAC5C,6LAAC;4CAAK,WAAU;sDAAsB,gBAAgB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;8BAMpE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAyB;;;;;;;8CAEnC,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAyB;;;;;;;8CAEnC,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAc;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAa;;;;;;;8CAEvB,6LAAC;;sDACC,6LAAC;sDAAO;;;;;;wCAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC;GA/JwB;;QAQZ,yMAAA,CAAA,kBAAe;;;KARH", "debugId": null}}]}