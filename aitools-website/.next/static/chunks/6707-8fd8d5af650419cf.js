"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6707],{646:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},1154:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},1586:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},3332:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3467:(e,t,r)=>{r.d(t,{$g:()=>o,Ef:()=>c,S9:()=>m,Y$:()=>n,kX:()=>a,mV:()=>d,mp:()=>h,sT:()=>x,tF:()=>u,v4:()=>i,vS:()=>s});let a={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}},s=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"All Prices"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],n=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],c=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},d=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},o=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),m=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),x=()=>a.PRIORITY_LAUNCH.promotion,h=()=>{let e=a.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},3786:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},4186:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},5339:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6707:(e,t,r)=>{r.d(t,{A:()=>I});var a=r(5155),s=r(2115),l=r(7652),i=r(3385),n=r(9074),c=r(1586),d=r(3332),o=r(646),m=r(9946);let x=(0,m.A)("award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);var h=r(4186),u=r(3467);let p=(0,m.A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),g=(0,m.A)("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),b=(0,m.A)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);var f=r(3786),y=r(5339),v=r(1154);let j={light:{container:"bg-white border-2 border-purple-500 text-purple-600",icon:"text-purple-500",text:"text-purple-600",accent:"bg-purple-500"},dark:{container:"bg-gray-900 border-2 border-yellow-400 text-yellow-400",icon:"text-yellow-400",text:"text-yellow-400",accent:"bg-yellow-400"},neutral:{container:"bg-gray-50 border-2 border-gray-400 text-gray-700",icon:"text-gray-600",text:"text-gray-700",accent:"bg-gray-400"}},N={sm:{container:"px-3 py-2 text-xs",icon:"h-3 w-3",spacing:"gap-1.5"},md:{container:"px-4 py-2.5 text-sm",icon:"h-4 w-4",spacing:"gap-2"},lg:{container:"px-6 py-3 text-base",icon:"h-5 w-5",spacing:"gap-2.5"}};function w(e){let{theme:t="light",size:r="md",showCopyButton:l=!1,toolName:i="Your Tool",className:n=""}=e,[c,d]=(0,s.useState)(!1),o=j[t],m=N[r],h=async()=>{try{await navigator.clipboard.writeText(k(t,r)),d(!0),setTimeout(()=>d(!1),2e3)}catch(e){console.error("Failed to copy:",e)}};return(0,a.jsxs)("div",{className:"inline-flex items-center ".concat(n),children:[(0,a.jsxs)("a",{href:"https://aitools.pub",target:"_blank",rel:"noopener noreferrer",className:"\n          inline-flex items-center rounded-lg font-semibold transition-all duration-200 hover:scale-105 hover:shadow-lg\n          ".concat(o.container," ").concat(m.container," ").concat(m.spacing,"\n        "),children:[(0,a.jsx)(x,{className:"".concat(o.icon," ").concat(m.icon)}),(0,a.jsx)("span",{className:o.text,children:"Featured on AITools"})]}),l&&(0,a.jsx)("button",{onClick:h,className:"ml-2 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors",title:"Copy embed code",children:c?(0,a.jsx)(p,{className:"h-4 w-4 text-green-500"}):(0,a.jsx)(g,{className:"h-4 w-4"})})]})}function k(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"md",r=window.location.origin;return'<a href="'.concat(r,'" target="_blank" rel="noopener noreferrer" style="display: inline-flex; align-items: center; padding: ').concat("sm"===t?"8px 12px":"md"===t?"10px 16px":"12px 24px","; border-radius: 8px; text-decoration: none; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-weight: 600; font-size: ").concat("sm"===t?"12px":"md"===t?"14px":"16px","; gap: ").concat("sm"===t?"6px":"8px","; ").concat("light"===e?"background-color: #ffffff; border: 2px solid #9333ea; color: #9333ea;":"dark"===e?"background-color: #111827; border: 2px solid #fbbf24; color: #fbbf24;":"background-color: #f9fafb; border: 2px solid #6b7280; color: #374151;",' transition: all 0.2s ease;">\n  <svg width="').concat("sm"===t?"12":"md"===t?"16":"20",'" height="').concat("sm"===t?"12":"md"===t?"16":"20",'" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">\n    <path d="m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"/>\n    <circle cx="12" cy="8" r="6"/>\n  </svg>\n  Featured on AITools\n</a>')}function A(e){let{toolId:t,toolWebsite:r,onVerificationSuccess:i,className:n=""}=e,[c,d]=(0,s.useState)("light"),[m,h]=(0,s.useState)(!1),[u,j]=(0,s.useState)(!1),[N,A]=(0,s.useState)("idle"),[_,I]=(0,s.useState)(""),E=(0,l.c3)("badge_verification"),R=[{value:"light",label:E("themes.light.label"),description:E("themes.light.description")},{value:"dark",label:E("themes.dark.label"),description:E("themes.dark.description")},{value:"neutral",label:E("themes.neutral.label"),description:E("themes.neutral.description")}],F=async()=>{try{let e=k(c,"lg");await navigator.clipboard.writeText(e),h(!0),setTimeout(()=>h(!1),2e3)}catch(e){console.error("Failed to copy:",e)}},C=async()=>{if(!r)return void I(E("errors.no_website"));j(!0),I(""),A("idle");try{let e=await fetch("/api/tools/verify-badge",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({toolId:t,website:r,expectedText:"Featured on AITools"})}),a=await e.json();a.success?(A("success"),setTimeout(()=>{i()},1500)):(A("error"),I(a.error||E("errors.verification_failed")))}catch(e){A("error"),I(E("errors.network_error"))}finally{j(!1)}};return(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200 ".concat(n),children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsx)(x,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:E("title")}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:E("subtitle")})]})]}),(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3",children:"1"}),(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"选择Badge主题"})]}),(0,a.jsx)("div",{className:"mb-4 ml-11",children:(0,a.jsx)("div",{className:"flex border-b border-gray-200",children:R.map(e=>(0,a.jsx)("button",{onClick:()=>d(e.value),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat(c===e.value?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700"),children:e.label},e.value))})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-200 mb-4 ml-11",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:"预览："}),(0,a.jsx)(w,{theme:c,size:"lg"})]}),(0,a.jsx)("div",{className:"ml-11",children:(0,a.jsx)("button",{onClick:F,className:"w-full bg-blue-600 text-white px-4 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center justify-center gap-2",children:m?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p,{className:"h-4 w-4"}),"已复制!"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g,{className:"h-4 w-4"}),"复制嵌入代码"]})})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3",children:"2"}),(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"添加到您的网站"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 ml-11",children:"将复制的代码添加到您网站的首页，通常放在页脚或侧边栏位置。"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-600 text-white flex items-center justify-center text-sm font-medium mr-3",children:"3"}),(0,a.jsx)("h4",{className:"text-md font-medium text-gray-900",children:"验证Badge安装"})]}),r&&(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-200 mb-4 ml-11",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600 mb-2",children:[(0,a.jsx)(b,{className:"h-4 w-4"}),"您的网站："]}),(0,a.jsxs)("a",{href:r,target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:text-blue-800 flex items-center gap-1",children:[r,(0,a.jsx)(f.A,{className:"h-3 w-3"})]})]}),"success"===N&&(0,a.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-4 ml-11",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-green-800",children:[(0,a.jsx)(o.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"Badge验证成功！"})]}),(0,a.jsx)("p",{className:"text-sm text-green-600 mt-1",children:"您现在可以选择任意发布日期，无需等待一个月。"})]}),"error"===N&&(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4 ml-11",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-red-800",children:[(0,a.jsx)(y.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"font-medium",children:"验证失败"})]}),(0,a.jsx)("p",{className:"text-sm text-red-600 mt-1",children:_})]}),(0,a.jsx)("div",{className:"ml-11",children:(0,a.jsx)("button",{onClick:C,disabled:u||"success"===N,className:"bg-blue-600 text-white px-6 py-2 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 transition-colors",children:u?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"h-4 w-4 animate-spin"}),"验证中..."]}):"success"===N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),"已验证！"]}):"验证Badge"})})]})]})]})}let _=u.vS;function I(e){let{toolId:t,toolWebsite:r,currentOption:m="paid",currentDate:p,isEditing:g=!1,onSubmit:b,isSubmitting:f,error:y,hasPaidOrder:v=!1,badgeVerified:j=!1}=e,[N,w]=(0,s.useState)(v?"paid":m),[k,I]=(0,s.useState)(""),[E,R]=(0,s.useState)(!1),[F,C]=(0,s.useState)(j),P=(0,l.c3)("launch"),S=(0,i.Ym)(),M=()=>{if(!(0,u.mp)())return 0;let e=u.kX.PRIORITY_LAUNCH.originalPrice;return Math.round((e-u.kX.PRIORITY_LAUNCH.displayPrice)/e*100)},U=()=>{if(F){let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]}let e=new Date;return e.setMonth(e.getMonth()+1),e.toISOString().split("T")[0]},T=()=>{let e=new Date;return e.setDate(e.getDate()+1),e.toISOString().split("T")[0]};(0,s.useEffect)(()=>{p?I(p):"free"===N?I(U()):I(T())},[N,p]);let L=e=>{v||(w(e),"free"===e?I(U()):I(T()))},D=async()=>{k&&await b(N,k)};return(0,a.jsxs)("div",{className:"space-y-8",children:[!v&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:g?P("select_plan"):P("select_option")}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 gap-6",children:_.map(e=>(0,a.jsxs)("div",{className:"relative border-2 rounded-lg p-6 cursor-pointer transition-all flex flex-col ".concat(N===e.id?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300"," ").concat("recommended"in e&&e.recommended?"ring-2 ring-blue-200":""),onClick:()=>L(e.id),children:["recommended"in e&&e.recommended&&(0,a.jsx)("div",{className:"absolute -top-3 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:P("recommended")}),(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4 flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:["free"===e.id?(0,a.jsx)(n.A,{className:"h-6 w-6 text-gray-600 mr-3"}):(0,a.jsx)(c.A,{className:"h-6 w-6 text-blue-600 mr-3"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:P("plans.".concat(e.id,".title"))}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:P("plans.".concat(e.id,".description"))})]})]}),(0,a.jsx)("div",{className:"text-right",children:"paid"===e.id&&(0,u.mp)()?(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"inline-flex items-center px-3 py-1 rounded-full bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-semibold mb-3 shadow-lg",children:[(0,a.jsx)(d.A,{className:"h-3 w-3 mr-1"}),P("promotion.early_bird")]}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[P("promotion.original_price")," ",(0,a.jsx)("span",{className:"line-through font-medium text-gray-400",children:(0,u.S9)(u.kX.PRIORITY_LAUNCH.originalPrice,S)})]}),(0,a.jsx)("div",{className:"text-3xl font-bold text-gray-900",children:(0,u.$g)(e.price,S)}),(0,a.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full bg-red-100 text-red-600 text-xs font-bold",children:["Save ",M(),"%"]})]})]}):(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,u.$g)(e.price,S)})})]}),(0,a.jsx)("ul",{className:"space-y-2",children:e.features.map((t,r)=>(0,a.jsxs)("li",{className:"flex items-center text-sm text-gray-600",children:[(0,a.jsx)(o.A,{className:"h-4 w-4 text-green-500 mr-2 flex-shrink-0"}),P("plans.".concat(e.id,".features.").concat(r))]},r))}),(0,a.jsxs)("div",{className:"mt-4",children:[(0,a.jsx)("input",{type:"radio",name:"launchOption",value:e.id,checked:N===e.id,onChange:()=>L(e.id),className:"sr-only"}),(0,a.jsx)("div",{className:"w-4 h-4 rounded-full border-2 ".concat(N===e.id?"border-blue-500 bg-blue-500":"border-gray-300"),children:N===e.id&&(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full mx-auto mt-0.5"})})]})]},e.id))})]}),!v&&"free"===N&&!F&&r&&(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(x,{className:"h-6 w-6 text-blue-600 mr-3 mt-0.5"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-blue-900 mb-2",children:P("badge_option_title")}),(0,a.jsx)("p",{className:"text-blue-700 mb-4",children:P("badge_option_description")}),(0,a.jsxs)("button",{onClick:()=>R(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors flex items-center gap-2",children:[(0,a.jsx)(x,{className:"h-4 w-4"}),P("badge_option_button")]})]})]})}),F&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-green-800",children:"Badge验证成功！"}),(0,a.jsx)("p",{className:"text-sm text-green-600 mt-1",children:"您现在可以选择任意发布日期，无需等待一个月。"})]})]})}),E&&(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(A,{toolId:t,toolWebsite:r,onVerificationSuccess:()=>{if(C(!0),R(!1),"free"===N){let e=new Date;e.setDate(e.getDate()+1),I(e.toISOString().split("T")[0])}}})}),v&&(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-green-500 mr-2"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-green-800",children:P("priority_service_activated_title")}),(0,a.jsx)("p",{className:"text-sm text-green-600 mt-1",children:P("priority_service_activated_description")})]})]})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-4 flex items-center",children:[(0,a.jsx)(h.A,{className:"h-5 w-5 mr-2"}),P("select_date")]}),(0,a.jsxs)("div",{className:"max-w-md",children:[(0,a.jsx)("input",{type:"date",value:k,onChange:e=>I(e.target.value),min:v||"paid"===N?T():U(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:v?P("paid_date_info"):"free"===N?F?P("paid_date_info")+" (Badge已验证)":P("free_date_info"):P("paid_date_info")})]})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("button",{onClick:D,disabled:f||!k,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mx-auto",children:f?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"paid"===N?P("processing"):P("saving")]}):(0,a.jsx)(a.Fragment,{children:v?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),P("save_changes")]}):"paid"===N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(c.A,{className:"h-4 w-4 mr-2"}),(0,u.mp)()?(0,a.jsxs)("span",{className:"flex items-center",children:[g?"zh"===S?"升级并支付 ":"Upgrade and Pay ":"zh"===S?"立即支付 ":"Pay Now ",(0,a.jsx)("span",{className:"line-through text-blue-200 mx-1 text-sm",children:(0,u.S9)(u.kX.PRIORITY_LAUNCH.originalPrice,S)}),(0,a.jsx)("span",{className:"font-bold",children:(0,u.$g)(u.kX.PRIORITY_LAUNCH.displayPrice,S)}),(0,a.jsx)("span",{className:"ml-2 px-2 py-0.5 bg-amber-400 text-amber-900 text-xs rounded-full font-medium",children:P("promotion.early_bird")})]}):(0,a.jsx)("span",{children:g?P("upgrade_and_pay",{price:(0,u.$g)(u.kX.PRIORITY_LAUNCH.displayPrice,S)}):P("pay_amount",{price:(0,u.$g)(u.kX.PRIORITY_LAUNCH.displayPrice,S)})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{className:"h-4 w-4 mr-2"}),g?P("save_changes"):P("confirm_date")]})})}),y&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-4",children:y}),(0,a.jsx)("p",{className:"text-gray-500 text-sm mt-4",children:v?P("changes_effective"):"paid"===N?P("payment_redirect"):g?P("changes_effective"):P("review_queue")})]})]})}},7652:(e,t,r)=>{r.d(t,{c3:()=>l});var a=r(3385);function s(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let l=s(0,a.c3);s(0,a.kc)},9074:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9946:(e,t,r)=>{r.d(t,{A:()=>m});var a=r(2115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),i=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:i,className:o="",children:m,iconNode:x,...h}=e;return(0,a.createElement)("svg",{ref:t,...d,width:s,height:s,stroke:r,strokeWidth:i?24*Number(l)/Number(s):l,className:n("lucide",o),...!m&&!c(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let r=(0,a.forwardRef)((r,l)=>{let{className:c,...d}=r;return(0,a.createElement)(o,{ref:l,iconNode:t,className:n("lucide-".concat(s(i(e))),"lucide-".concat(e),c),...d})});return r.displayName=i(e),r}}}]);