(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8493],{2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>i,a8:()=>o,rd:()=>d});var a=r(9984),s=r(981);let l=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:i,redirect:n,usePathname:o,useRouter:d}=(0,s.A)(l)},2731:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var a=r(5155);function s(e){let{size:t="md",className:r=""}=e;return(0,a.jsx)("div",{className:"flex justify-center items-center ".concat(r),children:(0,a.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},3467:(e,t,r)=>{"use strict";r.d(t,{$g:()=>c,Ef:()=>o,S9:()=>m,Y$:()=>n,kX:()=>a,mV:()=>d,mp:()=>u,sT:()=>x,tF:()=>g,v4:()=>i,vS:()=>s});let a={PRIORITY_LAUNCH:{displayPrice:19.9,originalPrice:49.9,stripeAmount:1990,originalStripeAmount:4990,currency:"USD",stripeCurrency:"usd",productName:"AI Tool Priority Launch Service",description:"Get your AI tool prioritized for review and featured placement",promotion:{enabled:!0,description:"Limited-time offer - First 100 paid users",discountPercent:50,remainingSlots:85},features:["Choose any publish date","Priority review processing","Featured homepage placement","Dedicated customer support"]},FREE_LAUNCH:{displayPrice:0,stripeAmount:0,currency:"USD",stripeCurrency:"usd",productName:"Free Launch Service",description:"Choose any publish date after one month",features:["Free submission for review","Publish date: from one month later","Standard review process","Standard display placement"]}},s=[{id:"free",title:"免费发布",description:a.FREE_LAUNCH.description,price:a.FREE_LAUNCH.displayPrice,features:a.FREE_LAUNCH.features},{id:"paid",title:"优先发布",description:a.PRIORITY_LAUNCH.description,price:a.PRIORITY_LAUNCH.displayPrice,features:a.PRIORITY_LAUNCH.features,recommended:!0}],l={FREE:{value:"free",label:"Free",color:"bg-green-100 text-green-800"},FREEMIUM:{value:"freemium",label:" Freemium",color:"bg-blue-100 text-blue-800"},PAID:{value:"paid",label:"Paid",color:"bg-orange-100 text-orange-800"}},i=[{value:"",label:"All Prices"},{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],n=[{value:l.FREE.value,label:l.FREE.label},{value:l.FREEMIUM.value,label:l.FREEMIUM.label},{value:l.PAID.value,label:l.PAID.label}],o=e=>{switch(e){case l.FREE.value:return l.FREE.color;case l.FREEMIUM.value:return l.FREEMIUM.color;case l.PAID.value:return l.PAID.color;default:return"bg-gray-100 text-gray-800"}},d=e=>{switch(e){case l.FREE.value:return l.FREE.label;case l.FREEMIUM.value:return l.FREEMIUM.label;case l.PAID.value:return l.PAID.label;default:return e}},c=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),m=(e,t)=>0===e?"zh"===t?"免费":"Free":"\xa5".concat(e),x=()=>a.PRIORITY_LAUNCH.promotion,u=()=>{let e=a.PRIORITY_LAUNCH.promotion;return e.enabled&&e.remainingSlots>0},g=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"cny";return new Intl.NumberFormat("zh-CN",{style:"currency",currency:t.toUpperCase(),minimumFractionDigits:2}).format(e/100)}},5734:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(5155),s=r(646),l=r(4416);function i(e){let{message:t,onClose:r,className:i=""}=e;return(0,a.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 ".concat(i),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(s.A,{className:"w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-green-800 text-sm",children:t})}),r&&(0,a.jsx)("button",{onClick:r,className:"ml-3 text-green-400 hover:text-green-600 transition-colors",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})})]})})}},6063:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var a=r(5155),s=r(2115),l=r(2108),i=r(2388),n=r(7652),o=r(3385),d=r(9911);function c(e){let{isOpen:t,onClose:r}=e,[c,m]=(0,s.useState)("method"),[x,u]=(0,s.useState)(""),[g,h]=(0,s.useState)(""),[p,b]=(0,s.useState)(!1),[f,w]=(0,s.useState)("");(0,i.a8)();let y=(0,n.c3)("auth");(0,o.Ym)();let j=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"success",r=document.createElement("div");r.className="fixed top-4 right-4 p-4 rounded-lg text-white z-50 ".concat("success"===t?"bg-green-500":"bg-red-500"),r.textContent=e,document.body.appendChild(r),setTimeout(()=>document.body.removeChild(r),3e3)},v=()=>{m("method"),u(""),h(""),w(""),r()},N=async e=>{try{b(!0),await (0,l.signIn)(e,{callbackUrl:"/"})}catch(e){j(y("login_failed"),"error")}finally{b(!1)}},_=async()=>{if(!x)return void w(y("email_required"));if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(x))return void w(y("email_invalid"));w(""),b(!0);try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:x})}),t=await e.json();t.success?(h(t.token),m("code"),j(y("verification_sent"))):j(t.error||y("send_failed"),"error")}catch(e){j(y("network_error"),"error")}finally{b(!1)}},k=async e=>{if(6===e.length){b(!0);try{let t=await (0,l.signIn)("email-code",{email:x,code:e,token:g,redirect:!1});(null==t?void 0:t.ok)?(j(y("login_success")),v()):j((null==t?void 0:t.error)||y("verification_error"),"error")}catch(e){j(y("network_error"),"error")}finally{b(!1)}}},E=(e,t)=>{if(t.length>1)return;let r=document.querySelectorAll(".code-input");if(r[e].value=t,t&&e<5){var a;null==(a=r[e+1])||a.focus()}let s=Array.from(r).map(e=>e.value).join("");6===s.length&&k(s)};return t?(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50",onClick:v}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 text-center flex-1",children:["method"===c&&y("login_title"),"email"===c&&y("email_login_title"),"code"===c&&y("verification_title")]}),(0,a.jsx)("button",{onClick:v,className:"text-gray-400 hover:text-gray-600 transition-colors",children:(0,a.jsx)(d.QCr,{})})]}),(0,a.jsxs)("div",{className:"p-6",children:["method"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:y("choose_method")}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-blue-300 rounded-lg text-blue-700 hover:bg-blue-50 transition-colors disabled:opacity-50",onClick:()=>N("google"),disabled:p,children:[(0,a.jsx)(d.DSS,{}),y("google_login")]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-white text-gray-500",children:y("or")})})]}),(0,a.jsxs)("button",{className:"w-full flex items-center justify-center gap-3 px-4 py-3 border border-black-300 rounded-lg text-black-700 hover:bg-black-50 transition-colors",onClick:()=>m("email"),children:[(0,a.jsx)(d.maD,{}),y("email_login")]})]}),"email"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:y("email_instruction")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("email_address")}),(0,a.jsx)("input",{type:"email",value:x,onChange:e=>u(e.target.value),placeholder:y("email_placeholder"),onKeyPress:e=>"Enter"===e.key&&_(),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),f&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:f})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50",onClick:_,disabled:p,children:p?y("sending"):y("send_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:y("back")})]})]}),"code"===c&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-600 text-center",children:y("verification_instruction",{email:x})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:y("verification_code")}),(0,a.jsx)("div",{className:"flex justify-center gap-2",children:[0,1,2,3,4,5].map(e=>(0,a.jsx)("input",{type:"text",maxLength:1,onChange:t=>E(e,t.target.value),disabled:p,className:"code-input w-12 h-12 text-center text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"},e))})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("button",{className:"w-full px-4 py-2 text-sm text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("email"),children:y("resend_code")}),(0,a.jsx)("button",{className:"w-full px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors",onClick:()=>m("method"),children:y("back")})]})]})]})]})]}):null}},8493:(e,t,r)=>{Promise.resolve().then(r.bind(r,6096)),Promise.resolve().then(r.bind(r,8988))},8988:(e,t,r)=>{"use strict";r.d(t,{default:()=>S});var a=r(5155),s=r(2115),l=r(2108),i=r(2388),n=r(7652),o=r(2731),d=r(5734),c=r(6063),m=r(3467),x=r(7550),u=r(9869),g=r(1284),h=r(8164),p=r(3311);let b=["ai_assistant","chatgpt","conversational_ai","smart_qa","language_model","writing_assistant","content_generation","copywriting","blog_writing","marketing_copy","image_generation","image_editing","ai_painting","avatar_generation","background_removal","video_generation","video_editing","video_clipping","short_video_creation","video_subtitles","speech_synthesis","speech_recognition","music_generation","speech_to_text","text_to_speech","code_generation","code_completion","code_review","development_assistant","low_code_platform","data_analysis","data_visualization","business_intelligence","machine_learning","deep_learning","office_automation","document_processing","project_management","team_collaboration","note_taking","ui_design","logo_design","web_design","graphic_design","prototype_design","seo_optimization","social_media_marketing","email_marketing","content_marketing","market_analysis","machine_translation","real_time_translation","document_translation","voice_translation"];var f=r(4416),w=r(7924),y=r(3332);function j(e){let{selectedTags:t,onTagsChange:r,maxTags:l=3,placeholder:o}=e,[d,c]=(0,s.useState)(""),[m,x]=(0,s.useState)(!1),u=(0,i.a8)(),g=(0,n.c3)("common"),h=function(){let e=(0,n.c3)("tags");return b.map(t=>({key:t,label:e(t)}))}();null==u||u.startsWith("/en");let p=e=>{t.includes(e)?r(t.filter(t=>t!==e)):t.length<l&&r([...t,e])},j=e=>{r(t.filter(t=>t!==e))},v=h.filter(e=>e.label.toLowerCase().includes(d.toLowerCase())&&!t.includes(e.key)),N=e=>{let t=h.find(t=>t.key===e);return t?t.label:e};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:g("select_tags")}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:g("selected_count",{count:t.length,max:l})})]}),t.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-700",children:g("selected_tags")}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(e=>(0,a.jsxs)("span",{className:"inline-flex items-center px-3 py-1 rounded-md text-sm font-medium bg-blue-100 text-blue-800",children:[N(e),(0,a.jsx)("button",{type:"button",onClick:()=>j(e),className:"ml-2 text-blue-600 hover:text-blue-800",children:(0,a.jsx)(f.A,{className:"h-3 w-3"})})]},e))})]}),(0,a.jsx)("div",{className:"space-y-3",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:g("select_tags_max",{max:l})}),(0,a.jsxs)("div",{className:"relative mb-3",children:[(0,a.jsx)("input",{type:"text",placeholder:o||g("search_tags"),value:d,onChange:e=>c(e.target.value),onFocus:()=>x(!0),className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),(0,a.jsx)(w.A,{className:"absolute left-3 top-2.5 h-4 w-4 text-gray-400"})]}),(m||d)&&(0,a.jsx)("div",{className:"relative",children:(0,a.jsx)("div",{className:"absolute z-10 w-full bg-white border border-gray-300 rounded-lg shadow-lg max-h-60 overflow-y-auto",children:v.length>0?(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 gap-1",children:v.map(e=>{let r=t.length>=l;return(0,a.jsx)("button",{type:"button",onClick:()=>{p(e.key),c(""),x(!1)},disabled:r,className:"\n                              w-full px-3 py-2 text-sm text-left rounded-md transition-colors\n                              ".concat(r?"bg-gray-100 text-gray-400 cursor-not-allowed":"hover:bg-blue-50 text-gray-700","\n                            "),children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(y.A,{className:"h-3 w-3 mr-2 text-gray-400"}),e.label]})},e.key)})}),v.length>50&&(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-2 px-3",children:g("found_tags",{count:v.length})})]}):(0,a.jsx)("div",{className:"p-4 text-center text-gray-500 text-sm",children:g(d?"no_tags_found":"start_typing")})})})]})}),(m||d)&&(0,a.jsx)("div",{className:"fixed inset-0 z-0",onClick:()=>{x(!1),c("")}}),t.length>=l&&(0,a.jsx)("p",{className:"text-sm text-amber-600",children:g("max_tags_limit",{max:l})})]})}var v=r(9651),N=r(9509);let _={TOOL_NAME:{min:2,max:40,label:"Tool Name"},TAGLINE:{min:4,max:80,label:"Tagline"},DESCRIPTION:{min:10,max:8e3,label:"Description"},WEBSITE_URL:{min:10,max:100,label:"Website URL"}};function k(e){let{current:t,max:r,min:s=0,className:l=""}=e,i=t>r,n=t<s,o=t/r*100,d="text-gray-500";return d=i?"text-red-500":n?"text-orange-500":o>80?"text-yellow-600":"text-green-600",(0,a.jsxs)("div",{className:"text-sm ".concat(d," ").concat(l),children:[(0,a.jsx)("span",{className:"font-medium",children:t}),(0,a.jsxs)("span",{className:"text-gray-400",children:["/",r]}),s>0&&t<s&&(0,a.jsxs)("span",{className:"ml-2 text-orange-500",children:["(At least ",s," characters)"]}),i&&(0,a.jsxs)("span",{className:"ml-2 text-red-500",children:["(Exceeded by ",t-r," characters)"]})]})}N.env.UPLOAD_BASE_DIR;var E=r(9137),A=r.n(E);let C=(0,r(5028).default)(()=>Promise.all([r.e(8827),r.e(6999)]).then(r.bind(r,9380)).then(e=>e.default),{loadableGenerated:{webpack:()=>[null]},ssr:!1});function F(e){let{value:t,onChange:r,placeholder:l,maxLength:i,error:o,className:d="",height:c=300}=e,m=(0,n.c3)("form"),[x,u]=(0,s.useState)(!1);(0,s.useEffect)(()=>{u(!0)},[]);let g=e=>{let t=e||"";i&&t.length>i||r(t)};return x?(0,a.jsxs)("div",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]])+" "+(d||""),children:[(0,a.jsx)("div",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]])+" markdown-editor-wrapper",children:(0,a.jsx)(C,{value:t,onChange:g,height:c,preview:"edit",hideToolbar:!1,visibleDragbar:!0,textareaProps:{placeholder:l||m("description_placeholder"),style:{fontSize:14,lineHeight:1.5,fontFamily:"inherit"},maxLength:i},"data-color-mode":"light"})}),i&&(0,a.jsxs)("div",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]])+" mt-1 flex justify-between items-center text-sm",children:[(0,a.jsxs)("div",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]])+" text-gray-500",children:["Supports Markdown: ",(0,a.jsx)("b",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]]),children:"**bold**"}),", ",(0,a.jsx)("b",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]]),children:"## headings"}),", ",(0,a.jsx)("b",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]]),children:"- lists"})]}),(0,a.jsxs)("div",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]])+" "+"".concat(t.length>.9*i?"text-orange-600":"text-gray-500"),children:[t.length,"/",i]})]}),o&&(0,a.jsx)("div",{className:A().dynamic([["ede86589fc8ae36d",[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""]]])+" mt-1 text-sm text-red-600",children:o}),(0,a.jsx)(A(),{id:"ede86589fc8ae36d",dynamic:[o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":""],children:".markdown-editor-wrapper .w-md-editor{background-color:transparent}.markdown-editor-wrapper .w-md-editor-text-container{-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem;border:1px solid#d1d5db}.markdown-editor-wrapper .w-md-editor-text-container:focus-within{border-color:#3b82f6;-webkit-box-shadow:0 0 0 2px rgba(59,130,246,.1);-moz-box-shadow:0 0 0 2px rgba(59,130,246,.1);box-shadow:0 0 0 2px rgba(59,130,246,.1)}.markdown-editor-wrapper .w-md-editor-text-input,.markdown-editor-wrapper .w-md-editor-text-textarea{font-size:14px!important;line-height:1.5!important;padding:12px!important}.markdown-editor-wrapper .w-md-editor-toolbar{border-bottom:1px solid#e5e7eb;padding:8px 12px;background-color:#f9fafb;-webkit-border-radius:.375rem .375rem 0 0;-moz-border-radius:.375rem .375rem 0 0;border-radius:.375rem .375rem 0 0}.markdown-editor-wrapper .w-md-editor-toolbar-divider{background-color:#e5e7eb}.markdown-editor-wrapper .w-md-editor-toolbar button{color:#6b7280;-webkit-border-radius:.25rem;-moz-border-radius:.25rem;border-radius:.25rem;padding:4px 6px}.markdown-editor-wrapper .w-md-editor-toolbar button:hover{background-color:#e5e7eb;color:#374151}.markdown-editor-wrapper .w-md-editor-toolbar button.active{background-color:#dbeafe;color:#1d4ed8}".concat(o?"\n        .markdown-editor-wrapper .w-md-editor-text-container {\n          border-color: #ef4444 !important;\n        }\n        ":"","      ")})]}):(0,a.jsxs)("div",{className:d,children:[(0,a.jsx)("textarea",{value:t,onChange:e=>g(e.target.value),placeholder:l,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(o?"border-red-300":"border-gray-300"),style:{height:"".concat(c,"px")},maxLength:i}),i&&(0,a.jsxs)("div",{className:"mt-1 text-right text-sm text-gray-500",children:[t.length,"/",i]}),o&&(0,a.jsx)("div",{className:"mt-1 text-sm text-red-600",children:o})]})}function S(e){let{categoryOptions:t,isEditMode:r=!1,toolId:b,initialTool:f}=e,w=(0,n.c3)("submit"),{data:y,status:N}=(0,l.useSession)(),E=(0,i.rd)(),[A,C]=(0,s.useState)(f||null),[S,I]=(0,s.useState)(r&&!f),[L,T]=(0,s.useState)({name:"",tagline:"",description:"",website:"",logoFile:null,category:"",tags:[],pricing:""}),[R,P]=(0,s.useState)(null),[U,O]=(0,s.useState)(""),[D,M]=(0,s.useState)(!1),[z,q]=(0,s.useState)(!1),[H,Y]=(0,s.useState)("idle"),[B,G]=(0,s.useState)(""),[W,J]=(0,s.useState)({}),[$,X]=(0,s.useState)(!1),[K,Q]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(!r||!b||f)return;let e=async()=>{try{let e=await fetch("/api/tools/".concat(b)),t=await e.json();if(t.success){let e=t.data;C(e),T({name:e.name||"",tagline:e.tagline||"",description:e.description||"",website:e.website||"",logoFile:null,category:e.category||"",tags:e.tags||[],pricing:e.pricing||""}),O(e.logo||""),P(e.logo||"")}else Y("error"),G(t.message||"获取工具信息失败")}catch(e){console.error("获取工具信息失败:",e),Y("error"),G("网络错误，请重试")}finally{I(!1)}};y?e():"loading"!==N&&I(!1)},[b,y,N,r,f]),(0,s.useEffect)(()=>{r&&f&&(C(f),T({name:f.name||"",tagline:f.tagline||"",description:f.description||"",website:f.website||"",logoFile:null,category:f.category||"",tags:f.tags||[],pricing:f.pricing||""}),O(f.logo||""),P(f.logo||""),I(!1))},[r,f]);let V=e=>{let{name:t,value:r}=e.target;T(e=>({...e,[t]:r}))},Z=e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){T(e=>({...e,logoFile:r}));let e=new FileReader;e.onload=e=>{var t;P(null==(t=e.target)?void 0:t.result)},e.readAsDataURL(r)}},ee=async()=>{if(!L.website.trim())return void J(e=>({...e,website:w("form.website_url_invalid")}));try{new URL(L.website)}catch(e){J(e=>({...e,website:w("form.website_url_invalid")}));return}Q(!0),J(e=>({...e,website:""}));try{let e=await fetch("/api/ai/generate-product-info",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({website:L.website})}),t=await e.json();t.success?(T(e=>({...e,name:t.data.name||e.name,tagline:t.data.tagline||e.tagline,description:t.data.description||e.description,category:t.data.category||e.category,tags:t.data.tags&&t.data.tags.length>0?t.data.tags:e.tags})),J(e=>{let t={...e};return delete t.name,delete t.tagline,delete t.description,delete t.category,delete t.tags,t})):J(e=>({...e,website:t.error||"AI generation failed, please try again"}))}catch(e){console.error("AI generation error:",e),J(e=>({...e,website:"Network error, please try again"}))}finally{Q(!1)}},et=()=>{let e={};return L.name.trim()||(e.name=w("form.tool_name")+" is required"),L.description.trim()||(e.description=w("form.description")+" is required"),L.website.trim()||(e.website=w("form.website_url")+" is required"),L.category||(e.category=w("form.category")+" is required"),L.pricing||(e.pricing=w("form.pricing_model")+" is required"),L.website&&!L.website.match(/^https?:\/\/.+/)&&(e.website=w("form.website_url_placeholder")),r||L.logoFile||(e.logo=w("form.logo_required")),0===L.tags.length&&(e.tags=w("form.tags_placeholder")),J(e),0===Object.keys(e).length},er=async e=>{var t;if(e.preventDefault(),!(null==y||null==(t=y.user)?void 0:t.email))return void X(!0);if(et()){q(!0),Y("idle");try{let e=U;if(L.logoFile){let t=new FormData;t.append("logo",L.logoFile);let r=await fetch("/api/upload/logo",{method:"POST",body:t});if(r.ok)e=(await r.json()).data.url;else{let e=await r.json();throw Error(e.message||"Logo upload failed")}}if(r&&b){let t={name:L.name,tagline:L.tagline,description:L.description,website:L.website,logo:e||void 0,category:L.category,tags:L.tags,pricing:L.pricing},r=await fetch("/api/tools/".concat(b),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),a=await r.json();a.success?(Y("success"),G("工具信息更新成功！"),setTimeout(()=>{E.push("/profile/submitted")},2e3)):(Y("error"),G(a.error||"Update failed, please retry"))}else{let t={name:L.name,tagline:L.tagline,description:L.description,website:L.website,logo:e,category:L.category,tags:L.tags,pricing:L.pricing},r=await fetch("/api/tools/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(r.ok){let e=await r.json();Y("success"),G(w("form.success_message")),setTimeout(()=>{E.push("/submit/tool-info-success?toolId=".concat(e.data.toolId))},500)}else{let e=await r.json();throw Error(e.message||"Submission failed")}}}catch(e){console.error("Submit error:",e),Y("error"),G(e.message+". "+w("form.error_message"))}finally{q(!1)}}};return"loading"===N||S?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsx)(o.A,{size:"lg"})}):y?r&&!A?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Tool not found"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"The tool you want to edit does not exist or has been deleted."}),(0,a.jsx)(i.N_,{href:"/profile/submitted",className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:"Back to Tools"})]})}):"success"===H?(0,a.jsx)("div",{className:"max-w-2xl mx-auto",children:(0,a.jsx)(d.A,{message:B||w("form.success_message")})}):(0,a.jsxs)(s.Fragment,{children:[(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[r&&(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)(i.N_,{href:"/profile/submitted",className:"inline-flex items-center text-blue-600 hover:text-blue-700 mb-4",children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back to Tools"]}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Tool"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Update your tool information to help more users understand your product."})]}),!r&&(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"flex justify-center mb-4",children:(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-full",children:(0,a.jsx)(u.A,{className:"h-8 w-8 text-blue-600"})})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:w("title")}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:w("subtitle")})]}),(0,a.jsxs)("form",{onSubmit:er,className:"max-w-4xl mx-auto space-y-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold text-gray-900 mb-6 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-5 w-5 mr-2 text-blue-600"}),w("form.basic_info")]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("label",{htmlFor:"website",className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.website_url")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})}),!r&&(0,a.jsx)("input",{type:"url",id:"website",name:"website",value:L.website,onChange:V,placeholder:r?"https://example.com":w("form.website_url_placeholder"),maxLength:_.WEBSITE_URL.max,className:"w-full pl-10 pr-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(W.website?"border-red-300":"border-gray-300"),required:!0}),r&&(0,a.jsx)("div",{className:"w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-600",children:L.website})]}),!r&&(0,a.jsx)("button",{type:"button",onClick:ee,disabled:K||!L.website.trim(),className:"px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-sm font-medium rounded-md hover:from-purple-700 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center gap-2 whitespace-nowrap",children:K?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(o.A,{size:"sm"}),"生成中..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4"}),"AI 表单信息生成"]})})]}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[!r&&(0,a.jsx)(k,{current:L.website.length,max:_.WEBSITE_URL.max,min:_.WEBSITE_URL.min}),W.website&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:W.website})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.tool_name")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",value:L.name,onChange:V,placeholder:w("form.tool_name_placeholder"),maxLength:_.TOOL_NAME.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(k,{current:L.name.length,max:_.TOOL_NAME.max,min:_.TOOL_NAME.min}),W.name&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:W.name})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"tagline",className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.tagline")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)("input",{type:"text",id:"tagline",name:"tagline",value:L.tagline,onChange:V,placeholder:w("form.tagline_placeholder"),maxLength:_.TAGLINE.max,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"}),(0,a.jsxs)("div",{className:"mt-1 flex justify-between items-center",children:[(0,a.jsx)(k,{current:L.tagline.length,max:_.TAGLINE.max,min:_.TAGLINE.min}),W.tagline&&(0,a.jsx)("span",{className:"text-red-500 text-sm",children:W.tagline})]})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.description")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(F,{value:L.description,onChange:e=>T(t=>({...t,description:e})),placeholder:w("form.description_placeholder"),maxLength:_.DESCRIPTION.max,error:W.description,height:200})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.logo_upload")," ",!r&&(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-6",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors",children:[(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;(null==(t=e.target.files)?void 0:t[0])&&Z(e)},className:"hidden",id:"logo-upload",required:!r}),(0,a.jsxs)("label",{htmlFor:"logo-upload",className:"cursor-pointer",children:[(0,a.jsx)(u.A,{className:"h-8 w-8 text-gray-400 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:D?w("form.uploading"):w("form.click_to_upload")}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:w("form.logo_upload_hint")})]})]}),W.logo&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:W.logo})]}),R&&(0,a.jsx)(v.Ay,{alt:"app logo",src:R,width:v.iu.toolLogo.width,height:v.iu.toolLogo.height,className:"rounded-lg object-cover",sizes:v.ng.toolLogo,placeholder:"blur"})]})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:w("form.category_and_pricing")}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.category")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("select",{id:"category",name:"category",value:L.category,onChange:V,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:w("form.category_placeholder")}),t.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{htmlFor:"pricing",className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.pricing_model")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsxs)("select",{id:"pricing",name:"pricing",value:L.pricing,onChange:V,className:"w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(W.pricing?"border-red-300":"border-gray-300"),required:!0,children:[(0,a.jsx)("option",{value:"",children:w("form.pricing_placeholder")}),m.Y$.map(e=>(0,a.jsx)("option",{value:e.value,children:w("form.".concat(e.value))},e.value))]}),W.pricing&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:W.pricing})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[w("form.tags")," ",(0,a.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,a.jsx)(j,{selectedTags:L.tags,onTagsChange:e=>{T(t=>({...t,tags:e}))},maxTags:3,placeholder:w("form.tags_placeholder")}),W.tags&&(0,a.jsx)("p",{className:"text-red-600 text-sm mt-1",children:W.tags})]})]}),!r&&(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:w("form.guidelines_title")}),(0,a.jsxs)("ul",{className:"space-y-2 text-blue-800",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),w("form.guideline_1")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),w("form.guideline_2")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),w("form.guideline_3")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),w("form.guideline_4")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"flex-shrink-0 w-2 h-2 bg-blue-600 rounded-full mt-2 mr-3"}),w("form.guideline_5")]})]})]}),(0,a.jsx)("div",{className:r?"flex justify-end":"flex justify-center",children:(0,a.jsx)("button",{type:"submit",disabled:z,className:"inline-flex items-center border border-transparent font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors ".concat("px-8 py-3 text-base"),children:z?(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)(o.A,{size:"sm",className:"mr-2"}),w("form.submitting")]}):(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)(u.A,{className:"h-5 w-5 mr-2"}),w("form.submit_button")]})})})]}),"error"===H&&(0,a.jsx)("div",{className:"mt-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-800",children:B})})]}),(0,a.jsx)(c.A,{isOpen:$,onClose:()=>X(!1)})]}):(0,a.jsxs)(s.Fragment,{children:[(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-4",children:w("auth.login_required")}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:w("auth.login_to_submit")}),(0,a.jsx)("button",{onClick:()=>X(!0),className:"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700",children:w("auth.login")})]})}),(0,a.jsx)(c.A,{isOpen:$,onClose:()=>X(!1)})]})}},9651:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>s,iu:()=>l,ng:()=>i});var a=r(5155);function s(e){let{src:t,alt:r,width:s,height:l,className:i="",priority:n=!1,fill:o=!1,sizes:d,placeholder:c="empty",blurDataURL:m,fallbackSrc:x="/images/placeholder.svg"}=e;return o?(0,a.jsx)("div",{className:"relative overflow-hidden",children:(0,a.jsx)("img",{src:t,alt:r,className:i,style:{objectFit:"contain",padding:2,width:"100%",height:"100%"}})}):(0,a.jsx)("img",{src:t,alt:r,width:s,height:l,className:i,style:{objectFit:"contain",padding:2}})}r(2115);let l={avatar:{width:40,height:40},avatarLarge:{width:80,height:80},toolLogo:{width:52,height:52},toolLogoLarge:{width:84,height:84},thumbnail:{width:200,height:150},card:{width:300,height:200},hero:{width:1200,height:600}},i={avatar:"40px",toolLogo:"52px",toolLogoLarge:"84px",thumbnail:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",card:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",hero:"100vw",full:"100vw"}}}]);