(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3919],{6672:(e,s,l)=>{Promise.resolve().then(l.bind(l,9453))},9453:(e,s,l)=>{"use strict";l.r(s),l.d(s,{default:()=>d});var t=l(5155),a=l(2108),n=l(2115);function d(){var e,s,l,d;let{data:i,status:r}=(0,a.useSession)(),[c,o]=(0,n.useState)(""),[m,u]=(0,n.useState)(""),[h,x]=(0,n.useState)(""),[b,p]=(0,n.useState)(""),j=async()=>{try{let e=await fetch("/api/auth/send-code",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:c})}),s=await e.json();s.success?(x(s.token),p("验证码已发送！Token: "+s.token)):p("发送失败: "+s.error)}catch(e){p("网络错误")}},g=async()=>{try{let e=await (0,a.signIn)("email-code",{email:c,code:m,token:h,redirect:!1});(null==e?void 0:e.ok)?p("登录成功！"):p("登录失败: "+(null==e?void 0:e.error))}catch(e){p("验证失败")}};return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 py-12 px-4",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"认证测试页面"}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"当前状态"}),(0,t.jsxs)("p",{children:["Status: ",r]}),i?(0,t.jsxs)("div",{children:[(0,t.jsxs)("p",{children:["已登录用户: ",null==(e=i.user)?void 0:e.email]}),(0,t.jsxs)("p",{children:["用户名: ",null==(s=i.user)?void 0:s.name]}),(0,t.jsxs)("p",{children:["用户ID: ",null==(l=i.user)?void 0:l.id]}),(0,t.jsxs)("p",{children:["用户角色: ",null==(d=i.user)?void 0:d.role]}),(0,t.jsx)("button",{onClick:()=>(0,a.signOut)(),className:"mt-2 bg-red-500 text-white px-4 py-2 rounded",children:"退出登录"})]}):(0,t.jsx)("p",{children:"未登录"})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"邮件验证码登录测试"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-1",children:"邮箱"}),(0,t.jsx)("input",{type:"email",value:c,onChange:e=>o(e.target.value),className:"w-full border rounded px-3 py-2",placeholder:"输入邮箱"})]}),(0,t.jsx)("button",{onClick:j,className:"w-full bg-blue-500 text-white py-2 rounded mb-4",children:"发送验证码"}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-1",children:"验证码"}),(0,t.jsx)("input",{type:"text",value:m,onChange:e=>u(e.target.value),className:"w-full border rounded px-3 py-2",placeholder:"输入6位验证码"})]}),(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("label",{className:"block text-sm font-medium mb-1",children:"Token"}),(0,t.jsx)("input",{type:"text",value:h,onChange:e=>x(e.target.value),className:"w-full border rounded px-3 py-2",placeholder:"自动填充"})]}),(0,t.jsx)("button",{onClick:g,className:"w-full bg-green-500 text-white py-2 rounded mb-4",children:"验证登录"}),b&&(0,t.jsx)("div",{className:"p-3 bg-gray-100 rounded text-sm",children:b})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"OAuth登录测试"}),(0,t.jsx)("button",{onClick:()=>(0,a.signIn)("google"),className:"w-full bg-red-500 text-white py-2 rounded mb-2",children:"Google登录"}),(0,t.jsx)("button",{onClick:()=>(0,a.signIn)("github"),className:"w-full bg-gray-800 text-white py-2 rounded",children:"GitHub登录"})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2108,8441,1684,7358],()=>s(6672)),_N_E=e.O()}]);