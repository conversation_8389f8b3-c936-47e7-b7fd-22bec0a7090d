(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9099],{4934:(e,s,t)=>{Promise.resolve().then(t.bind(t,7265))},7265:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var l=t(5155),d=t(2115),a=t(6707),r=t(7652);function i(){let[e,s]=(0,d.useState)("free"),[t,i]=(0,d.useState)(""),[n,c]=(0,d.useState)(!1),[x,m]=(0,d.useState)(""),[o,g]=(0,d.useState)(!1),[h,b]=(0,d.useState)(!1);(0,r.c3)("launch");let u=async(e,t)=>{c(!0),m(""),await new Promise(e=>setTimeout(e,2e3)),"paid"===e?alert("模拟支付流程：选择了付费选项，发布日期：".concat(t)):alert("模拟免费提交：发布日期：".concat(t)),s(e),i(t),c(!1)};return(0,l.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Launch Date Selector 演示"}),(0,l.jsx)("p",{className:"text-lg text-gray-600",children:"展示带有Badge验证功能的发布日期选择器"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"演示控制"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"付费状态"}),(0,l.jsx)("button",{onClick:()=>g(!o),className:"w-full px-4 py-2 rounded-lg font-medium transition-colors ".concat(o?"bg-green-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:o?"已付费":"未付费"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Badge验证状态"}),(0,l.jsx)("button",{onClick:()=>b(!h),className:"w-full px-4 py-2 rounded-lg font-medium transition-colors ".concat(h?"bg-blue-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:h?"Badge已验证":"Badge未验证"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"重置状态"}),(0,l.jsx)("button",{onClick:()=>{g(!1),b(!1),m("")},className:"w-full px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors",children:"重置"})]})]})]}),(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:(0,l.jsx)(a.A,{toolId:"demo-tool-id",toolWebsite:"https://example.com",currentOption:e,currentDate:t,isEditing:!1,onSubmit:u,isSubmitting:n,error:x,hasPaidOrder:o,badgeVerified:h})}),(0,l.jsxs)("div",{className:"bg-blue-50 rounded-lg border border-blue-200 p-6 mt-8",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:"当前状态"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium text-blue-800",children:"选择的选项："}),(0,l.jsx)("span",{className:"text-blue-600 ml-2",children:e||"未选择"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium text-blue-800",children:"选择的日期："}),(0,l.jsx)("span",{className:"text-blue-600 ml-2",children:t||"未选择"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium text-blue-800",children:"付费状态："}),(0,l.jsx)("span",{className:"text-blue-600 ml-2",children:o?"已付费":"未付费"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium text-blue-800",children:"Badge状态："}),(0,l.jsx)("span",{className:"text-blue-600 ml-2",children:h?"已验证":"未验证"})]})]})]}),(0,l.jsxs)("div",{className:"bg-gray-50 rounded-lg border border-gray-200 p-6 mt-8",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"功能说明"}),(0,l.jsxs)("div",{className:"space-y-3 text-sm text-gray-700",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"免费发布 + Badge未验证："})," 只能选择一个月后的日期"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"免费发布 + Badge已验证："})," 可以选择明天开始的任意日期，显示badge验证成功提示"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"付费发布："})," 可以选择明天开始的任意日期"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"已付费："})," 显示优先服务激活提示，可以选择任意日期"]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("strong",{children:"Badge验证流程："})," 在免费选项下会显示badge获取选项，用户可以通过验证badge来解锁立即发布权限"]})]})]})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3385,6707,8441,1684,7358],()=>s(4934)),_N_E=e.O()}]);