(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9922],{3868:(s,e,i)=>{"use strict";i.r(e),i.d(e,{default:()=>a});var r=i(5155);i(2115);var d=i(3402),l=i(3467);function a(){let s=async(s,e)=>{console.log("Test submission:",{option:s,date:e}),await new Promise(s=>setTimeout(s,1e3))};return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto py-8 px-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"限时优惠测试页面"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"当前优惠配置"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"优惠状态"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,r.jsxs)("p",{children:["优惠启用: ",(0,l.mp)()?"是":"否"]}),(0,r.jsxs)("p",{children:["优惠描述: ",(0,l.sT)().description]}),(0,r.jsxs)("p",{children:["折扣百分比: ",(0,l.sT)().discountPercent,"%"]}),(0,r.jsxs)("p",{children:["剩余名额: ",(0,l.sT)().remainingSlots]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-medium text-gray-900 mb-2",children:"价格对比"}),(0,r.jsxs)("div",{className:"space-y-1 text-sm",children:[(0,r.jsxs)("p",{children:["原价: ",(0,l.S9)(l.kX.PRIORITY_LAUNCH.originalPrice)]}),(0,r.jsxs)("p",{children:["现价: ",(0,l.$g)(l.kX.PRIORITY_LAUNCH.displayPrice)]}),(0,r.jsxs)("p",{children:["节省: \xa5",l.kX.PRIORITY_LAUNCH.originalPrice-l.kX.PRIORITY_LAUNCH.displayPrice]})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold mb-6",children:"发布日期选择器（含限时优惠）"}),(0,r.jsx)(d.A,{currentOption:"free",isEditing:!1,onSubmit:s,isSubmitting:!1,hasPaidOrder:!1})]})]})}},5357:(s,e,i)=>{Promise.resolve().then(i.bind(i,3868))}},s=>{var e=e=>s(s.s=e);s.O(0,[3385,3402,8441,1684,7358],()=>e(5357)),_N_E=s.O()}]);