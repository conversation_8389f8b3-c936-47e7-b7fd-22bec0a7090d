(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4926],{646:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},981:(e,t,s)=>{"use strict";s.d(t,{A:()=>h});var r=s(5695),a=s(2115),l=s.t(a,2),n=s(3385),c=l["use".trim()],o=s(3225),i=s(6160),d=s(469),m=s(5155),x=s(8986);function h(e){let{Link:t,config:s,getPathname:l,...h}=function(e,t){var s,l,n;let x={...s=t||{},localePrefix:"object"==typeof(n=s.localePrefix)?n:{mode:n||"always"},localeCookie:!!((l=s.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof l&&l},localeDetection:s.localeDetection??!0,alternateLinks:s.alternateLinks??!0},h=x.pathnames,u=(0,a.forwardRef)(function({href:t,locale:s,...r},a){let l,n;"object"==typeof t?(l=t.pathname,n=t.params):l=t;let d=(0,o._x)(t),u=e(),p=(0,o.yL)(u)?c(u):u,f=d?g({locale:s||p,href:null==h?l:{pathname:l,params:n},forcePrefix:null!=s||void 0}):l;return(0,m.jsx)(i.default,{ref:a,href:"object"==typeof t?{...t,pathname:f}:f,locale:s,localeCookie:x.localeCookie,...r})});function g(e){let t,{forcePrefix:s,href:r,locale:a}=e;return null==h?"object"==typeof r?(t=r.pathname,r.query&&(t+=(0,d.Zn)(r.query))):t=r:t=(0,d.FP)({locale:a,...(0,d.TK)(r),pathnames:x.pathnames}),(0,d.x3)(t,a,x,s)}function p(e){return function(t,...s){return e(g(t),...s)}}return{config:x,Link:u,redirect:p(r.redirect),permanentRedirect:p(r.permanentRedirect),getPathname:g}}(n.Ym,e);return{...h,Link:t,usePathname:function(){let e=function(e){let t=(0,r.usePathname)(),s=(0,n.Ym)();return(0,a.useMemo)(()=>{if(!t)return t;let r=t,a=(0,o.XP)(s,e.localePrefix);if((0,o.wO)(a,t))r=(0,o.MY)(t,a);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,o.bL)(s);(0,o.wO)(e,t)&&(r=(0,o.MY)(t,e))}return r},[e.localePrefix,s,t])}(s),t=(0,n.Ym)();return(0,a.useMemo)(()=>e&&s.pathnames?(0,d.aM)(t,e,s.pathnames):e,[t,e])},useRouter:function(){let e=(0,r.useRouter)(),t=(0,n.Ym)(),c=(0,r.usePathname)();return(0,a.useMemo)(()=>{function r(e){return function(r,a){let{locale:n,...o}=a||{},i=[l({href:r,locale:n||t})];Object.keys(o).length>0&&i.push(o),e(...i),(0,x.A)(s.localeCookie,c,t,n)}}return{...e,push:r(e.push),replace:r(e.replace),prefetch:r(e.prefetch)}},[t,c,e])},getPathname:l}}},1441:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(5155),a=s(2115),l=s(2388),n=s(7652),c=s(3385),o=s(2731),i=s(9783),d=s(2839),m=s(4186),x=s(646),h=s(4861),u=s(9946);let g=(0,u.A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);var p=s(2713);let f=(0,u.A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]]);var y=s(5339),b=s(2657);function j(){var e,t,s,u,j,v,N,w;(0,l.a8)();let _=(0,n.c3)("admin"),k=(0,c.Ym)(),[A,P]=(0,a.useState)("7d"),[L,T]=(0,a.useState)(null),[S,C]=(0,a.useState)(!0),[E,q]=(0,a.useState)("");(0,a.useEffect)(()=>{O()},[A]);let O=async()=>{try{C(!0),q("");let e=await d.u.getAdminStats(A);e.success&&e.data?T(e.data):q(e.error||_("errors.fetch_failed"))}catch(e){q(_("errors.network_error"))}finally{C(!1)}};return S?(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,r.jsx)(o.A,{size:"lg",className:"py-20"})}):(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[E&&(0,r.jsx)(i.default,{message:E,onClose:()=>q(""),className:"mb-6"}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 flex items-center",children:[(0,r.jsx)(p.A,{className:"mr-3 h-8 w-8 text-blue-600"}),_("dashboard.title")]}),(0,r.jsx)("p",{className:"mt-2 text-lg text-gray-600",children:_("dashboard.subtitle")})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("select",{value:A,onChange:e=>P(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,r.jsx)("option",{value:"1d",children:_("dashboard.time_range.today")}),(0,r.jsx)("option",{value:"7d",children:_("dashboard.time_range.7days")}),(0,r.jsx)("option",{value:"30d",children:_("dashboard.time_range.30days")}),(0,r.jsx)("option",{value:"90d",children:_("dashboard.time_range.90days")})]})})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:_("dashboard.stats.total_tools")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(null==L||null==(e=L.overview)?void 0:e.totalTools)||0})]}),(0,r.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,r.jsx)(p.A,{className:"w-6 h-6 text-blue-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(f,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+12%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:_("dashboard.stats.vs_last_week")})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:_("dashboard.stats.pending_review")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-yellow-600",children:(null==L||null==(t=L.overview)?void 0:t.pendingTools)||0})]}),(0,r.jsx)("div",{className:"p-3 bg-yellow-100 rounded-lg",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-yellow-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 text-yellow-500 mr-1"}),(0,r.jsx)("span",{className:"text-yellow-600",children:_("dashboard.stats.needs_attention")})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:_("dashboard.stats.approved_today")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-green-600",children:((null==L||null==(s=L.overview)?void 0:s.approvedTools)||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-green-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(f,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+8%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:_("dashboard.stats.vs_last_week")})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:_("dashboard.stats.rejected_today")}),(0,r.jsx)("p",{className:"text-2xl font-bold text-red-600",children:((null==L||null==(u=L.overview)?void 0:u.rejectedTools)||0).toLocaleString()})]}),(0,r.jsx)("div",{className:"p-3 bg-red-100 rounded-lg",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-red-600"})})]}),(0,r.jsxs)("div",{className:"mt-4 flex items-center text-sm",children:[(0,r.jsx)(f,{className:"w-4 h-4 text-green-500 mr-1"}),(0,r.jsx)("span",{className:"text-green-600",children:"+15%"}),(0,r.jsx)("span",{className:"text-gray-500 ml-1",children:_("dashboard.stats.vs_last_week")})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8",children:[(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:_("dashboard.overview.title")}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-blue-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(p.A,{className:"w-5 h-5 text-blue-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:_("dashboard.stats.total_tools")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-blue-600",children:(null==L||null==(j=L.overview)?void 0:j.totalTools)||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-yellow-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-yellow-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:_("dashboard.stats.pending_review")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-yellow-600",children:(null==L||null==(v=L.overview)?void 0:v.pendingTools)||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-green-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-green-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:_("dashboard.stats.approved_today")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-green-600",children:(null==L||null==(N=L.overview)?void 0:N.approvedTools)||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-red-50 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 text-red-600"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-700",children:_("dashboard.stats.rejected_today")})]}),(0,r.jsx)("span",{className:"text-lg font-bold text-red-600",children:(null==L||null==(w=L.overview)?void 0:w.rejectedTools)||0})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:_("dashboard.quick_actions.title")}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("button",{onClick:()=>window.location.href="/admin",className:"w-full flex items-center justify-center space-x-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[(0,r.jsx)(b.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:_("dashboard.quick_actions.view_pending")})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(g,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:_("dashboard.quick_actions.view_history")})]}),(0,r.jsxs)("button",{className:"w-full flex items-center justify-center space-x-2 p-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:_("dashboard.quick_actions.export_report")})]}),(0,r.jsxs)("button",{onClick:()=>O(),className:"w-full flex items-center justify-center space-x-2 p-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors",children:[(0,r.jsx)(f,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:_("dashboard.quick_actions.refresh_data")})]})]})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:_("dashboard.system_info.title")}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:L?_("dashboard.system_info.status_online"):_("dashboard.system_info.status_offline")}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:_("dashboard.system_info.system_status")})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:A}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:_("dashboard.system_info.stats_period")})]}),(0,r.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:new Date().toLocaleDateString("zh"===k?"zh-CN":"en-US")}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:_("dashboard.system_info.last_updated")})]})]})]})]})}},2388:(e,t,s)=>{"use strict";s.d(t,{N_:()=>n,a8:()=>o,rd:()=>i});var r=s(9984),a=s(981);let l=(0,r.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:n,redirect:c,usePathname:o,useRouter:i}=(0,a.A)(l)},2657:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2713:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2731:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});var r=s(5155);function a(e){let{size:t="md",className:s=""}=e;return(0,r.jsx)("div",{className:"flex justify-center items-center ".concat(s),children:(0,r.jsx)("div",{className:"".concat({sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"}[t]," border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin")})})}},2839:(e,t,s)=>{"use strict";s.d(t,{u:()=>d});var r=s(9509);function a(){if(r.env.NEXT_PUBLIC_APP_URL)return r.env.NEXT_PUBLIC_APP_URL;{let{protocol:e,hostname:t,port:s}=window.location;return"".concat(e,"//").concat(t).concat(s?":".concat(s):"")}}function l(){if(r.env.NEXT_PUBLIC_API_BASE_URL)return r.env.NEXT_PUBLIC_API_BASE_URL;let e=a();return"".concat(e,"/api")}function n(){return"production"}function c(){return"development"===n()}a(),l(),r.env.NEXTAUTH_URL?r.env.NEXTAUTH_URL:a(),n(),c(),n(),window.location.port||window.location.protocol,c();let o=l();class i{async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let s="".concat(this.baseURL).concat(e),r={headers:{"Content-Type":"application/json",...t.headers},...t};console.log("API request:",{url:s,config:r});let a=await fetch(s,r),l=await a.json();if(!a.ok)throw Error(l.error||"HTTP error! status: ".concat(a.status));return l}catch(e){return console.error("API request failed:",e),{success:!1,error:e instanceof Error?e.message:"请求失败"}}}async getTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/tools".concat(s?"?".concat(s):""))}async getTool(e){return this.request("/tools/".concat(e))}async createTool(e){return this.request("/tools",{method:"POST",body:JSON.stringify(e)})}async updateTool(e,t){return this.request("/tools/".concat(e),{method:"PUT",body:JSON.stringify(t)})}async deleteTool(e){return this.request("/tools/".concat(e),{method:"DELETE"})}async getLikedTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/user/liked-tools".concat(s?"?".concat(s):""))}async getAdminTools(e){let t=new URLSearchParams;e&&Object.entries(e).forEach(e=>{let[s,r]=e;void 0!==r&&t.append(s,r.toString())});let s=t.toString();return this.request("/admin/tools".concat(s?"?".concat(s):""))}async approveTool(e,t){return this.request("/admin/tools/".concat(e,"/approve"),{method:"POST",body:JSON.stringify(t)})}async rejectTool(e,t){return this.request("/admin/tools/".concat(e,"/reject"),{method:"POST",body:JSON.stringify(t)})}async getAdminStats(e){return this.request("/admin/stats".concat(e?"?timeRange=".concat(e):""))}async getCategories(){return this.request("/categories")}async getOrder(e){return this.request("/orders/".concat(e))}async processOrderPayment(e,t){return this.request("/orders/".concat(e,"/pay"),{method:"POST",body:JSON.stringify(t)})}async getCurrentUser(){return this.request("/auth/me")}async updateProfile(e){return this.request("/auth/me",{method:"PUT",body:JSON.stringify(e)})}constructor(e=o){this.baseURL=e}}let d=new i},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4416:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4861:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},5339:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6149:(e,t,s)=>{Promise.resolve().then(s.bind(s,1441))},7652:(e,t,s)=>{"use strict";s.d(t,{c3:()=>l});var r=s(3385);function a(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let l=a(0,r.c3);a(0,r.kc)},9783:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(5155),a=s(5339),l=s(4416);function n(e){let{message:t,onClose:s,className:n=""}=e;return(0,r.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(n),children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)(a.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("p",{className:"text-red-800 text-sm",children:t})}),s&&(0,r.jsx)("button",{onClick:s,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,r.jsx)(l.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,s)=>{"use strict";s.d(t,{A:()=>m});var r=s(2115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,s)=>s?s.toUpperCase():t.toLowerCase()),n=e=>{let t=l(e);return t.charAt(0).toUpperCase()+t.slice(1)},c=function(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return t.filter((e,t,s)=>!!e&&""!==e.trim()&&s.indexOf(e)===t).join(" ").trim()},o=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let d=(0,r.forwardRef)((e,t)=>{let{color:s="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:d="",children:m,iconNode:x,...h}=e;return(0,r.createElement)("svg",{ref:t,...i,width:a,height:a,stroke:s,strokeWidth:n?24*Number(l)/Number(a):l,className:c("lucide",d),...!m&&!o(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])}),m=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{className:o,...i}=s;return(0,r.createElement)(d,{ref:l,iconNode:t,className:c("lucide-".concat(a(n(e))),"lucide-".concat(e),o),...i})});return s.displayName=n(e),s}},9984:(e,t,s)=>{"use strict";function r(e){return e}s.d(t,{A:()=>r})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,8441,1684,7358],()=>t(6149)),_N_E=e.O()}]);