(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5534],{981:(e,t,r)=>{"use strict";r.d(t,{A:()=>h});var a=r(5695),l=r(2115),n=r.t(l,2),o=r(3385),s=n["use".trim()],c=r(3225),i=r(6160),u=r(469),d=r(5155),m=r(8986);function h(e){let{Link:t,config:r,getPathname:n,...h}=function(e,t){var r,n,o;let m={...r=t||{},localePrefix:"object"==typeof(o=r.localePrefix)?o:{mode:o||"always"},localeCookie:!!((n=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof n&&n},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},h=m.pathnames,f=(0,l.forwardRef)(function({href:t,locale:r,...a},l){let n,o;"object"==typeof t?(n=t.pathname,o=t.params):n=t;let u=(0,c._x)(t),f=e(),p=(0,c.yL)(f)?s(f):f,k=u?x({locale:r||p,href:null==h?n:{pathname:n,params:o},forcePrefix:null!=r||void 0}):n;return(0,d.jsx)(i.default,{ref:l,href:"object"==typeof t?{...t,pathname:k}:k,locale:r,localeCookie:m.localeCookie,...a})});function x(e){let t,{forcePrefix:r,href:a,locale:l}=e;return null==h?"object"==typeof a?(t=a.pathname,a.query&&(t+=(0,u.Zn)(a.query))):t=a:t=(0,u.FP)({locale:l,...(0,u.TK)(a),pathnames:m.pathnames}),(0,u.x3)(t,l,m,r)}function p(e){return function(t,...r){return e(x(t),...r)}}return{config:m,Link:f,redirect:p(a.redirect),permanentRedirect:p(a.permanentRedirect),getPathname:x}}(o.Ym,e);return{...h,Link:t,usePathname:function(){let e=function(e){let t=(0,a.usePathname)(),r=(0,o.Ym)();return(0,l.useMemo)(()=>{if(!t)return t;let a=t,l=(0,c.XP)(r,e.localePrefix);if((0,c.wO)(l,t))a=(0,c.MY)(t,l);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,c.bL)(r);(0,c.wO)(e,t)&&(a=(0,c.MY)(t,e))}return a},[e.localePrefix,r,t])}(r),t=(0,o.Ym)();return(0,l.useMemo)(()=>e&&r.pathnames?(0,u.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,a.useRouter)(),t=(0,o.Ym)(),s=(0,a.usePathname)();return(0,l.useMemo)(()=>{function a(e){return function(a,l){let{locale:o,...c}=l||{},i=[n({href:a,locale:o||t})];Object.keys(c).length>0&&i.push(c),e(...i),(0,m.A)(r.localeCookie,s,t,o)}}return{...e,push:a(e.push),replace:a(e.replace),prefetch:a(e.prefetch)}},[t,s,e])},getPathname:n}}},2100:(e,t,r)=>{Promise.resolve().then(r.bind(r,6160)),Promise.resolve().then(r.bind(r,6096)),Promise.resolve().then(r.bind(r,4354)),Promise.resolve().then(r.bind(r,9783))},2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>o,a8:()=>c,rd:()=>i});var a=r(9984),l=r(981);let n=(0,a.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:o,redirect:s,usePathname:c,useRouter:i}=(0,l.A)(n)},4354:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155);r(2115);var l=r(2388),n=r(7652);let o=e=>{let{category:t}=e,r=(0,n.c3)("common");return(0,a.jsx)(l.N_,{href:"/categories/".concat(t.slug),children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",style:{height:"100%"},children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:t.color||"#3B82F6"},children:(0,a.jsx)("span",{className:"text-white",children:t.icon||"\uD83D\uDD27"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:r("tools_count",{count:t.toolCount})})]})]}),(0,a.jsx)("p",{className:"text-gray-600 text-sm",children:t.description})]})})})}},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5339:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(9946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(3385),l=r(5155);function n(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,l.jsx)(a.Dk,{locale:t,...r})}},7652:(e,t,r)=>{"use strict";r.d(t,{c3:()=>n});var a=r(3385);function l(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let n=l(0,a.c3);l(0,a.kc)},9783:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var a=r(5155),l=r(5339),n=r(4416);function o(e){let{message:t,onClose:r,className:o=""}=e;return(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 ".concat(o),children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)(l.A,{className:"w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0"}),(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("p",{className:"text-red-800 text-sm",children:t})}),r&&(0,a.jsx)("button",{onClick:r,className:"ml-3 text-red-400 hover:text-red-600 transition-colors",children:(0,a.jsx)(n.A,{className:"w-4 h-4"})})]})})}},9946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(2115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase()),o=e=>{let t=n(e);return t.charAt(0).toUpperCase()+t.slice(1)},s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()},c=e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:n=2,absoluteStrokeWidth:o,className:u="",children:d,iconNode:m,...h}=e;return(0,a.createElement)("svg",{ref:t,...i,width:l,height:l,stroke:r,strokeWidth:o?24*Number(n)/Number(l):n,className:s("lucide",u),...!d&&!c(h)&&{"aria-hidden":"true"},...h},[...m.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,n)=>{let{className:c,...i}=r;return(0,a.createElement)(u,{ref:n,iconNode:t,className:s("lucide-".concat(l(o(e))),"lucide-".concat(e),c),...i})});return r.displayName=o(e),r}},9984:(e,t,r)=>{"use strict";function a(e){return e}r.d(t,{A:()=>a})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,8441,1684,7358],()=>t(2100)),_N_E=e.O()}]);