(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[245],{981:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var n=a(5695),r=a(2115),o=a.t(r,2),l=a(3385),i=o["use".trim()],c=a(3225),s=a(6160),u=a(469),f=a(5155),h=a(8986);function m(e){let{Link:t,config:a,getPathname:o,...m}=function(e,t){var a,o,l;let h={...a=t||{},localePrefix:"object"==typeof(l=a.localePrefix)?l:{mode:l||"always"},localeCookie:!!((o=a.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof o&&o},localeDetection:a.localeDetection??!0,alternateLinks:a.alternateLinks??!0},m=h.pathnames,d=(0,r.forwardRef)(function({href:t,locale:a,...n},r){let o,l;"object"==typeof t?(o=t.pathname,l=t.params):o=t;let u=(0,c._x)(t),d=e(),P=(0,c.yL)(d)?i(d):d,b=u?p({locale:a||P,href:null==m?o:{pathname:o,params:l},forcePrefix:null!=a||void 0}):o;return(0,f.jsx)(s.default,{ref:r,href:"object"==typeof t?{...t,pathname:b}:b,locale:a,localeCookie:h.localeCookie,...n})});function p(e){let t,{forcePrefix:a,href:n,locale:r}=e;return null==m?"object"==typeof n?(t=n.pathname,n.query&&(t+=(0,u.Zn)(n.query))):t=n:t=(0,u.FP)({locale:r,...(0,u.TK)(n),pathnames:h.pathnames}),(0,u.x3)(t,r,h,a)}function P(e){return function(t,...a){return e(p(t),...a)}}return{config:h,Link:d,redirect:P(n.redirect),permanentRedirect:P(n.permanentRedirect),getPathname:p}}(l.Ym,e);return{...m,Link:t,usePathname:function(){let e=function(e){let t=(0,n.usePathname)(),a=(0,l.Ym)();return(0,r.useMemo)(()=>{if(!t)return t;let n=t,r=(0,c.XP)(a,e.localePrefix);if((0,c.wO)(r,t))n=(0,c.MY)(t,r);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,c.bL)(a);(0,c.wO)(e,t)&&(n=(0,c.MY)(t,e))}return n},[e.localePrefix,a,t])}(a),t=(0,l.Ym)();return(0,r.useMemo)(()=>e&&a.pathnames?(0,u.aM)(t,e,a.pathnames):e,[t,e])},useRouter:function(){let e=(0,n.useRouter)(),t=(0,l.Ym)(),i=(0,n.usePathname)();return(0,r.useMemo)(()=>{function n(e){return function(n,r){let{locale:l,...c}=r||{},s=[o({href:n,locale:l||t})];Object.keys(c).length>0&&s.push(c),e(...s),(0,h.A)(a.localeCookie,i,t,l)}}return{...e,push:n(e.push),replace:n(e.replace),prefetch:n(e.prefetch)}},[t,i,e])},getPathname:o}}},1670:(e,t,a)=>{"use strict";a.d(t,{default:()=>c});var n=a(5155),r=a(2115),o=a(2388),l=a(7652),i=a(6707);function c(e){let{toolId:t,toolWebsite:a,locale:c,currentOption:s="free",currentDate:u,minFreeDate:f,minPaidDate:h,hasPaidOrder:m=!1,orderId:d,isEditMode:p=!1,badgeVerified:P=!1}=e,b=(0,o.rd)(),k=(0,l.c3)("launch"),[y,x]=(0,r.useState)(!1),[L,w]=(0,r.useState)(""),g=async(e,a)=>{x(!0),w("");try{let n=await fetch("/api/tools/".concat(t,"/launch-date"),{method:"POST",headers:{"Content-Type":"application/json","X-Locale":c},body:JSON.stringify({launchOption:e,selectedDate:a,hasPaidOrder:m})}),r=await n.json();r.success?"paid"===e&&r.data.paymentUrl?window.location.href=r.data.paymentUrl:b.push("/submit/launch-date-success?toolId=".concat(t)):w(r.message||k("submit_failed"))}catch(e){w(k("network_error"))}finally{x(!1)}};return(0,n.jsx)(i.A,{toolId:t,toolWebsite:a,currentOption:m?"paid":s,currentDate:u,isEditing:p,onSubmit:g,isSubmitting:y,error:L,hasPaidOrder:m,badgeVerified:P})}},2388:(e,t,a)=>{"use strict";a.d(t,{N_:()=>l,a8:()=>c,rd:()=>s});var n=a(9984),r=a(981);let o=(0,n.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:l,redirect:i,usePathname:c,useRouter:s}=(0,r.A)(o)},6096:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var n=a(3385),r=a(5155);function o(e){let{locale:t,...a}=e;if(!t)throw Error(void 0);return(0,r.jsx)(n.Dk,{locale:t,...a})}},9010:(e,t,a)=>{Promise.resolve().then(a.bind(a,6160)),Promise.resolve().then(a.bind(a,6096)),Promise.resolve().then(a.bind(a,1670))},9984:(e,t,a)=>{"use strict";function n(e){return e}a.d(t,{A:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,6707,8441,1684,7358],()=>t(9010)),_N_E=e.O()}]);