(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[245],{981:(e,t,a)=>{"use strict";a.d(t,{A:()=>m});var n=a(5695),r=a(2115),o=a.t(r,2),l=a(3385),c=o["use".trim()],i=a(3225),s=a(6160),u=a(469),f=a(5155),h=a(8986);function m(e){let{Link:t,config:a,getPathname:o,...m}=function(e,t){var a,o,l;let h={...a=t||{},localePrefix:"object"==typeof(l=a.localePrefix)?l:{mode:l||"always"},localeCookie:!!((o=a.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof o&&o},localeDetection:a.localeDetection??!0,alternateLinks:a.alternateLinks??!0},m=h.pathnames,d=(0,r.forwardRef)(function({href:t,locale:a,...n},r){let o,l;"object"==typeof t?(o=t.pathname,l=t.params):o=t;let u=(0,i._x)(t),d=e(),P=(0,i.yL)(d)?c(d):d,k=u?p({locale:a||P,href:null==m?o:{pathname:o,params:l},forcePrefix:null!=a||void 0}):o;return(0,f.jsx)(s.default,{ref:r,href:"object"==typeof t?{...t,pathname:k}:k,locale:a,localeCookie:h.localeCookie,...n})});function p(e){let t,{forcePrefix:a,href:n,locale:r}=e;return null==m?"object"==typeof n?(t=n.pathname,n.query&&(t+=(0,u.Zn)(n.query))):t=n:t=(0,u.FP)({locale:r,...(0,u.TK)(n),pathnames:h.pathnames}),(0,u.x3)(t,r,h,a)}function P(e){return function(t,...a){return e(p(t),...a)}}return{config:h,Link:d,redirect:P(n.redirect),permanentRedirect:P(n.permanentRedirect),getPathname:p}}(l.Ym,e);return{...m,Link:t,usePathname:function(){let e=function(e){let t=(0,n.usePathname)(),a=(0,l.Ym)();return(0,r.useMemo)(()=>{if(!t)return t;let n=t,r=(0,i.XP)(a,e.localePrefix);if((0,i.wO)(r,t))n=(0,i.MY)(t,r);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,i.bL)(a);(0,i.wO)(e,t)&&(n=(0,i.MY)(t,e))}return n},[e.localePrefix,a,t])}(a),t=(0,l.Ym)();return(0,r.useMemo)(()=>e&&a.pathnames?(0,u.aM)(t,e,a.pathnames):e,[t,e])},useRouter:function(){let e=(0,n.useRouter)(),t=(0,l.Ym)(),c=(0,n.usePathname)();return(0,r.useMemo)(()=>{function n(e){return function(n,r){let{locale:l,...i}=r||{},s=[o({href:n,locale:l||t})];Object.keys(i).length>0&&s.push(i),e(...s),(0,h.A)(a.localeCookie,c,t,l)}}return{...e,push:n(e.push),replace:n(e.replace),prefetch:n(e.prefetch)}},[t,c,e])},getPathname:o}}},1670:(e,t,a)=>{"use strict";a.d(t,{default:()=>i});var n=a(5155),r=a(2115),o=a(2388),l=a(7652),c=a(3402);function i(e){let{toolId:t,locale:a,currentOption:i="free",currentDate:s,minFreeDate:u,minPaidDate:f,hasPaidOrder:h=!1,orderId:m,isEditMode:d=!1}=e,p=(0,o.rd)(),P=(0,l.c3)("launch"),[k,y]=(0,r.useState)(!1),[x,b]=(0,r.useState)(""),L=async(e,n)=>{y(!0),b("");try{let r=await fetch("/api/tools/".concat(t,"/launch-date"),{method:"POST",headers:{"Content-Type":"application/json","X-Locale":a},body:JSON.stringify({launchOption:e,selectedDate:n,hasPaidOrder:h})}),o=await r.json();o.success?"paid"===e&&o.data.paymentUrl?window.location.href=o.data.paymentUrl:p.push("/submit/launch-date-success?toolId=".concat(t)):b(o.message||P("submit_failed"))}catch(e){b(P("network_error"))}finally{y(!1)}};return(0,n.jsx)(c.A,{toolId:t,currentOption:h?"paid":i,currentDate:s,isEditing:d,onSubmit:L,isSubmitting:k,error:x,hasPaidOrder:h})}},2388:(e,t,a)=>{"use strict";a.d(t,{N_:()=>l,a8:()=>i,rd:()=>s});var n=a(9984),r=a(981);let o=(0,n.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:l,redirect:c,usePathname:i,useRouter:s}=(0,r.A)(o)},6096:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var n=a(3385),r=a(5155);function o(e){let{locale:t,...a}=e;if(!t)throw Error(void 0);return(0,r.jsx)(n.Dk,{locale:t,...a})}},9010:(e,t,a)=>{Promise.resolve().then(a.bind(a,6160)),Promise.resolve().then(a.bind(a,6096)),Promise.resolve().then(a.bind(a,1670))},9984:(e,t,a)=>{"use strict";function n(e){return e}a.d(t,{A:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[3385,6160,3402,8441,1684,7358],()=>t(9010)),_N_E=e.O()}]);