(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[465],{981:(e,t,r)=>{"use strict";r.d(t,{A:()=>m});var n=r(5695),o=r(2115),a=r.t(o,2),l=r(3385),i=a["use".trim()],c=r(3225),s=r(6160),u=r(469),d=r(5155),f=r(8986);function m(e){let{Link:t,config:r,getPathname:a,...m}=function(e,t){var r,a,l;let f={...r=t||{},localePrefix:"object"==typeof(l=r.localePrefix)?l:{mode:l||"always"},localeCookie:!!((a=r.localeCookie)??1)&&{name:"NEXT_LOCALE",sameSite:"lax",..."object"==typeof a&&a},localeDetection:r.localeDetection??!0,alternateLinks:r.alternateLinks??!0},m=f.pathnames,h=(0,o.forwardRef)(function({href:t,locale:r,...n},o){let a,l;"object"==typeof t?(a=t.pathname,l=t.params):a=t;let u=(0,c._x)(t),h=e(),b=(0,c.yL)(h)?i(h):h,v=u?p({locale:r||b,href:null==m?a:{pathname:a,params:l},forcePrefix:null!=r||void 0}):a;return(0,d.jsx)(s.default,{ref:o,href:"object"==typeof t?{...t,pathname:v}:v,locale:r,localeCookie:f.localeCookie,...n})});function p(e){let t,{forcePrefix:r,href:n,locale:o}=e;return null==m?"object"==typeof n?(t=n.pathname,n.query&&(t+=(0,u.Zn)(n.query))):t=n:t=(0,u.FP)({locale:o,...(0,u.TK)(n),pathnames:f.pathnames}),(0,u.x3)(t,o,f,r)}function b(e){return function(t,...r){return e(p(t),...r)}}return{config:f,Link:h,redirect:b(n.redirect),permanentRedirect:b(n.permanentRedirect),getPathname:p}}(l.Ym,e);return{...m,Link:t,usePathname:function(){let e=function(e){let t=(0,n.usePathname)(),r=(0,l.Ym)();return(0,o.useMemo)(()=>{if(!t)return t;let n=t,o=(0,c.XP)(r,e.localePrefix);if((0,c.wO)(o,t))n=(0,c.MY)(t,o);else if("as-needed"===e.localePrefix.mode&&e.localePrefix.prefixes){let e=(0,c.bL)(r);(0,c.wO)(e,t)&&(n=(0,c.MY)(t,e))}return n},[e.localePrefix,r,t])}(r),t=(0,l.Ym)();return(0,o.useMemo)(()=>e&&r.pathnames?(0,u.aM)(t,e,r.pathnames):e,[t,e])},useRouter:function(){let e=(0,n.useRouter)(),t=(0,l.Ym)(),i=(0,n.usePathname)();return(0,o.useMemo)(()=>{function n(e){return function(n,o){let{locale:l,...c}=o||{},s=[a({href:n,locale:l||t})];Object.keys(c).length>0&&s.push(c),e(...s),(0,f.A)(r.localeCookie,i,t,l)}}return{...e,push:n(e.push),replace:n(e.replace),prefetch:n(e.prefetch)}},[t,i,e])},getPathname:a}}},2388:(e,t,r)=>{"use strict";r.d(t,{N_:()=>l,a8:()=>c,rd:()=>s});var n=r(9984),o=r(981);let a=(0,n.A)({locales:["en","zh"],defaultLocale:"en",localePrefix:"always",localeDetection:!0,alternateLinks:!0,localeCookie:{name:"NEXT_LOCALE",sameSite:"lax",maxAge:31536e3}}),{Link:l,redirect:i,usePathname:c,useRouter:s}=(0,o.A)(a)},4354:(e,t,r)=>{"use strict";r.d(t,{default:()=>l});var n=r(5155);r(2115);var o=r(2388),a=r(7652);let l=e=>{let{category:t}=e,r=(0,a.c3)("common");return(0,n.jsx)(o.N_,{href:"/categories/".concat(t.slug),children:(0,n.jsx)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 hover:scale-105 cursor-pointer",style:{height:"100%"},children:(0,n.jsxs)("div",{className:"p-6",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-4 mb-4",children:[(0,n.jsx)("div",{className:"w-12 h-12 rounded-lg flex items-center justify-center text-2xl",style:{backgroundColor:t.color||"#3B82F6"},children:(0,n.jsx)("span",{className:"text-white",children:t.icon||"\uD83D\uDD27"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:t.name}),(0,n.jsx)("p",{className:"text-sm text-gray-500",children:r("tools_count",{count:t.toolCount})})]})]}),(0,n.jsx)("p",{className:"text-gray-600 text-sm",children:t.description})]})})})}},4436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>u});var n=r(2115),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=n.createContext&&n.createContext(o),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,o,a;n=e,o=t,a=r[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(o))in n?Object.defineProperty(n,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[o]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>n.createElement(d,i({attr:s({},e.attr)},t),function e(t){return t&&t.map((t,r)=>n.createElement(t.tag,s({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:o,size:a,title:c}=e,u=function(e,t){if(null==e)return{};var r,n,o=function(e,t){if(null==e)return{};var r={};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){if(t.indexOf(n)>=0)continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(o[r]=e[r])}return o}(e,l),d=a||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),n.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,u,{className:r,style:s(s({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),c&&n.createElement("title",null,c),e.children)};return void 0!==a?n.createElement(a.Consumer,null,e=>t(e)):t(o)}},4601:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var n=r(5155),o=r(2115),a=r(2388),l=r(7652),i=r(2108),c=r(9911),s=r(6214);function u(e){let{toolId:t,initialLikes:r=0,initialLiked:u=!1,onLoginRequired:d,onUnlike:f,isInLikedPage:m=!1,showCount:h=!0,size:p="md"}=e,{data:b}=(0,i.useSession)(),{getToolState:v,initializeToolState:y,toggleLike:x}=(0,s.X)(),k=(0,a.a8)(),g=(0,l.c3)("common");null==k||k.startsWith("/en");let j=v(t);(0,o.useEffect)(()=>{y(t,r,u)},[t,r,u]);let w=async()=>{if(!b){null==d||d();return}if(j.loading)return;let e=j.liked;await x(t,m)&&m&&e&&f&&f(t)},O=(()=>{switch(p){case"sm":return{button:"p-1.5",icon:"h-4 w-4",text:"text-sm"};case"lg":return{button:"p-3",icon:"h-6 w-6",text:"text-lg"};default:return{button:"p-2",icon:"h-5 w-5",text:"text-base"}}})();return(0,n.jsxs)("button",{onClick:w,disabled:j.loading,className:"\n        ".concat(O.button,"\n        inline-flex items-center space-x-1\n        ").concat(j.liked?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-500","\n        transition-colors duration-200\n        disabled:opacity-50 disabled:cursor-not-allowed\n      "),title:g(j.liked?"unlike":"like"),children:[j.loading?(0,n.jsx)("div",{className:"".concat(O.icon," animate-spin rounded-full border-2 border-gray-300 border-t-red-500")}):j.liked?(0,n.jsx)(c.Mbv,{className:O.icon}):(0,n.jsx)(c.sOK,{className:O.icon}),h&&(0,n.jsx)("span",{className:"".concat(O.text," font-medium"),children:j.likes})]})}},6096:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(3385),o=r(5155);function a(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return(0,o.jsx)(n.Dk,{locale:t,...r})}},6214:(e,t,r)=>{"use strict";r.d(t,{LikeProvider:()=>c,X:()=>s});var n=r(5155),o=r(2115),a=r(2108);let l={liked:!1,likes:0,loading:!1},i=(0,o.createContext)(null);function c(e){let{children:t}=e,{data:r}=(0,a.useSession)(),[c,s]=(0,o.useState)({}),u=(0,o.useCallback)(e=>c[e]||l,[c]),d=(0,o.useCallback)(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];s(n=>n[e]?n:{...n,[e]:{liked:r,likes:t,loading:!1}})},[]),f=(0,o.useCallback)(async e=>{if(r)try{let t=await fetch("/api/tools/".concat(e,"/like"));if(t.ok){let r=await t.json();r.success&&s(t=>({...t,[e]:{liked:r.data.liked,likes:r.data.likes,loading:!1}}))}}catch(e){console.error("Failed to refresh tool state:",e)}},[r]),m=(0,o.useCallback)(async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!r)return!1;s(t=>({...t,[e]:{...t[e]||l,loading:!0}}));try{let r=await fetch("/api/tools/".concat(e,"/like"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t?{forceUnlike:!0}:{})});if(r.ok){let t=await r.json();if(t.success)return s(r=>({...r,[e]:{liked:t.data.liked,likes:t.data.likes,loading:!1}})),!0}return s(t=>({...t,[e]:{...t[e]||l,loading:!1}})),!1}catch(t){return console.error("Like request failed:",t),s(t=>({...t,[e]:{...t[e]||l,loading:!1}})),!1}},[r]);return(0,o.useEffect)(()=>{r?Object.keys(c).forEach(e=>{f(e)}):s(e=>{let t={};return Object.keys(e).forEach(r=>{t[r]={...e[r],liked:!1,loading:!1}}),t})},[r]),(0,n.jsx)(i.Provider,{value:{toolStates:c,toggleLike:m,getToolState:u,initializeToolState:d,refreshToolState:f},children:t})}function s(){let e=(0,o.useContext)(i);if(!e)throw Error("useLike must be used within a LikeProvider");return e}},7652:(e,t,r)=>{"use strict";r.d(t,{c3:()=>a});var n=r(3385);function o(e,t){return(...e)=>{try{return t(...e)}catch{throw Error(void 0)}}}let a=o(0,n.c3);o(0,n.kc)},7940:(e,t,r)=>{Promise.resolve().then(r.bind(r,6160)),Promise.resolve().then(r.bind(r,6096)),Promise.resolve().then(r.bind(r,4354)),Promise.resolve().then(r.bind(r,4601))},9984:(e,t,r)=>{"use strict";function n(e){return e}r.d(t,{A:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[6711,3385,6160,2108,8441,1684,7358],()=>t(7940)),_N_E=e.O()}]);