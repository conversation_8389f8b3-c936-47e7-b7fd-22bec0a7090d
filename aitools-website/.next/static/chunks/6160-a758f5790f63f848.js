"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6160],{469:(e,t,n)=>{n.d(t,{DT:()=>l,FP:()=>i,TK:()=>o,Zn:()=>u,aM:()=>a,x3:()=>f});var r=n(3225);function o(e){return"string"==typeof e?{pathname:e}:e}function u(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}function i({pathname:e,locale:t,params:n,pathnames:o,query:i}){function a(e){let a,l=o[e];return l?(a=(0,r.Wl)(l,t,e),n&&Object.entries(n).forEach(([e,t])=>{let n,r;Array.isArray(t)?(n=`(\\[)?\\[...${e}\\](\\])?`,r=t.map(e=>String(e)).join("/")):(n=`\\[${e}\\]`,r=String(t)),a=a.replace(RegExp(n,"g"),r)}),a=(a=a.replace(/\[\[\.\.\..+\]\]/g,"")).split("/").map(e=>encodeURIComponent(e)).join("/")):a=e,a=(0,r.po)(a),i&&(a+=u(i)),a}if("string"==typeof e)return a(e);{let{pathname:t,...n}=e;return{...n,pathname:a(t)}}}function a(e,t,n){let o=(0,r.FD)(Object.keys(n)),u=decodeURI(t);for(let t of o){let o=n[t];if("string"==typeof o){if((0,r.ql)(o,u))return t}else if((0,r.ql)((0,r.Wl)(o,e,t),u))return t}return t}function l(e,t=window.location.pathname){return"/"===e?t:t.replace(e,"")}function f(e,t,n,o){let u,{mode:i}=n.localePrefix;return void 0!==o?u=o:(0,r._x)(e)&&("always"===i?u=!0:"as-needed"===i&&(u=n.domains?!n.domains.some(e=>e.defaultLocale===t):t!==n.defaultLocale)),u?(0,r.PJ)((0,r.XP)(t,n.localePrefix),e):e}},2664:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return u}});let r=n(9991),o=n(7102);function u(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2757:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return u},formatWithValidation:function(){return a},urlObjectKeys:function(){return i}});let r=n(6966)._(n(8859)),o=/https?|ftp|gopher|file/;function u(e){let{auth:t,hostname:n}=e,u=e.protocol||"",i=e.pathname||"",a=e.hash||"",l=e.query||"",f=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?f=t+e.host:n&&(f=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(f+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return u&&!u.endsWith(":")&&(u+=":"),e.slashes||(!u||o.test(u))&&!1!==f?(f="//"+(f||""),i&&"/"!==i[0]&&(i="/"+i)):f||(f=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+u+f+(i=i.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let i=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return u(e)}},3180:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3225:(e,t,n)=>{n.d(t,{FD:()=>y,MY:()=>u,PJ:()=>i,Wl:()=>l,XP:()=>s,_x:()=>o,bL:()=>p,po:()=>f,ql:()=>c,wO:()=>a,yL:()=>P});var r=n(9509);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function u(e,t){return e.replace(RegExp(`^${t}`),"")||"/"}function i(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function a(e,t){return t===e||t.startsWith(`${e}/`)}function l(e,t,n){return"string"==typeof e?e:e[t]||n}function f(e){let t=function(){try{return"true"===r.env._next_intl_trailing_slash}catch{return!1}}(),[n,...o]=e.split("#"),u=o.join("#"),i=n;if("/"!==i){let e=i.endsWith("/");t&&!e?i+="/":!t&&e&&(i=i.slice(0,-1))}return u&&(i+="#"+u),i}function c(e,t){let n=f(e),r=f(t);return(function(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return RegExp(`^${t}$`)})(n).test(r)}function s(e,t){return"never"!==t.mode&&t.prefixes?.[e]||p(e)}function p(e){return"/"+e}function d(e){return e.includes("[[...")}function h(e){return e.includes("[...")}function m(e){return e.includes("[")}function g(e,t){let n=e.split("/"),r=t.split("/"),o=Math.max(n.length,r.length);for(let e=0;e<o;e++){let t=n[e],o=r[e];if(!t&&o)return -1;if(t&&!o)return 1;if(t||o){if(!m(t)&&m(o))return -1;if(m(t)&&!m(o))return 1;if(!h(t)&&h(o))return -1;if(h(t)&&!h(o))return 1;if(!d(t)&&d(o))return -1;if(d(t)&&!d(o))return 1}}return 0}function y(e){return e.sort(g)}function P(e){return"function"==typeof e.then}},5695:(e,t,n)=>{var r=n(8999);n.o(r,"permanentRedirect")&&n.d(t,{permanentRedirect:function(){return r.permanentRedirect}}),n.o(r,"redirect")&&n.d(t,{redirect:function(){return r.redirect}}),n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},6160:(e,t,n)=>{n.d(t,{default:()=>f});var r=n(6874),o=n(5695),u=n(2115),i=n(3385),a=n(8986),l=n(5155),f=(0,u.forwardRef)(function(e,t){let{href:n,locale:u,localeCookie:f,onClick:c,prefetch:s,...p}=e,d=(0,i.Ym)(),h=null!=u&&u!==d,m=(0,o.usePathname)();return h&&(s=!1),(0,l.jsx)(r,{ref:t,href:n,hrefLang:h?u:void 0,onClick:function(e){(0,a.A)(f,m,d,u),c&&c(e)},prefetch:s,...p})})},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(2115);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=u(e,r)),t&&(o.current=u(t,r))},[e,t])}function u(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return P}});let r=n(6966),o=n(5155),u=r._(n(2115)),i=n(2757),a=n(5227),l=n(9818),f=n(6654),c=n(9991),s=n(5929);n(3230);let p=n(4930),d=n(2664),h=n(6634);function m(e){return"string"==typeof e?e:(0,i.formatUrl)(e)}function g(e){let t,n,r,[i,g]=(0,u.useOptimistic)(p.IDLE_LINK_STATUS),P=(0,u.useRef)(null),{href:b,as:v,children:_,prefetch:j=null,passHref:E,replace:O,shallow:x,scroll:S,onClick:R,onMouseEnter:A,onTouchStart:C,legacyBehavior:T=!1,onNavigate:L,ref:N,unstable_dynamicOnHover:M,...k}=e;t=_,T&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let w=u.default.useContext(a.AppRouterContext),U=!1!==j,I=null===j?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:D,as:F}=u.default.useMemo(()=>{let e=m(b);return{href:e,as:v?m(v):e}},[b,v]);T&&(n=u.default.Children.only(t));let K=T?n&&"object"==typeof n&&n.ref:N,$=u.default.useCallback(e=>(null!==w&&(P.current=(0,p.mountLinkInstance)(e,D,w,I,U,g)),()=>{P.current&&((0,p.unmountLinkForCurrentNavigation)(P.current),P.current=null),(0,p.unmountPrefetchableInstance)(e)}),[U,D,w,I,g]),W={ref:(0,f.useMergedRef)($,K),onClick(e){T||"function"!=typeof R||R(e),T&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),w&&(e.defaultPrevented||function(e,t,n,r,o,i,a){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),u.default.startTransition(()=>{if(a){let e=!1;if(a({preventDefault:()=>{e=!0}}),e)return}(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==i||i,r.current)})}}(e,D,F,P,O,S,L))},onMouseEnter(e){T||"function"!=typeof A||A(e),T&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),w&&U&&(0,p.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){T||"function"!=typeof C||C(e),T&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),w&&U&&(0,p.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,c.isAbsoluteUrl)(F)?W.href=F:T&&!E&&("a"!==n.type||"href"in n.props)||(W.href=(0,s.addBasePath)(F)),r=T?u.default.cloneElement(n,W):(0,o.jsx)("a",{...k,...W,children:t}),(0,o.jsx)(y.Provider,{value:i,children:r})}n(3180);let y=(0,u.createContext)(p.IDLE_LINK_STATUS),P=()=>(0,u.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8859:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function u(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return u},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},8986:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(469);function o(e,t,n,o){if(!e||o===n||null==o||!t)return;let u=(0,r.DT)(t),{name:i,...a}=e;a.path||(a.path=""!==u?u:"/");let l=`${i}=${o};`;for(let[e,t]of Object.entries(a))l+=`${"maxAge"===e?"max-age":e}`,"boolean"!=typeof t&&(l+="="+t),l+=";";document.cookie=l}},9991:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return P},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return p},ST:function(){return d},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return i},getURL:function(){return a},isAbsoluteUrl:function(){return u},isResSent:function(){return f},loadGetInitialProps:function(){return s},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),u=0;u<r;u++)o[u]=arguments[u];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,u=e=>o.test(e);function i(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function a(){let{href:e}=window.location,t=i();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function f(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function s(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await s(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&f(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let p="undefined"!=typeof performance,d=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class P extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);