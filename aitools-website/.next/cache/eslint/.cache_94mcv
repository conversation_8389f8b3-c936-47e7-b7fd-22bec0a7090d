[{"/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx": "1", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx": "2", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx": "3", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx": "4", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx": "5", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx": "6", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx": "7", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx": "8", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx": "9", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx": "10", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx": "11", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx": "12", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx": "13", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx": "14", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx": "15", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx": "16", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx": "17", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx": "18", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx": "19", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx": "20", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx": "21", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx": "22", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx": "23", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx": "24", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/ActionButtons.tsx": "25", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/EditLaunchDateButton.tsx": "26", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx": "27", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx": "28", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx": "29", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx": "30", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx": "31", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx": "32", "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx": "33", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts": "34", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts": "35", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts": "36", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts": "37", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts": "38", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts": "39", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts": "40", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts": "41", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts": "42", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts": "43", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts": "44", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts": "45", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts": "46", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts": "47", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts": "48", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts": "49", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts": "50", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts": "51", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts": "52", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts": "53", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts": "54", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts": "55", "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts": "56", "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx": "57", "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx": "58", "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts": "59", "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx": "60", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx": "61", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx": "62", "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx": "63", "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx": "64", "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx": "65", "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx": "66", "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx": "67", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx": "68", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCardClient.tsx": "69", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx": "70", "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx": "71", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageServer.tsx": "72", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryFiltersClient.tsx": "73", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageServer.tsx": "74", "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryToolsDisplayClient.tsx": "75", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Footer.tsx": "76", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx": "77", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx": "78", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx": "79", "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx": "80", "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx": "81", "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsListClient.tsx": "82", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx": "83", "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx": "84", "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx": "85", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx": "86", "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx": "87", "/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx": "88", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx": "89", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx": "90", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionServer.tsx": "91", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx": "92", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx": "93", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx": "94", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailServer.tsx": "95", "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx": "96", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx": "97", "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx": "98", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts": "99", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts": "100", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts": "101", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags-i18n.ts": "102", "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts": "103", "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx": "104", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts": "105", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts": "106", "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts": "107", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts": "108", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts": "109", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts": "110", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts": "111", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts": "112", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts": "113", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts": "114", "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts": "115", "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts": "116", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts": "117", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts": "118", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts": "119", "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts": "120", "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts": "121", "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts": "122"}, {"size": 8982, "mtime": 1751080369147, "results": "123", "hashOfConfig": "124"}, {"size": 12973, "mtime": 1751216809007, "results": "125", "hashOfConfig": "124"}, {"size": 18534, "mtime": 1751284027255, "results": "126", "hashOfConfig": "124"}, {"size": 6022, "mtime": 1751216761592, "results": "127", "hashOfConfig": "124"}, {"size": 515, "mtime": 1751216207594, "results": "128", "hashOfConfig": "124"}, {"size": 11242, "mtime": 1751191337661, "results": "129", "hashOfConfig": "124"}, {"size": 6739, "mtime": 1751333014001, "results": "130", "hashOfConfig": "124"}, {"size": 7512, "mtime": 1751337873996, "results": "131", "hashOfConfig": "124"}, {"size": 11513, "mtime": 1751080448998, "results": "132", "hashOfConfig": "124"}, {"size": 13683, "mtime": 1751217289644, "results": "133", "hashOfConfig": "124"}, {"size": 5798, "mtime": 1751214976702, "results": "134", "hashOfConfig": "124"}, {"size": 16239, "mtime": 1751427833953, "results": "135", "hashOfConfig": "124"}, {"size": 7713, "mtime": 1751331527276, "results": "136", "hashOfConfig": "124"}, {"size": 3945, "mtime": 1751331961565, "results": "137", "hashOfConfig": "124"}, {"size": 11245, "mtime": 1751385214046, "results": "138", "hashOfConfig": "124"}, {"size": 2140, "mtime": 1751429631571, "results": "139", "hashOfConfig": "124"}, {"size": 10571, "mtime": 1751216222080, "results": "140", "hashOfConfig": "124"}, {"size": 2188, "mtime": 1751331961569, "results": "141", "hashOfConfig": "124"}, {"size": 2799, "mtime": 1751127821278, "results": "142", "hashOfConfig": "124"}, {"size": 21012, "mtime": 1751216524695, "results": "143", "hashOfConfig": "124"}, {"size": 1256, "mtime": 1751354075250, "results": "144", "hashOfConfig": "124"}, {"size": 2222, "mtime": 1751331527296, "results": "145", "hashOfConfig": "124"}, {"size": 6417, "mtime": 1751275156496, "results": "146", "hashOfConfig": "124"}, {"size": 1323, "mtime": 1751352144100, "results": "147", "hashOfConfig": "124"}, {"size": 801, "mtime": 1751267570019, "results": "148", "hashOfConfig": "124"}, {"size": 804, "mtime": 1751290149892, "results": "149", "hashOfConfig": "124"}, {"size": 10072, "mtime": 1751331961561, "results": "150", "hashOfConfig": "124"}, {"size": 13317, "mtime": 1751384975078, "results": "151", "hashOfConfig": "124"}, {"size": 4543, "mtime": 1750930937103, "results": "152", "hashOfConfig": "124"}, {"size": 6972, "mtime": 1751018762448, "results": "153", "hashOfConfig": "124"}, {"size": 3885, "mtime": 1751018851458, "results": "154", "hashOfConfig": "124"}, {"size": 5768, "mtime": 1751419880504, "results": "155", "hashOfConfig": "124"}, {"size": 5801, "mtime": 1751247859534, "results": "156", "hashOfConfig": "124"}, {"size": 5321, "mtime": 1750906802986, "results": "157", "hashOfConfig": "124"}, {"size": 2206, "mtime": 1751160497677, "results": "158", "hashOfConfig": "124"}, {"size": 2381, "mtime": 1751160563812, "results": "159", "hashOfConfig": "124"}, {"size": 3265, "mtime": 1751163869055, "results": "160", "hashOfConfig": "124"}, {"size": 171, "mtime": 1750921851894, "results": "161", "hashOfConfig": "124"}, {"size": 3714, "mtime": 1750921931408, "results": "162", "hashOfConfig": "124"}, {"size": 4683, "mtime": 1751160718688, "results": "163", "hashOfConfig": "124"}, {"size": 2305, "mtime": 1751379451876, "results": "164", "hashOfConfig": "124"}, {"size": 4267, "mtime": 1751161594662, "results": "165", "hashOfConfig": "124"}, {"size": 2553, "mtime": 1751161424205, "results": "166", "hashOfConfig": "124"}, {"size": 4357, "mtime": 1751288615702, "results": "167", "hashOfConfig": "124"}, {"size": 5242, "mtime": 1751013821668, "results": "168", "hashOfConfig": "124"}, {"size": 1022, "mtime": 1750984456438, "results": "169", "hashOfConfig": "124"}, {"size": 4831, "mtime": 1751384010179, "results": "170", "hashOfConfig": "124"}, {"size": 12978, "mtime": 1751331527297, "results": "171", "hashOfConfig": "124"}, {"size": 4227, "mtime": 1751162633491, "results": "172", "hashOfConfig": "124"}, {"size": 8858, "mtime": 1751199328528, "results": "173", "hashOfConfig": "124"}, {"size": 3803, "mtime": 1751161706933, "results": "174", "hashOfConfig": "124"}, {"size": 5414, "mtime": 1751379027032, "results": "175", "hashOfConfig": "124"}, {"size": 6117, "mtime": 1751296155444, "results": "176", "hashOfConfig": "124"}, {"size": 2794, "mtime": 1751164176243, "results": "177", "hashOfConfig": "124"}, {"size": 2491, "mtime": 1751160639981, "results": "178", "hashOfConfig": "124"}, {"size": 3444, "mtime": 1751161223047, "results": "179", "hashOfConfig": "124"}, {"size": 431, "mtime": 1751165597653, "results": "180", "hashOfConfig": "124"}, {"size": 245, "mtime": 1751244021142, "results": "181", "hashOfConfig": "124"}, {"size": 2933, "mtime": 1751377798557, "results": "182", "hashOfConfig": "124"}, {"size": 1579, "mtime": 1751338812726, "results": "183", "hashOfConfig": "124"}, {"size": 845, "mtime": 1750908285683, "results": "184", "hashOfConfig": "124"}, {"size": 9057, "mtime": 1751271932960, "results": "185", "hashOfConfig": "124"}, {"size": 77, "mtime": 1751171640781, "results": "186", "hashOfConfig": "124"}, {"size": 505, "mtime": 1750908273441, "results": "187", "hashOfConfig": "124"}, {"size": 3854, "mtime": 1751254369488, "results": "188", "hashOfConfig": "124"}, {"size": 863, "mtime": 1750908296528, "results": "189", "hashOfConfig": "124"}, {"size": 6526, "mtime": 1751292943182, "results": "190", "hashOfConfig": "124"}, {"size": 4776, "mtime": 1751429889494, "results": "191", "hashOfConfig": "124"}, {"size": 4813, "mtime": 1751431419208, "results": "192", "hashOfConfig": "124"}, {"size": 9251, "mtime": 1751216490067, "results": "193", "hashOfConfig": "124"}, {"size": 6887, "mtime": 1751243760925, "results": "194", "hashOfConfig": "124"}, {"size": 4600, "mtime": 1751332637424, "results": "195", "hashOfConfig": "124"}, {"size": 6182, "mtime": 1751332884370, "results": "196", "hashOfConfig": "124"}, {"size": 5013, "mtime": 1751334241567, "results": "197", "hashOfConfig": "124"}, {"size": 2062, "mtime": 1751332906391, "results": "198", "hashOfConfig": "124"}, {"size": 3074, "mtime": 1751384753885, "results": "199", "hashOfConfig": "124"}, {"size": 2451, "mtime": 1751243869428, "results": "200", "hashOfConfig": "124"}, {"size": 1680, "mtime": 1751216156055, "results": "201", "hashOfConfig": "124"}, {"size": 1554, "mtime": 1751217104534, "results": "202", "hashOfConfig": "124"}, {"size": 1240, "mtime": 1751243730311, "results": "203", "hashOfConfig": "124"}, {"size": 7493, "mtime": 1751431462500, "results": "204", "hashOfConfig": "124"}, {"size": 22912, "mtime": 1751447037347, "results": "205", "hashOfConfig": "124"}, {"size": 867, "mtime": 1750922283437, "results": "206", "hashOfConfig": "124"}, {"size": 362, "mtime": 1750922147686, "results": "207", "hashOfConfig": "124"}, {"size": 8948, "mtime": 1751349101013, "results": "208", "hashOfConfig": "124"}, {"size": 5275, "mtime": 1751023043127, "results": "209", "hashOfConfig": "124"}, {"size": 2605, "mtime": 1751019665417, "results": "210", "hashOfConfig": "124"}, {"size": 26539, "mtime": 1751357208927, "results": "211", "hashOfConfig": "124"}, {"size": 9319, "mtime": 1751216453727, "results": "212", "hashOfConfig": "124"}, {"size": 9034, "mtime": 1751419577166, "results": "213", "hashOfConfig": "124"}, {"size": 2444, "mtime": 1751419607784, "results": "214", "hashOfConfig": "124"}, {"size": 3366, "mtime": 1751418793419, "results": "215", "hashOfConfig": "124"}, {"size": 10116, "mtime": 1751217323841, "results": "216", "hashOfConfig": "124"}, {"size": 872, "mtime": 1751419908917, "results": "217", "hashOfConfig": "124"}, {"size": 8891, "mtime": 1751426791595, "results": "218", "hashOfConfig": "124"}, {"size": 8869, "mtime": 1751216466888, "results": "219", "hashOfConfig": "124"}, {"size": 5433, "mtime": 1751023279983, "results": "220", "hashOfConfig": "124"}, {"size": 2452, "mtime": 1751447058148, "results": "221", "hashOfConfig": "124"}, {"size": 5155, "mtime": 1751181125061, "results": "222", "hashOfConfig": "124"}, {"size": 3849, "mtime": 1751072665492, "results": "223", "hashOfConfig": "124"}, {"size": 4478, "mtime": 1751271806862, "results": "224", "hashOfConfig": "124"}, {"size": 3036, "mtime": 1751293637039, "results": "225", "hashOfConfig": "124"}, {"size": 5288, "mtime": 1751292694381, "results": "226", "hashOfConfig": "124"}, {"size": 5354, "mtime": 1751086484747, "results": "227", "hashOfConfig": "124"}, {"size": 432, "mtime": 1751216104505, "results": "228", "hashOfConfig": "124"}, {"size": 410, "mtime": 1751211931429, "results": "229", "hashOfConfig": "124"}, {"size": 757, "mtime": 1751331527325, "results": "230", "hashOfConfig": "124"}, {"size": 10538, "mtime": 1751249910169, "results": "231", "hashOfConfig": "124"}, {"size": 7460, "mtime": 1751423543452, "results": "232", "hashOfConfig": "124"}, {"size": 5474, "mtime": 1751122879189, "results": "233", "hashOfConfig": "124"}, {"size": 3457, "mtime": 1751122835025, "results": "234", "hashOfConfig": "124"}, {"size": 921, "mtime": 1750903252798, "results": "235", "hashOfConfig": "124"}, {"size": 5018, "mtime": 1751186011040, "results": "236", "hashOfConfig": "124"}, {"size": 3274, "mtime": 1751081422931, "results": "237", "hashOfConfig": "124"}, {"size": 3985, "mtime": 1751017840303, "results": "238", "hashOfConfig": "124"}, {"size": 490, "mtime": 1751331527324, "results": "239", "hashOfConfig": "124"}, {"size": 1667, "mtime": 1750903308052, "results": "240", "hashOfConfig": "124"}, {"size": 2141, "mtime": 1750921803605, "results": "241", "hashOfConfig": "124"}, {"size": 3989, "mtime": 1750984256539, "results": "242", "hashOfConfig": "124"}, {"size": 4859, "mtime": 1751181597006, "results": "243", "hashOfConfig": "124"}, {"size": 3406, "mtime": 1751090710634, "results": "244", "hashOfConfig": "124"}, {"size": 720, "mtime": 1750903327281, "results": "245", "hashOfConfig": "124"}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 13, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1ifueu7", {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 9, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 6, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 26, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 8, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "492", "messages": "493", "suppressedMessages": "494", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "495", "messages": "496", "suppressedMessages": "497", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "498", "messages": "499", "suppressedMessages": "500", "errorCount": 10, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "501", "messages": "502", "suppressedMessages": "503", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "504", "messages": "505", "suppressedMessages": "506", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "507", "messages": "508", "suppressedMessages": "509", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "510", "messages": "511", "suppressedMessages": "512", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "513", "messages": "514", "suppressedMessages": "515", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "516", "messages": "517", "suppressedMessages": "518", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "519", "messages": "520", "suppressedMessages": "521", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "522", "messages": "523", "suppressedMessages": "524", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "525", "messages": "526", "suppressedMessages": "527", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "528", "messages": "529", "suppressedMessages": "530", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "531", "messages": "532", "suppressedMessages": "533", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "534", "messages": "535", "suppressedMessages": "536", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "537", "messages": "538", "suppressedMessages": "539", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "540", "messages": "541", "suppressedMessages": "542", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "543", "messages": "544", "suppressedMessages": "545", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "546", "messages": "547", "suppressedMessages": "548", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "549", "messages": "550", "suppressedMessages": "551", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "552", "messages": "553", "suppressedMessages": "554", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "555", "messages": "556", "suppressedMessages": "557", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "558", "messages": "559", "suppressedMessages": "560", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "561", "messages": "562", "suppressedMessages": "563", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "564", "messages": "565", "suppressedMessages": "566", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "567", "messages": "568", "suppressedMessages": "569", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "570", "messages": "571", "suppressedMessages": "572", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "573", "messages": "574", "suppressedMessages": "575", "errorCount": 5, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "576", "messages": "577", "suppressedMessages": "578", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "579", "messages": "580", "suppressedMessages": "581", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "582", "messages": "583", "suppressedMessages": "584", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "585", "messages": "586", "suppressedMessages": "587", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "588", "messages": "589", "suppressedMessages": "590", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "591", "messages": "592", "suppressedMessages": "593", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "594", "messages": "595", "suppressedMessages": "596", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "597", "messages": "598", "suppressedMessages": "599", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "600", "messages": "601", "suppressedMessages": "602", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "603", "messages": "604", "suppressedMessages": "605", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "606", "messages": "607", "suppressedMessages": "608", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "609", "messages": "610", "suppressedMessages": "611", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/about/page.tsx", ["612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/dashboard/page.tsx", ["625", "626", "627", "628", "629", "630", "631", "632", "633", "634"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/page.tsx", ["635", "636", "637", "638", "639", "640", "641", "642"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/AdminToolDetailClient.tsx", ["643", "644", "645"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/BackButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/admin/tools/[id]/page.tsx", ["646", "647", "648", "649"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/[slug]/page.tsx", ["650", "651", "652"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/categories/page.tsx", ["653", "654", "655", "656", "657", "658", "659", "660"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/contact/page.tsx", ["661", "662", "663", "664", "665", "666", "667", "668", "669", "670", "671", "672", "673", "674"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/dashboard/page.tsx", ["675"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/page.tsx", ["676"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/CheckoutClient.tsx", ["677"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/payment/checkout/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/privacy/page.tsx", ["678", "679", "680", "681", "682", "683", "684", "685", "686", "687", "688", "689", "690", "691"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/liked/page.tsx", ["692"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/page.tsx", ["693", "694", "695"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/profile/submitted/page.tsx", ["696"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/search/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/settings/page.tsx", ["697", "698", "699", "700", "701", "702"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/edit/[toolId]/page.tsx", ["703"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/LaunchDateClient.tsx", ["704", "705", "706", "707"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/launch-date/[toolId]/page.tsx", ["708", "709", "710", "711", "712", "713"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/ActionButtons.tsx", ["714"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/EditLaunchDateButton.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/submit/success/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/terms/page.tsx", ["715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739", "740"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-auth/page.tsx", ["741", "742", "743", "744"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-pricing/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/test-stripe/page.tsx", ["745"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/[id]/page.tsx", ["746"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/[locale]/tools/page.tsx", ["747", "748", "749", "750", "751", "752", "753", "754"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/stats/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/approve/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/[id]/reject/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/admin/tools/route.ts", ["755", "756"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/[...nextauth]/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/me/route.ts", ["757", "758"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/auth/send-code/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/categories/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/pay/route.ts", ["759"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/orders/[id]/route.ts", ["760"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/stripe/webhook/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/test/create-payment-intent/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/comments/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/launch-date/route.ts", ["761", "762"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/like/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/[id]/route.ts", ["763", "764", "765", "766", "767"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/publish/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/route.ts", ["768", "769", "770", "771"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/tools/submit/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/upload/logo/route.ts", ["772"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/liked-tools/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/api/user/tools/route.ts", ["773", "774", "775", "776"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/page.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/app/sitemap.xml/route.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/CategoryCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ErrorMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LaunchDateSelector.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/Layout.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/StripeCheckoutForm.tsx", ["777"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/SuccessMessage.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/TagSelector.tsx", ["778"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCard.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ToolCardClient.tsx", ["779", "780"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/LoginModal.tsx", ["781", "782", "783", "784", "785"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/auth/UserMenuClient.tsx", ["786", "787", "788"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoriesPageServer.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryFiltersClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryPageServer.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/categories/CategoryToolsDisplayClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Footer.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/Header.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/LanguageSwitcherClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/MobileMenuClient.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/layout/SearchFormClient.tsx", ["789"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/LikedToolsClient.tsx", ["790"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/profile/SubmittedToolsListClient.tsx", ["791", "792", "793", "794", "795"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/ChakraProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/providers/SessionProvider.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/search/SearchPageClient.tsx", ["796", "797", "798", "799", "800", "801", "802", "803", "804", "805"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/PerformanceMonitor.tsx", ["806", "807", "808"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/seo/SEOHead.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/submit/SubmitFormClient.tsx", ["809", "810"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSection.tsx", ["811", "812", "813", "814"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionClient.tsx", ["815", "816", "817"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/CommentSectionServer.tsx", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/LikeButton.tsx", ["818", "819"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailClient.tsx", ["820", "821", "822", "823", "824"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailInteraction.tsx", ["825"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolDetailServer.tsx", ["826", "827", "828", "829"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/tools/ToolsPageClient.tsx", ["830"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/LazyLoad.tsx", ["831"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/components/ui/OptimizedImage.tsx", ["832", "833", "834"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories-i18n.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/categories.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/pricing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags-i18n.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/constants/tags.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/contexts/LikeContext.tsx", ["835"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/config.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/request.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/i18n/routing.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api-messages.ts", ["836"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/api.ts", ["837", "838", "839", "840", "841"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/auth.ts", ["842", "843", "844", "845", "846"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/env.ts", ["847"], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/mongodb.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seedData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/seo/structuredData.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/lib/stripe.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/middleware.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Category.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Comment.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Order.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/Tool.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/models/User.ts", [], [], "/Users/<USER>/workspace/aitools/aitools-website/src/types/global.d.ts", ["848"], [], {"ruleId": "849", "severity": 2, "message": "850", "line": 89, "column": 15, "nodeType": "851", "endLine": 92, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 89, "column": 15, "nodeType": "851", "endLine": 92, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 89, "column": 15, "nodeType": "851", "endLine": 92, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 89, "column": 15, "nodeType": "851", "endLine": 92, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 89, "column": 15, "nodeType": "851", "endLine": 92, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 89, "column": 15, "nodeType": "851", "endLine": 92, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 89, "column": 15, "nodeType": "851", "endLine": 92, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "852", "line": 200, "column": 13, "nodeType": "851", "endLine": 203, "endColumn": 14}, {"ruleId": "849", "severity": 2, "message": "852", "line": 200, "column": 13, "nodeType": "851", "endLine": 203, "endColumn": 14}, {"ruleId": "849", "severity": 2, "message": "852", "line": 200, "column": 13, "nodeType": "851", "endLine": 203, "endColumn": 14}, {"ruleId": "849", "severity": 2, "message": "852", "line": 200, "column": 13, "nodeType": "851", "endLine": 203, "endColumn": 14}, {"ruleId": "849", "severity": 2, "message": "852", "line": 200, "column": 13, "nodeType": "851", "endLine": 203, "endColumn": 14}, {"ruleId": "849", "severity": 2, "message": "852", "line": 200, "column": 13, "nodeType": "851", "endLine": 203, "endColumn": 14}, {"ruleId": "853", "severity": 2, "message": "854", "line": 13, "column": 3, "nodeType": null, "messageId": "855", "endLine": 13, "endColumn": 8}, {"ruleId": "853", "severity": 2, "message": "856", "line": 18, "column": 3, "nodeType": null, "messageId": "855", "endLine": 18, "endColumn": 8}, {"ruleId": "853", "severity": 2, "message": "857", "line": 19, "column": 3, "nodeType": null, "messageId": "855", "endLine": 19, "endColumn": 11}, {"ruleId": "853", "severity": 2, "message": "858", "line": 22, "column": 3, "nodeType": null, "messageId": "855", "endLine": 22, "endColumn": 7}, {"ruleId": "853", "severity": 2, "message": "859", "line": 28, "column": 9, "nodeType": null, "messageId": "855", "endLine": 28, "endColumn": 17}, {"ruleId": "860", "severity": 1, "message": "861", "line": 40, "column": 6, "nodeType": "862", "endLine": 40, "endColumn": 17, "suggestions": "863"}, {"ruleId": "853", "severity": 2, "message": "864", "line": 54, "column": 14, "nodeType": null, "messageId": "855", "endLine": 54, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "865", "line": 61, "column": 9, "nodeType": null, "messageId": "855", "endLine": 61, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "866", "line": 70, "column": 9, "nodeType": null, "messageId": "855", "endLine": 70, "endColumn": 24}, {"ruleId": "853", "severity": 2, "message": "867", "line": 83, "column": 9, "nodeType": null, "messageId": "855", "endLine": 83, "endColumn": 27}, {"ruleId": "853", "severity": 2, "message": "868", "line": 16, "column": 3, "nodeType": null, "messageId": "855", "endLine": 16, "endColumn": 6}, {"ruleId": "853", "severity": 2, "message": "859", "line": 27, "column": 9, "nodeType": null, "messageId": "855", "endLine": 27, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "869", "line": 31, "column": 9, "nodeType": null, "messageId": "855", "endLine": 31, "endColumn": 17}, {"ruleId": "860", "severity": 1, "message": "870", "line": 48, "column": 6, "nodeType": "862", "endLine": 48, "endColumn": 20, "suggestions": "871"}, {"ruleId": "853", "severity": 2, "message": "864", "line": 65, "column": 14, "nodeType": null, "messageId": "855", "endLine": 65, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "864", "line": 98, "column": 14, "nodeType": null, "messageId": "855", "endLine": 98, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "864", "line": 124, "column": 14, "nodeType": null, "messageId": "855", "endLine": 124, "endColumn": 17}, {"ruleId": "872", "severity": 1, "message": "873", "line": 288, "column": 27, "nodeType": "851", "endLine": 292, "endColumn": 29}, {"ruleId": "853", "severity": 2, "message": "874", "line": 22, "column": 3, "nodeType": null, "messageId": "855", "endLine": 22, "endColumn": 9}, {"ruleId": "853", "severity": 2, "message": "864", "line": 59, "column": 14, "nodeType": null, "messageId": "855", "endLine": 59, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "864", "line": 98, "column": 14, "nodeType": null, "messageId": "855", "endLine": 98, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "875", "line": 4, "column": 21, "nodeType": null, "messageId": "855", "endLine": 4, "endColumn": 25}, {"ruleId": "853", "severity": 2, "message": "876", "line": 9, "column": 3, "nodeType": null, "messageId": "855", "endLine": 9, "endColumn": 12}, {"ruleId": "872", "severity": 1, "message": "873", "line": 148, "column": 13, "nodeType": "851", "endLine": 152, "endColumn": 15}, {"ruleId": "872", "severity": 1, "message": "873", "line": 228, "column": 19, "nodeType": "851", "endLine": 233, "endColumn": 21}, {"ruleId": "853", "severity": 2, "message": "877", "line": 6, "column": 29, "nodeType": null, "messageId": "855", "endLine": 6, "endColumn": 44}, {"ruleId": "853", "severity": 2, "message": "878", "line": 8, "column": 10, "nodeType": null, "messageId": "855", "endLine": 8, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "879", "line": 111, "column": 12, "nodeType": null, "messageId": "855", "endLine": 111, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "879", "line": 65, "column": 12, "nodeType": null, "messageId": "855", "endLine": 65, "endColumn": 17}, {"ruleId": "849", "severity": 2, "message": "850", "line": 186, "column": 15, "nodeType": "851", "endLine": 189, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 186, "column": 15, "nodeType": "851", "endLine": 189, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 186, "column": 15, "nodeType": "851", "endLine": 189, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 186, "column": 15, "nodeType": "851", "endLine": 189, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 186, "column": 15, "nodeType": "851", "endLine": 189, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 186, "column": 15, "nodeType": "851", "endLine": 189, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 186, "column": 15, "nodeType": "851", "endLine": 189, "endColumn": 16}, {"ruleId": "853", "severity": 2, "message": "880", "line": 5, "column": 38, "nodeType": null, "messageId": "855", "endLine": 5, "endColumn": 44}, {"ruleId": "849", "severity": 2, "message": "850", "line": 99, "column": 15, "nodeType": "851", "endLine": 102, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 99, "column": 15, "nodeType": "851", "endLine": 102, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 99, "column": 15, "nodeType": "851", "endLine": 102, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 99, "column": 15, "nodeType": "851", "endLine": 102, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 99, "column": 15, "nodeType": "851", "endLine": 102, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 99, "column": 15, "nodeType": "851", "endLine": 102, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 99, "column": 15, "nodeType": "851", "endLine": 102, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "881", "line": 174, "column": 19, "nodeType": "851", "endLine": 174, "endColumn": 80}, {"ruleId": "849", "severity": 2, "message": "881", "line": 174, "column": 19, "nodeType": "851", "endLine": 174, "endColumn": 80}, {"ruleId": "849", "severity": 2, "message": "881", "line": 174, "column": 19, "nodeType": "851", "endLine": 174, "endColumn": 80}, {"ruleId": "849", "severity": 2, "message": "881", "line": 174, "column": 19, "nodeType": "851", "endLine": 174, "endColumn": 80}, {"ruleId": "849", "severity": 2, "message": "881", "line": 174, "column": 19, "nodeType": "851", "endLine": 174, "endColumn": 80}, {"ruleId": "849", "severity": 2, "message": "881", "line": 174, "column": 19, "nodeType": "851", "endLine": 174, "endColumn": 80}, {"ruleId": "853", "severity": 2, "message": "864", "line": 95, "column": 14, "nodeType": null, "messageId": "855", "endLine": 95, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "879", "line": 81, "column": 12, "nodeType": null, "messageId": "855", "endLine": 81, "endColumn": 17}, {"ruleId": "860", "severity": 1, "message": "882", "line": 50, "column": 6, "nodeType": "862", "endLine": 50, "endColumn": 15, "suggestions": "883"}, {"ruleId": "849", "severity": 2, "message": "850", "line": 65, "column": 15, "nodeType": "851", "endLine": 68, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 65, "column": 15, "nodeType": "851", "endLine": 68, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 65, "column": 15, "nodeType": "851", "endLine": 68, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 65, "column": 15, "nodeType": "851", "endLine": 68, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 65, "column": 15, "nodeType": "851", "endLine": 68, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 65, "column": 15, "nodeType": "851", "endLine": 68, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 65, "column": 15, "nodeType": "851", "endLine": 68, "endColumn": 16}, {"ruleId": "884", "severity": 2, "message": "885", "line": 159, "column": 41, "nodeType": "886", "messageId": "887", "suggestions": "888"}, {"ruleId": "849", "severity": 2, "message": "852", "line": 224, "column": 31, "nodeType": "851", "endLine": 224, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 224, "column": 31, "nodeType": "851", "endLine": 224, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 224, "column": 31, "nodeType": "851", "endLine": 224, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 224, "column": 31, "nodeType": "851", "endLine": 224, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 224, "column": 31, "nodeType": "851", "endLine": 224, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 224, "column": 31, "nodeType": "851", "endLine": 224, "endColumn": 50}, {"ruleId": "889", "severity": 2, "message": "890", "line": 57, "column": 37, "nodeType": "891", "messageId": "892", "endLine": 57, "endColumn": 40, "suggestions": "893"}, {"ruleId": "853", "severity": 2, "message": "894", "line": 11, "column": 3, "nodeType": null, "messageId": "855", "endLine": 11, "endColumn": 7}, {"ruleId": "853", "severity": 2, "message": "895", "line": 18, "column": 3, "nodeType": null, "messageId": "855", "endLine": 18, "endColumn": 7}, {"ruleId": "872", "severity": 1, "message": "873", "line": 96, "column": 19, "nodeType": "851", "endLine": 100, "endColumn": 21}, {"ruleId": "889", "severity": 2, "message": "890", "line": 59, "column": 10, "nodeType": "891", "messageId": "892", "endLine": 59, "endColumn": 13, "suggestions": "896"}, {"ruleId": "853", "severity": 2, "message": "897", "line": 12, "column": 3, "nodeType": null, "messageId": "855", "endLine": 12, "endColumn": 7}, {"ruleId": "853", "severity": 2, "message": "868", "line": 15, "column": 3, "nodeType": null, "messageId": "855", "endLine": 15, "endColumn": 6}, {"ruleId": "853", "severity": 2, "message": "864", "line": 94, "column": 14, "nodeType": null, "messageId": "855", "endLine": 94, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "864", "line": 111, "column": 14, "nodeType": null, "messageId": "855", "endLine": 111, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "864", "line": 128, "column": 14, "nodeType": null, "messageId": "855", "endLine": 128, "endColumn": 17}, {"ruleId": "872", "severity": 1, "message": "873", "line": 197, "column": 23, "nodeType": "851", "endLine": 201, "endColumn": 25}, {"ruleId": "853", "severity": 2, "message": "875", "line": 5, "column": 21, "nodeType": null, "messageId": "855", "endLine": 5, "endColumn": 25}, {"ruleId": "853", "severity": 2, "message": "898", "line": 5, "column": 10, "nodeType": null, "messageId": "855", "endLine": 5, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "899", "line": 25, "column": 3, "nodeType": null, "messageId": "855", "endLine": 25, "endColumn": 14}, {"ruleId": "853", "severity": 2, "message": "900", "line": 26, "column": 3, "nodeType": null, "messageId": "855", "endLine": 26, "endColumn": 14}, {"ruleId": "853", "severity": 2, "message": "901", "line": 28, "column": 3, "nodeType": null, "messageId": "855", "endLine": 28, "endColumn": 10}, {"ruleId": "849", "severity": 2, "message": "902", "line": 144, "column": 11, "nodeType": "851", "endLine": 147, "endColumn": 12}, {"ruleId": "849", "severity": 2, "message": "902", "line": 144, "column": 11, "nodeType": "851", "endLine": 147, "endColumn": 12}, {"ruleId": "849", "severity": 2, "message": "902", "line": 144, "column": 11, "nodeType": "851", "endLine": 147, "endColumn": 12}, {"ruleId": "849", "severity": 2, "message": "902", "line": 144, "column": 11, "nodeType": "851", "endLine": 147, "endColumn": 12}, {"ruleId": "849", "severity": 2, "message": "902", "line": 144, "column": 11, "nodeType": "851", "endLine": 147, "endColumn": 12}, {"ruleId": "849", "severity": 2, "message": "902", "line": 144, "column": 11, "nodeType": "851", "endLine": 147, "endColumn": 12}, {"ruleId": "853", "severity": 2, "message": "874", "line": 8, "column": 47, "nodeType": null, "messageId": "855", "endLine": 8, "endColumn": 53}, {"ruleId": "853", "severity": 2, "message": "903", "line": 3, "column": 8, "nodeType": null, "messageId": "855", "endLine": 3, "endColumn": 14}, {"ruleId": "849", "severity": 2, "message": "850", "line": 66, "column": 15, "nodeType": "851", "endLine": 69, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 66, "column": 15, "nodeType": "851", "endLine": 69, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 66, "column": 15, "nodeType": "851", "endLine": 69, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 66, "column": 15, "nodeType": "851", "endLine": 69, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 66, "column": 15, "nodeType": "851", "endLine": 69, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 66, "column": 15, "nodeType": "851", "endLine": 69, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 66, "column": 15, "nodeType": "851", "endLine": 69, "endColumn": 16}, {"ruleId": "884", "severity": 2, "message": "885", "line": 114, "column": 84, "nodeType": "886", "messageId": "887", "suggestions": "904"}, {"ruleId": "884", "severity": 2, "message": "905", "line": 127, "column": 61, "nodeType": "886", "messageId": "887", "suggestions": "906"}, {"ruleId": "884", "severity": 2, "message": "905", "line": 127, "column": 64, "nodeType": "886", "messageId": "887", "suggestions": "907"}, {"ruleId": "884", "severity": 2, "message": "905", "line": 127, "column": 69, "nodeType": "886", "messageId": "887", "suggestions": "908"}, {"ruleId": "884", "severity": 2, "message": "905", "line": 127, "column": 83, "nodeType": "886", "messageId": "887", "suggestions": "909"}, {"ruleId": "884", "severity": 2, "message": "885", "line": 163, "column": 35, "nodeType": "886", "messageId": "887", "suggestions": "910"}, {"ruleId": "884", "severity": 2, "message": "885", "line": 189, "column": 29, "nodeType": "886", "messageId": "887", "suggestions": "911"}, {"ruleId": "884", "severity": 2, "message": "885", "line": 191, "column": 34, "nodeType": "886", "messageId": "887", "suggestions": "912"}, {"ruleId": "884", "severity": 2, "message": "905", "line": 219, "column": 43, "nodeType": "886", "messageId": "887", "suggestions": "913"}, {"ruleId": "884", "severity": 2, "message": "905", "line": 219, "column": 49, "nodeType": "886", "messageId": "887", "suggestions": "914"}, {"ruleId": "884", "severity": 2, "message": "885", "line": 250, "column": 75, "nodeType": "886", "messageId": "887", "suggestions": "915"}, {"ruleId": "884", "severity": 2, "message": "885", "line": 255, "column": 79, "nodeType": "886", "messageId": "887", "suggestions": "916"}, {"ruleId": "849", "severity": 2, "message": "852", "line": 274, "column": 31, "nodeType": "851", "endLine": 274, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 274, "column": 31, "nodeType": "851", "endLine": 274, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 274, "column": 31, "nodeType": "851", "endLine": 274, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 274, "column": 31, "nodeType": "851", "endLine": 274, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 274, "column": 31, "nodeType": "851", "endLine": 274, "endColumn": 50}, {"ruleId": "849", "severity": 2, "message": "852", "line": 274, "column": 31, "nodeType": "851", "endLine": 274, "endColumn": 50}, {"ruleId": "853", "severity": 2, "message": "879", "line": 27, "column": 14, "nodeType": null, "messageId": "855", "endLine": 27, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "879", "line": 46, "column": 14, "nodeType": null, "messageId": "855", "endLine": 46, "endColumn": 19}, {"ruleId": "889", "severity": 2, "message": "890", "line": 63, "column": 42, "nodeType": "891", "messageId": "892", "endLine": 63, "endColumn": 45, "suggestions": "917"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 64, "column": 42, "nodeType": "891", "messageId": "892", "endLine": 64, "endColumn": 45, "suggestions": "918"}, {"ruleId": "853", "severity": 2, "message": "879", "line": 81, "column": 14, "nodeType": null, "messageId": "855", "endLine": 81, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "879", "line": 73, "column": 12, "nodeType": null, "messageId": "855", "endLine": 73, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "879", "line": 66, "column": 12, "nodeType": null, "messageId": "855", "endLine": 66, "endColumn": 17}, {"ruleId": "849", "severity": 2, "message": "850", "line": 147, "column": 15, "nodeType": "851", "endLine": 150, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 147, "column": 15, "nodeType": "851", "endLine": 150, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 147, "column": 15, "nodeType": "851", "endLine": 150, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 147, "column": 15, "nodeType": "851", "endLine": 150, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 147, "column": 15, "nodeType": "851", "endLine": 150, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 147, "column": 15, "nodeType": "851", "endLine": 150, "endColumn": 16}, {"ruleId": "849", "severity": 2, "message": "850", "line": 147, "column": 15, "nodeType": "851", "endLine": 150, "endColumn": 16}, {"ruleId": "889", "severity": 2, "message": "890", "line": 20, "column": 18, "nodeType": "891", "messageId": "892", "endLine": 20, "endColumn": 21, "suggestions": "919"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 56, "column": 22, "nodeType": "891", "messageId": "892", "endLine": 56, "endColumn": 25, "suggestions": "920"}, {"ruleId": "853", "severity": 2, "message": "921", "line": 8, "column": 27, "nodeType": null, "messageId": "855", "endLine": 8, "endColumn": 34}, {"ruleId": "889", "severity": 2, "message": "890", "line": 96, "column": 23, "nodeType": "891", "messageId": "892", "endLine": 96, "endColumn": 26, "suggestions": "922"}, {"ruleId": "853", "severity": 2, "message": "923", "line": 30, "column": 13, "nodeType": null, "messageId": "855", "endLine": 30, "endColumn": 26}, {"ruleId": "853", "severity": 2, "message": "875", "line": 5, "column": 8, "nodeType": null, "messageId": "855", "endLine": 5, "endColumn": 12}, {"ruleId": "853", "severity": 2, "message": "924", "line": 92, "column": 11, "nodeType": null, "messageId": "855", "endLine": 92, "endColumn": 14}, {"ruleId": "889", "severity": 2, "message": "890", "line": 162, "column": 25, "nodeType": "891", "messageId": "892", "endLine": 162, "endColumn": 28, "suggestions": "925"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 184, "column": 20, "nodeType": "891", "messageId": "892", "endLine": 184, "endColumn": 23, "suggestions": "926"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 216, "column": 28, "nodeType": "891", "messageId": "892", "endLine": 216, "endColumn": 31, "suggestions": "927"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 244, "column": 19, "nodeType": "891", "messageId": "892", "endLine": 244, "endColumn": 22, "suggestions": "928"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 245, "column": 56, "nodeType": "891", "messageId": "892", "endLine": 245, "endColumn": 59, "suggestions": "929"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 245, "column": 79, "nodeType": "891", "messageId": "892", "endLine": 245, "endColumn": 82, "suggestions": "930"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 23, "column": 18, "nodeType": "891", "messageId": "892", "endLine": 23, "endColumn": 21, "suggestions": "931"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 67, "column": 22, "nodeType": "891", "messageId": "892", "endLine": 67, "endColumn": 25, "suggestions": "932"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 167, "column": 19, "nodeType": "891", "messageId": "892", "endLine": 167, "endColumn": 22, "suggestions": "933"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 172, "column": 70, "nodeType": "891", "messageId": "892", "endLine": 172, "endColumn": 73, "suggestions": "934"}, {"ruleId": "853", "severity": 2, "message": "879", "line": 59, "column": 14, "nodeType": null, "messageId": "855", "endLine": 59, "endColumn": 19}, {"ruleId": "889", "severity": 2, "message": "890", "line": 41, "column": 18, "nodeType": "891", "messageId": "892", "endLine": 41, "endColumn": 21, "suggestions": "935"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 54, "column": 22, "nodeType": "891", "messageId": "892", "endLine": 54, "endColumn": 25, "suggestions": "936"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 91, "column": 52, "nodeType": "891", "messageId": "892", "endLine": 91, "endColumn": 55, "suggestions": "937"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 92, "column": 52, "nodeType": "891", "messageId": "892", "endLine": 92, "endColumn": 55, "suggestions": "938"}, {"ruleId": "853", "severity": 2, "message": "864", "line": 64, "column": 14, "nodeType": null, "messageId": "855", "endLine": 64, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "939", "line": 32, "column": 9, "nodeType": null, "messageId": "855", "endLine": 32, "endColumn": 22}, {"ruleId": "853", "severity": 2, "message": "940", "line": 5, "column": 10, "nodeType": null, "messageId": "855", "endLine": 5, "endColumn": 25}, {"ruleId": "853", "severity": 2, "message": "941", "line": 31, "column": 83, "nodeType": null, "messageId": "855", "endLine": 31, "endColumn": 89}, {"ruleId": "853", "severity": 2, "message": "859", "line": 24, "column": 9, "nodeType": null, "messageId": "855", "endLine": 24, "endColumn": 17}, {"ruleId": "853", "severity": 2, "message": "941", "line": 26, "column": 9, "nodeType": null, "messageId": "855", "endLine": 26, "endColumn": 15}, {"ruleId": "853", "severity": 2, "message": "879", "line": 51, "column": 14, "nodeType": null, "messageId": "855", "endLine": 51, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "879", "line": 91, "column": 14, "nodeType": null, "messageId": "855", "endLine": 91, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "879", "line": 118, "column": 14, "nodeType": null, "messageId": "855", "endLine": 118, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "941", "line": 18, "column": 9, "nodeType": null, "messageId": "855", "endLine": 18, "endColumn": 15}, {"ruleId": "872", "severity": 1, "message": "873", "line": 66, "column": 13, "nodeType": "851", "endLine": 70, "endColumn": 15}, {"ruleId": "872", "severity": 1, "message": "873", "line": 98, "column": 21, "nodeType": "851", "endLine": 102, "endColumn": 23}, {"ruleId": "853", "severity": 2, "message": "874", "line": 12, "column": 44, "nodeType": null, "messageId": "855", "endLine": 12, "endColumn": 50}, {"ruleId": "860", "severity": 1, "message": "942", "line": 41, "column": 6, "nodeType": "862", "endLine": 41, "endColumn": 49, "suggestions": "943"}, {"ruleId": "853", "severity": 2, "message": "944", "line": 19, "column": 3, "nodeType": null, "messageId": "855", "endLine": 19, "endColumn": 12}, {"ruleId": "853", "severity": 2, "message": "945", "line": 69, "column": 3, "nodeType": null, "messageId": "855", "endLine": 69, "endColumn": 15}, {"ruleId": "853", "severity": 2, "message": "946", "line": 75, "column": 17, "nodeType": null, "messageId": "855", "endLine": 75, "endColumn": 25}, {"ruleId": "853", "severity": 2, "message": "947", "line": 78, "column": 9, "nodeType": null, "messageId": "855", "endLine": 78, "endColumn": 22}, {"ruleId": "853", "severity": 2, "message": "879", "line": 97, "column": 14, "nodeType": null, "messageId": "855", "endLine": 97, "endColumn": 19}, {"ruleId": "853", "severity": 2, "message": "875", "line": 8, "column": 10, "nodeType": null, "messageId": "855", "endLine": 8, "endColumn": 14}, {"ruleId": "853", "severity": 2, "message": "948", "line": 9, "column": 18, "nodeType": null, "messageId": "855", "endLine": 9, "endColumn": 24}, {"ruleId": "853", "severity": 2, "message": "949", "line": 9, "column": 26, "nodeType": null, "messageId": "855", "endLine": 9, "endColumn": 30}, {"ruleId": "853", "severity": 2, "message": "950", "line": 9, "column": 32, "nodeType": null, "messageId": "855", "endLine": 9, "endColumn": 36}, {"ruleId": "853", "severity": 2, "message": "951", "line": 9, "column": 38, "nodeType": null, "messageId": "855", "endLine": 9, "endColumn": 45}, {"ruleId": "853", "severity": 2, "message": "952", "line": 9, "column": 47, "nodeType": null, "messageId": "855", "endLine": 9, "endColumn": 55}, {"ruleId": "853", "severity": 2, "message": "874", "line": 29, "column": 3, "nodeType": null, "messageId": "855", "endLine": 29, "endColumn": 9}, {"ruleId": "853", "severity": 2, "message": "953", "line": 38, "column": 28, "nodeType": null, "messageId": "855", "endLine": 38, "endColumn": 47}, {"ruleId": "853", "severity": 2, "message": "954", "line": 39, "column": 18, "nodeType": null, "messageId": "855", "endLine": 39, "endColumn": 27}, {"ruleId": "853", "severity": 2, "message": "955", "line": 40, "column": 20, "nodeType": null, "messageId": "855", "endLine": 40, "endColumn": 31}, {"ruleId": "889", "severity": 2, "message": "890", "line": 50, "column": 33, "nodeType": "891", "messageId": "892", "endLine": 50, "endColumn": 36, "suggestions": "956"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 63, "column": 33, "nodeType": "891", "messageId": "892", "endLine": 63, "endColumn": 36, "suggestions": "957"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 185, "column": 22, "nodeType": "891", "messageId": "892", "endLine": 185, "endColumn": 25, "suggestions": "958"}, {"ruleId": "853", "severity": 2, "message": "959", "line": 61, "column": 25, "nodeType": null, "messageId": "855", "endLine": 61, "endColumn": 41}, {"ruleId": "872", "severity": 1, "message": "873", "line": 583, "column": 23, "nodeType": "851", "endLine": 587, "endColumn": 25}, {"ruleId": "853", "severity": 2, "message": "960", "line": 7, "column": 27, "nodeType": null, "messageId": "855", "endLine": 7, "endColumn": 34}, {"ruleId": "853", "severity": 2, "message": "961", "line": 7, "column": 36, "nodeType": null, "messageId": "855", "endLine": 7, "endColumn": 46}, {"ruleId": "860", "severity": 1, "message": "962", "line": 64, "column": 6, "nodeType": "862", "endLine": 64, "endColumn": 14, "suggestions": "963"}, {"ruleId": "872", "severity": 1, "message": "873", "line": 165, "column": 13, "nodeType": "851", "endLine": 169, "endColumn": 15}, {"ruleId": "853", "severity": 2, "message": "960", "line": 6, "column": 27, "nodeType": null, "messageId": "855", "endLine": 6, "endColumn": 34}, {"ruleId": "853", "severity": 2, "message": "961", "line": 6, "column": 36, "nodeType": null, "messageId": "855", "endLine": 6, "endColumn": 46}, {"ruleId": "872", "severity": 1, "message": "873", "line": 160, "column": 13, "nodeType": "851", "endLine": 164, "endColumn": 15}, {"ruleId": "853", "severity": 2, "message": "939", "line": 39, "column": 9, "nodeType": null, "messageId": "855", "endLine": 39, "endColumn": 22}, {"ruleId": "860", "severity": 1, "message": "964", "line": 47, "column": 6, "nodeType": "862", "endLine": 47, "endColumn": 42, "suggestions": "965"}, {"ruleId": "853", "severity": 2, "message": "966", "line": 28, "column": 16, "nodeType": null, "messageId": "855", "endLine": 28, "endColumn": 23}, {"ruleId": "860", "severity": 1, "message": "967", "line": 40, "column": 6, "nodeType": "862", "endLine": 40, "endColumn": 21, "suggestions": "968"}, {"ruleId": "853", "severity": 2, "message": "864", "line": 55, "column": 14, "nodeType": null, "messageId": "855", "endLine": 55, "endColumn": 17}, {"ruleId": "872", "severity": 1, "message": "873", "line": 70, "column": 19, "nodeType": "851", "endLine": 74, "endColumn": 21}, {"ruleId": "872", "severity": 1, "message": "873", "line": 199, "column": 27, "nodeType": "851", "endLine": 203, "endColumn": 29}, {"ruleId": "889", "severity": 2, "message": "890", "line": 22, "column": 12, "nodeType": "891", "messageId": "892", "endLine": 22, "endColumn": 15, "suggestions": "969"}, {"ruleId": "853", "severity": 2, "message": "970", "line": 14, "column": 3, "nodeType": null, "messageId": "855", "endLine": 14, "endColumn": 13}, {"ruleId": "853", "severity": 2, "message": "971", "line": 15, "column": 3, "nodeType": null, "messageId": "855", "endLine": 15, "endColumn": 9}, {"ruleId": "872", "severity": 1, "message": "873", "line": 63, "column": 19, "nodeType": "851", "endLine": 67, "endColumn": 21}, {"ruleId": "872", "severity": 1, "message": "873", "line": 196, "column": 25, "nodeType": "851", "endLine": 200, "endColumn": 27}, {"ruleId": "853", "severity": 2, "message": "939", "line": 40, "column": 9, "nodeType": null, "messageId": "855", "endLine": 40, "endColumn": 22}, {"ruleId": "872", "severity": 1, "message": "873", "line": 180, "column": 7, "nodeType": "851", "endLine": 189, "endColumn": 9}, {"ruleId": "853", "severity": 2, "message": "972", "line": 34, "column": 3, "nodeType": null, "messageId": "855", "endLine": 34, "endColumn": 14}, {"ruleId": "973", "severity": 1, "message": "974", "line": 49, "column": 9, "nodeType": "851", "endLine": 53, "endColumn": 11}, {"ruleId": "973", "severity": 1, "message": "974", "line": 59, "column": 5, "nodeType": "851", "endLine": 63, "endColumn": 7}, {"ruleId": "860", "severity": 1, "message": "975", "line": 176, "column": 6, "nodeType": "862", "endLine": 176, "endColumn": 15, "suggestions": "976"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 279, "column": 16, "nodeType": "891", "messageId": "892", "endLine": 279, "endColumn": 19, "suggestions": "977"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 6, "column": 34, "nodeType": "891", "messageId": "892", "endLine": 6, "endColumn": 37, "suggestions": "978"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 292, "column": 51, "nodeType": "891", "messageId": "892", "endLine": 292, "endColumn": 54, "suggestions": "979"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 293, "column": 25, "nodeType": "891", "messageId": "892", "endLine": 293, "endColumn": 28, "suggestions": "980"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 298, "column": 27, "nodeType": "891", "messageId": "892", "endLine": 298, "endColumn": 30, "suggestions": "981"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 299, "column": 25, "nodeType": "891", "messageId": "892", "endLine": 299, "endColumn": 28, "suggestions": "982"}, {"ruleId": "853", "severity": 2, "message": "983", "line": 7, "column": 10, "nodeType": null, "messageId": "855", "endLine": 7, "endColumn": 24}, {"ruleId": "889", "severity": 2, "message": "890", "line": 75, "column": 60, "nodeType": "891", "messageId": "892", "endLine": 75, "endColumn": 63, "suggestions": "984"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 155, "column": 31, "nodeType": "891", "messageId": "892", "endLine": 155, "endColumn": 34, "suggestions": "985"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 161, "column": 26, "nodeType": "891", "messageId": "892", "endLine": 161, "endColumn": 29, "suggestions": "986"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 162, "column": 26, "nodeType": "891", "messageId": "892", "endLine": 162, "endColumn": 29, "suggestions": "987"}, {"ruleId": "889", "severity": 2, "message": "890", "line": 85, "column": 35, "nodeType": "891", "messageId": "892", "endLine": 85, "endColumn": 38, "suggestions": "988"}, {"ruleId": "853", "severity": 2, "message": "989", "line": 1, "column": 8, "nodeType": null, "messageId": "855", "endLine": 1, "endColumn": 16}, "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "Do not use an `<a>` element to navigate to `/contact/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "@typescript-eslint/no-unused-vars", "'Users' is defined but never used.", "unusedVar", "'Heart' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'pathname' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchStats'. Either include it or remove the dependency array.", "ArrayExpression", ["990"], "'err' is defined but never used.", "'formatDate' is assigned a value but never used.", "'getActivityIcon' is assigned a value but never used.", "'getActivityBgColor' is assigned a value but never used.", "'Eye' is defined but never used.", "'tProfile' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["991"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "'locale' is defined but never used.", "'Tool' is defined but never used.", "'ArrowLeft' is defined but never used.", "'isValidCategory' is defined but never used.", "'getLocale' is defined but never used.", "'error' is defined but never used.", "'MapPin' is defined but never used.", "Do not use an `<a>` element to navigate to `/faq/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "React Hook useEffect has a missing dependency: 'createPaymentIntent'. Either include it or remove the dependency array.", ["992"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["993", "994", "995", "996"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["997", "998"], "'User' is defined but never used.", "'Edit' is defined but never used.", ["999", "1000"], "'Mail' is defined but never used.", "'useLocale' is defined but never used.", "'minFreeDate' is defined but never used.", "'minPaidDate' is defined but never used.", "'orderId' is defined but never used.", "Do not use an `<a>` element to navigate to `/profile/submitted/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "'Layout' is defined but never used.", ["1001", "1002", "1003", "1004"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1005", "1006", "1007", "1008"], ["1009", "1010", "1011", "1012"], ["1013", "1014", "1015", "1016"], ["1017", "1018", "1019", "1020"], ["1021", "1022", "1023", "1024"], ["1025", "1026", "1027", "1028"], ["1029", "1030", "1031", "1032"], ["1033", "1034", "1035", "1036"], ["1037", "1038", "1039", "1040"], ["1041", "1042", "1043", "1044"], ["1045", "1046", "1047", "1048"], ["1049", "1050"], ["1051", "1052"], ["1053", "1054"], ["1055", "1056"], "'request' is defined but never used.", ["1057", "1058"], "'paymentMethod' is assigned a value but never used.", "'now' is assigned a value but never used.", ["1059", "1060"], ["1061", "1062"], ["1063", "1064"], ["1065", "1066"], ["1067", "1068"], ["1069", "1070"], ["1071", "1072"], ["1073", "1074"], ["1075", "1076"], ["1077", "1078"], ["1079", "1080"], ["1081", "1082"], ["1083", "1084"], ["1085", "1086"], "'currentLocale' is assigned a value but never used.", "'getTranslations' is defined but never used.", "'locale' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'filterTools'. Either include it or remove the dependency array.", ["1087"], "'RefreshCw' is defined but never used.", "'initialStats' is defined but never used.", "'setTools' is assigned a value but never used.", "'handleReapply' is assigned a value but never used.", "'Filter' is defined but never used.", "'Grid' is defined but never used.", "'List' is defined but never used.", "'SortAsc' is defined but never used.", "'SortDesc' is defined but never used.", "'setSelectedCategory' is assigned a value but never used.", "'setSortBy' is assigned a value but never used.", "'setViewMode' is assigned a value but never used.", ["1088", "1089"], ["1090", "1091"], ["1092", "1093"], "'setUploadingLogo' is assigned a value but never used.", "'FaHeart' is defined but never used.", "'FaRegHeart' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1094"], "React Hook useEffect has a missing dependency: 'initializeToolState'. Either include it or remove the dependency array.", ["1095"], "'setTool' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchRelatedTools'. Either include it or remove the dependency array.", ["1096"], ["1097", "1098"], "'DollarSign' is defined but never used.", "'Share2' is defined but never used.", "'fallbackSrc' is assigned a value but never used.", "jsx-a11y/alt-text", "Image elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "React Hook useEffect has missing dependencies: 'refreshToolState' and 'toolStates'. Either include them or remove the dependency array.", ["1099"], ["1100", "1101"], ["1102", "1103"], ["1104", "1105"], ["1106", "1107"], ["1108", "1109"], ["1110", "1111"], "'getNextAuthUrl' is defined but never used.", ["1112", "1113"], ["1114", "1115"], ["1116", "1117"], ["1118", "1119"], ["1120", "1121"], "'mongoose' is defined but never used.", {"desc": "1122", "fix": "1123"}, {"desc": "1124", "fix": "1125"}, {"desc": "1126", "fix": "1127"}, {"messageId": "1128", "data": "1129", "fix": "1130", "desc": "1131"}, {"messageId": "1128", "data": "1132", "fix": "1133", "desc": "1134"}, {"messageId": "1128", "data": "1135", "fix": "1136", "desc": "1137"}, {"messageId": "1128", "data": "1138", "fix": "1139", "desc": "1140"}, {"messageId": "1141", "fix": "1142", "desc": "1143"}, {"messageId": "1144", "fix": "1145", "desc": "1146"}, {"messageId": "1141", "fix": "1147", "desc": "1143"}, {"messageId": "1144", "fix": "1148", "desc": "1146"}, {"messageId": "1128", "data": "1149", "fix": "1150", "desc": "1131"}, {"messageId": "1128", "data": "1151", "fix": "1152", "desc": "1134"}, {"messageId": "1128", "data": "1153", "fix": "1154", "desc": "1137"}, {"messageId": "1128", "data": "1155", "fix": "1156", "desc": "1140"}, {"messageId": "1128", "data": "1157", "fix": "1158", "desc": "1159"}, {"messageId": "1128", "data": "1160", "fix": "1161", "desc": "1162"}, {"messageId": "1128", "data": "1163", "fix": "1164", "desc": "1165"}, {"messageId": "1128", "data": "1166", "fix": "1167", "desc": "1168"}, {"messageId": "1128", "data": "1169", "fix": "1170", "desc": "1159"}, {"messageId": "1128", "data": "1171", "fix": "1172", "desc": "1162"}, {"messageId": "1128", "data": "1173", "fix": "1174", "desc": "1165"}, {"messageId": "1128", "data": "1175", "fix": "1176", "desc": "1168"}, {"messageId": "1128", "data": "1177", "fix": "1178", "desc": "1159"}, {"messageId": "1128", "data": "1179", "fix": "1180", "desc": "1162"}, {"messageId": "1128", "data": "1181", "fix": "1182", "desc": "1165"}, {"messageId": "1128", "data": "1183", "fix": "1184", "desc": "1168"}, {"messageId": "1128", "data": "1185", "fix": "1186", "desc": "1159"}, {"messageId": "1128", "data": "1187", "fix": "1188", "desc": "1162"}, {"messageId": "1128", "data": "1189", "fix": "1190", "desc": "1165"}, {"messageId": "1128", "data": "1191", "fix": "1192", "desc": "1168"}, {"messageId": "1128", "data": "1193", "fix": "1194", "desc": "1131"}, {"messageId": "1128", "data": "1195", "fix": "1196", "desc": "1134"}, {"messageId": "1128", "data": "1197", "fix": "1198", "desc": "1137"}, {"messageId": "1128", "data": "1199", "fix": "1200", "desc": "1140"}, {"messageId": "1128", "data": "1201", "fix": "1202", "desc": "1131"}, {"messageId": "1128", "data": "1203", "fix": "1204", "desc": "1134"}, {"messageId": "1128", "data": "1205", "fix": "1206", "desc": "1137"}, {"messageId": "1128", "data": "1207", "fix": "1208", "desc": "1140"}, {"messageId": "1128", "data": "1209", "fix": "1210", "desc": "1131"}, {"messageId": "1128", "data": "1211", "fix": "1212", "desc": "1134"}, {"messageId": "1128", "data": "1213", "fix": "1214", "desc": "1137"}, {"messageId": "1128", "data": "1215", "fix": "1216", "desc": "1140"}, {"messageId": "1128", "data": "1217", "fix": "1218", "desc": "1159"}, {"messageId": "1128", "data": "1219", "fix": "1220", "desc": "1162"}, {"messageId": "1128", "data": "1221", "fix": "1222", "desc": "1165"}, {"messageId": "1128", "data": "1223", "fix": "1224", "desc": "1168"}, {"messageId": "1128", "data": "1225", "fix": "1226", "desc": "1159"}, {"messageId": "1128", "data": "1227", "fix": "1228", "desc": "1162"}, {"messageId": "1128", "data": "1229", "fix": "1230", "desc": "1165"}, {"messageId": "1128", "data": "1231", "fix": "1232", "desc": "1168"}, {"messageId": "1128", "data": "1233", "fix": "1234", "desc": "1131"}, {"messageId": "1128", "data": "1235", "fix": "1236", "desc": "1134"}, {"messageId": "1128", "data": "1237", "fix": "1238", "desc": "1137"}, {"messageId": "1128", "data": "1239", "fix": "1240", "desc": "1140"}, {"messageId": "1128", "data": "1241", "fix": "1242", "desc": "1131"}, {"messageId": "1128", "data": "1243", "fix": "1244", "desc": "1134"}, {"messageId": "1128", "data": "1245", "fix": "1246", "desc": "1137"}, {"messageId": "1128", "data": "1247", "fix": "1248", "desc": "1140"}, {"messageId": "1141", "fix": "1249", "desc": "1143"}, {"messageId": "1144", "fix": "1250", "desc": "1146"}, {"messageId": "1141", "fix": "1251", "desc": "1143"}, {"messageId": "1144", "fix": "1252", "desc": "1146"}, {"messageId": "1141", "fix": "1253", "desc": "1143"}, {"messageId": "1144", "fix": "1254", "desc": "1146"}, {"messageId": "1141", "fix": "1255", "desc": "1143"}, {"messageId": "1144", "fix": "1256", "desc": "1146"}, {"messageId": "1141", "fix": "1257", "desc": "1143"}, {"messageId": "1144", "fix": "1258", "desc": "1146"}, {"messageId": "1141", "fix": "1259", "desc": "1143"}, {"messageId": "1144", "fix": "1260", "desc": "1146"}, {"messageId": "1141", "fix": "1261", "desc": "1143"}, {"messageId": "1144", "fix": "1262", "desc": "1146"}, {"messageId": "1141", "fix": "1263", "desc": "1143"}, {"messageId": "1144", "fix": "1264", "desc": "1146"}, {"messageId": "1141", "fix": "1265", "desc": "1143"}, {"messageId": "1144", "fix": "1266", "desc": "1146"}, {"messageId": "1141", "fix": "1267", "desc": "1143"}, {"messageId": "1144", "fix": "1268", "desc": "1146"}, {"messageId": "1141", "fix": "1269", "desc": "1143"}, {"messageId": "1144", "fix": "1270", "desc": "1146"}, {"messageId": "1141", "fix": "1271", "desc": "1143"}, {"messageId": "1144", "fix": "1272", "desc": "1146"}, {"messageId": "1141", "fix": "1273", "desc": "1143"}, {"messageId": "1144", "fix": "1274", "desc": "1146"}, {"messageId": "1141", "fix": "1275", "desc": "1143"}, {"messageId": "1144", "fix": "1276", "desc": "1146"}, {"messageId": "1141", "fix": "1277", "desc": "1143"}, {"messageId": "1144", "fix": "1278", "desc": "1146"}, {"messageId": "1141", "fix": "1279", "desc": "1143"}, {"messageId": "1144", "fix": "1280", "desc": "1146"}, {"messageId": "1141", "fix": "1281", "desc": "1143"}, {"messageId": "1144", "fix": "1282", "desc": "1146"}, {"messageId": "1141", "fix": "1283", "desc": "1143"}, {"messageId": "1144", "fix": "1284", "desc": "1146"}, {"messageId": "1141", "fix": "1285", "desc": "1143"}, {"messageId": "1144", "fix": "1286", "desc": "1146"}, {"desc": "1287", "fix": "1288"}, {"messageId": "1141", "fix": "1289", "desc": "1143"}, {"messageId": "1144", "fix": "1290", "desc": "1146"}, {"messageId": "1141", "fix": "1291", "desc": "1143"}, {"messageId": "1144", "fix": "1292", "desc": "1146"}, {"messageId": "1141", "fix": "1293", "desc": "1143"}, {"messageId": "1144", "fix": "1294", "desc": "1146"}, {"desc": "1295", "fix": "1296"}, {"desc": "1297", "fix": "1298"}, {"desc": "1299", "fix": "1300"}, {"messageId": "1141", "fix": "1301", "desc": "1143"}, {"messageId": "1144", "fix": "1302", "desc": "1146"}, {"desc": "1303", "fix": "1304"}, {"messageId": "1141", "fix": "1305", "desc": "1143"}, {"messageId": "1144", "fix": "1306", "desc": "1146"}, {"messageId": "1141", "fix": "1307", "desc": "1143"}, {"messageId": "1144", "fix": "1308", "desc": "1146"}, {"messageId": "1141", "fix": "1309", "desc": "1143"}, {"messageId": "1144", "fix": "1310", "desc": "1146"}, {"messageId": "1141", "fix": "1311", "desc": "1143"}, {"messageId": "1144", "fix": "1312", "desc": "1146"}, {"messageId": "1141", "fix": "1313", "desc": "1143"}, {"messageId": "1144", "fix": "1314", "desc": "1146"}, {"messageId": "1141", "fix": "1315", "desc": "1143"}, {"messageId": "1144", "fix": "1316", "desc": "1146"}, {"messageId": "1141", "fix": "1317", "desc": "1143"}, {"messageId": "1144", "fix": "1318", "desc": "1146"}, {"messageId": "1141", "fix": "1319", "desc": "1143"}, {"messageId": "1144", "fix": "1320", "desc": "1146"}, {"messageId": "1141", "fix": "1321", "desc": "1143"}, {"messageId": "1144", "fix": "1322", "desc": "1146"}, {"messageId": "1141", "fix": "1323", "desc": "1143"}, {"messageId": "1144", "fix": "1324", "desc": "1146"}, {"messageId": "1141", "fix": "1325", "desc": "1143"}, {"messageId": "1144", "fix": "1326", "desc": "1146"}, "Update the dependencies array to be: [fetchStats, timeRange]", {"range": "1327", "text": "1328"}, "Update the dependencies array to be: [fetchTools, statusFilter]", {"range": "1329", "text": "1330"}, "Update the dependencies array to be: [createPaymentIntent, orderId]", {"range": "1331", "text": "1332"}, "replaceWithAlt", {"alt": "1333"}, {"range": "1334", "text": "1335"}, "Replace with `&apos;`.", {"alt": "1336"}, {"range": "1337", "text": "1338"}, "Replace with `&lsquo;`.", {"alt": "1339"}, {"range": "1340", "text": "1341"}, "Replace with `&#39;`.", {"alt": "1342"}, {"range": "1343", "text": "1344"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "1345", "text": "1346"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1347", "text": "1348"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1349", "text": "1346"}, {"range": "1350", "text": "1348"}, {"alt": "1333"}, {"range": "1351", "text": "1352"}, {"alt": "1336"}, {"range": "1353", "text": "1354"}, {"alt": "1339"}, {"range": "1355", "text": "1356"}, {"alt": "1342"}, {"range": "1357", "text": "1358"}, {"alt": "1359"}, {"range": "1360", "text": "1361"}, "Replace with `&quot;`.", {"alt": "1362"}, {"range": "1363", "text": "1364"}, "Replace with `&ldquo;`.", {"alt": "1365"}, {"range": "1366", "text": "1367"}, "Replace with `&#34;`.", {"alt": "1368"}, {"range": "1369", "text": "1370"}, "Replace with `&rdquo;`.", {"alt": "1359"}, {"range": "1371", "text": "1372"}, {"alt": "1362"}, {"range": "1373", "text": "1374"}, {"alt": "1365"}, {"range": "1375", "text": "1376"}, {"alt": "1368"}, {"range": "1377", "text": "1378"}, {"alt": "1359"}, {"range": "1379", "text": "1380"}, {"alt": "1362"}, {"range": "1381", "text": "1382"}, {"alt": "1365"}, {"range": "1383", "text": "1384"}, {"alt": "1368"}, {"range": "1385", "text": "1386"}, {"alt": "1359"}, {"range": "1387", "text": "1388"}, {"alt": "1362"}, {"range": "1389", "text": "1390"}, {"alt": "1365"}, {"range": "1391", "text": "1392"}, {"alt": "1368"}, {"range": "1393", "text": "1394"}, {"alt": "1333"}, {"range": "1395", "text": "1396"}, {"alt": "1336"}, {"range": "1397", "text": "1398"}, {"alt": "1339"}, {"range": "1399", "text": "1400"}, {"alt": "1342"}, {"range": "1401", "text": "1402"}, {"alt": "1333"}, {"range": "1403", "text": "1404"}, {"alt": "1336"}, {"range": "1405", "text": "1406"}, {"alt": "1339"}, {"range": "1407", "text": "1408"}, {"alt": "1342"}, {"range": "1409", "text": "1410"}, {"alt": "1333"}, {"range": "1411", "text": "1412"}, {"alt": "1336"}, {"range": "1413", "text": "1414"}, {"alt": "1339"}, {"range": "1415", "text": "1416"}, {"alt": "1342"}, {"range": "1417", "text": "1418"}, {"alt": "1359"}, {"range": "1419", "text": "1420"}, {"alt": "1362"}, {"range": "1421", "text": "1422"}, {"alt": "1365"}, {"range": "1423", "text": "1424"}, {"alt": "1368"}, {"range": "1425", "text": "1426"}, {"alt": "1359"}, {"range": "1427", "text": "1428"}, {"alt": "1362"}, {"range": "1429", "text": "1430"}, {"alt": "1365"}, {"range": "1431", "text": "1432"}, {"alt": "1368"}, {"range": "1433", "text": "1434"}, {"alt": "1333"}, {"range": "1435", "text": "1436"}, {"alt": "1336"}, {"range": "1437", "text": "1438"}, {"alt": "1339"}, {"range": "1439", "text": "1440"}, {"alt": "1342"}, {"range": "1441", "text": "1442"}, {"alt": "1333"}, {"range": "1443", "text": "1444"}, {"alt": "1336"}, {"range": "1445", "text": "1446"}, {"alt": "1339"}, {"range": "1447", "text": "1448"}, {"alt": "1342"}, {"range": "1449", "text": "1450"}, {"range": "1451", "text": "1346"}, {"range": "1452", "text": "1348"}, {"range": "1453", "text": "1346"}, {"range": "1454", "text": "1348"}, {"range": "1455", "text": "1346"}, {"range": "1456", "text": "1348"}, {"range": "1457", "text": "1346"}, {"range": "1458", "text": "1348"}, {"range": "1459", "text": "1346"}, {"range": "1460", "text": "1348"}, {"range": "1461", "text": "1346"}, {"range": "1462", "text": "1348"}, {"range": "1463", "text": "1346"}, {"range": "1464", "text": "1348"}, {"range": "1465", "text": "1346"}, {"range": "1466", "text": "1348"}, {"range": "1467", "text": "1346"}, {"range": "1468", "text": "1348"}, {"range": "1469", "text": "1346"}, {"range": "1470", "text": "1348"}, {"range": "1471", "text": "1346"}, {"range": "1472", "text": "1348"}, {"range": "1473", "text": "1346"}, {"range": "1474", "text": "1348"}, {"range": "1475", "text": "1346"}, {"range": "1476", "text": "1348"}, {"range": "1477", "text": "1346"}, {"range": "1478", "text": "1348"}, {"range": "1479", "text": "1346"}, {"range": "1480", "text": "1348"}, {"range": "1481", "text": "1346"}, {"range": "1482", "text": "1348"}, {"range": "1483", "text": "1346"}, {"range": "1484", "text": "1348"}, {"range": "1485", "text": "1346"}, {"range": "1486", "text": "1348"}, {"range": "1487", "text": "1346"}, {"range": "1488", "text": "1348"}, "Update the dependencies array to be: [filterTools, likedTools, searchQuery, selectedCategory]", {"range": "1489", "text": "1490"}, {"range": "1491", "text": "1346"}, {"range": "1492", "text": "1348"}, {"range": "1493", "text": "1346"}, {"range": "1494", "text": "1348"}, {"range": "1495", "text": "1346"}, {"range": "1496", "text": "1348"}, "Update the dependencies array to be: [fetchComments, toolId]", {"range": "1497", "text": "1498"}, "Update the dependencies array to be: [toolId, initialLikes, initialLiked, initializeToolState]", {"range": "1499", "text": "1500"}, "Update the dependencies array to be: [fetchRelatedTools, tool.category]", {"range": "1501", "text": "1502"}, {"range": "1503", "text": "1346"}, {"range": "1504", "text": "1348"}, "Update the dependencies array to be: [refreshToolState, session, toolStates]", {"range": "1505", "text": "1506"}, {"range": "1507", "text": "1346"}, {"range": "1508", "text": "1348"}, {"range": "1509", "text": "1346"}, {"range": "1510", "text": "1348"}, {"range": "1511", "text": "1346"}, {"range": "1512", "text": "1348"}, {"range": "1513", "text": "1346"}, {"range": "1514", "text": "1348"}, {"range": "1515", "text": "1346"}, {"range": "1516", "text": "1348"}, {"range": "1517", "text": "1346"}, {"range": "1518", "text": "1348"}, {"range": "1519", "text": "1346"}, {"range": "1520", "text": "1348"}, {"range": "1521", "text": "1346"}, {"range": "1522", "text": "1348"}, {"range": "1523", "text": "1346"}, {"range": "1524", "text": "1348"}, {"range": "1525", "text": "1346"}, {"range": "1526", "text": "1348"}, {"range": "1527", "text": "1346"}, {"range": "1528", "text": "1348"}, [950, 961], "[fetchStats, timeRange]", [1532, 1546], "[fetchTools, statusFilter]", [1364, 1373], "[createPaymentIntent, orderId]", "&apos;", [7329, 7375], "To protect our or others&apos; legitimate interests", "&lsquo;", [7329, 7375], "To protect our or others&lsquo; legitimate interests", "&#39;", [7329, 7375], "To protect our or others&#39; legitimate interests", "&rsquo;", [7329, 7375], "To protect our or others&rsquo; legitimate interests", [1559, 1562], "unknown", [1559, 1562], "never", [1934, 1937], [1934, 1937], [4713, 4755], "Follow usage rules, respect others&apos; rights", [4713, 4755], "Follow usage rules, respect others&lsquo; rights", [4713, 4755], "Follow usage rules, respect others&#39; rights", [4713, 4755], "Follow usage rules, respect others&rsquo; rights", "&quot;", [5303, 5485], "AI Tools Navigator (hereinafter referred to as &quot;we\" or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", "&ldquo;", [5303, 5485], "AI Tools Navigator (hereinafter referred to as &ldquo;we\" or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", "&#34;", [5303, 5485], "AI Tools Navigator (hereinafter referred to as &#34;we\" or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", "&rdquo;", [5303, 5485], "AI Tools Navigator (hereinafter referred to as &rdquo;we\" or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we&quot; or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we&ldquo; or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we&#34; or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we&rdquo; or \"this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or &quot;this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or &ldquo;this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or &#34;this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or &rdquo;this platform\") is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or \"this platform&quot;) is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or \"this platform&ldquo;) is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or \"this platform&#34;) is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [5303, 5485], "AI Tools Navigator (hereinafter referred to as \"we\" or \"this platform&rdquo;) is a professional artificial intelligence tool discovery and recommendation platform that provides users with:", [7261, 7318], "Infringe on others&apos; intellectual property or other rights", [7261, 7318], "Infringe on others&lsquo; intellectual property or other rights", [7261, 7318], "Infringe on others&#39; intellectual property or other rights", [7261, 7318], "Infringe on others&rsquo; intellectual property or other rights", [8560, 8655], "The platform&apos;s design, code, trademarks, etc. are all protected by intellectual property rights", [8560, 8655], "The platform&lsquo;s design, code, trademarks, etc. are all protected by intellectual property rights", [8560, 8655], "The platform&#39;s design, code, trademarks, etc. are all protected by intellectual property rights", [8560, 8655], "The platform&rsquo;s design, code, trademarks, etc. are all protected by intellectual property rights", [8769, 8848], "We respect others&apos; intellectual property rights and expect users to do the same", [8769, 8848], "We respect others&lsquo; intellectual property rights and expect users to do the same", [8769, 8848], "We respect others&#39; intellectual property rights and expect users to do the same", [8769, 8848], "We respect others&rsquo; intellectual property rights and expect users to do the same", [10037, 10112], "Our services are provided &quot;as is\" without any express or implied warranties", [10037, 10112], "Our services are provided &ldquo;as is\" without any express or implied warranties", [10037, 10112], "Our services are provided &#34;as is\" without any express or implied warranties", [10037, 10112], "Our services are provided &rdquo;as is\" without any express or implied warranties", [10037, 10112], "Our services are provided \"as is&quot; without any express or implied warranties", [10037, 10112], "Our services are provided \"as is&ldquo; without any express or implied warranties", [10037, 10112], "Our services are provided \"as is&#34; without any express or implied warranties", [10037, 10112], "Our services are provided \"as is&rdquo; without any express or implied warranties", [11567, 11649], "These Terms of Service are governed by the laws of the People&apos;s Republic of China.", [11567, 11649], "These Terms of Service are governed by the laws of the People&lsquo;s Republic of China.", [11567, 11649], "These Terms of Service are governed by the laws of the People&#39;s Republic of China.", [11567, 11649], "These Terms of Service are governed by the laws of the People&rsquo;s Republic of China.", [11861, 11951], "If consultation fails, litigation may be brought to the People&apos;s Court in our jurisdiction", [11861, 11951], "If consultation fails, litigation may be brought to the People&lsquo;s Court in our jurisdiction", [11861, 11951], "If consultation fails, litigation may be brought to the People&#39;s Court in our jurisdiction", [11861, 11951], "If consultation fails, litigation may be brought to the People&rsquo;s Court in our jurisdiction", [1763, 1766], [1763, 1766], [1818, 1821], [1818, 1821], [746, 749], [746, 749], [1625, 1628], [1625, 1628], [2591, 2594], [2591, 2594], [4807, 4810], [4807, 4810], [5136, 5139], [5136, 5139], [5980, 5983], [5980, 5983], [6670, 6673], [6670, 6673], [6761, 6764], [6761, 6764], [6784, 6787], [6784, 6787], [876, 879], [876, 879], [2023, 2026], [2023, 2026], [4607, 4610], [4607, 4610], [4829, 4832], [4829, 4832], [1364, 1367], [1364, 1367], [1635, 1638], [1635, 1638], [2622, 2625], [2622, 2625], [2710, 2713], [2710, 2713], [1226, 1269], "[filterTools, likedTools, searchQuery, selectedCategory]", [1561, 1564], [1561, 1564], [2024, 2027], [2024, 2027], [4765, 4768], [4765, 4768], [1768, 1776], "[fetchComments, toolId]", [1272, 1308], "[toolId, initialLikes, initialLiked, initializeToolState]", [1249, 1264], "[fetchRelatedTools, tool.category]", [646, 649], [646, 649], [4405, 4414], "[refreshToolState, session, toolStates]", [8390, 8393], [8390, 8393], [125, 128], [125, 128], [6911, 6914], [6911, 6914], [6943, 6946], [6943, 6946], [7073, 7076], [7073, 7076], [7105, 7108], [7105, 7108], [2315, 2318], [2315, 2318], [4647, 4650], [4647, 4650], [4803, 4806], [4803, 4806], [4862, 4865], [4862, 4865], [1724, 1727], [1724, 1727]]